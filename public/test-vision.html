<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouzeAfrika Vision API Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .product { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        #results { margin-top: 20px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .loading { display: none; color: #666; }
        input[type="file"] { margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🤖 YouzeAfrika Vision API Test</h1>
    <p>Test the Gemini vision API's ability to recognize products from images.</p>

    <div class="test-section">
        <h3>📸 Upload Image Test</h3>
        <input type="file" id="imageInput" accept="image/*">
        <button onclick="testImageUpload()">Analyze Image</button>
        <div id="uploadLoading" class="loading">Analyzing image...</div>
        <div id="uploadResults"></div>
    </div>

    <div class="test-section">
        <h3>📁 Sample Images Test</h3>
        <button onclick="testSampleImages()">Test Sample Images</button>
        <div id="sampleLoading" class="loading">Processing sample images...</div>
        <div id="sampleResults"></div>
    </div>

    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:8001/api';

        async function testImageUpload() {
            const fileInput = document.getElementById('imageInput');
            const file = fileInput.files[0];

            if (!file) {
                alert('Please select an image first');
                return;
            }

            const loadingDiv = document.getElementById('uploadLoading');
            const resultsDiv = document.getElementById('uploadResults');

            loadingDiv.style.display = 'block';
            resultsDiv.innerHTML = '';

            const formData = new FormData();
            formData.append('image', file);

            try {
                const response = await fetch(`${API_BASE}/test/vision/upload`, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                loadingDiv.style.display = 'none';

                if (data.success) {
                    displayVisionResults(resultsDiv, data.vision_result, 'Uploaded Image Analysis');
                } else {
                    resultsDiv.innerHTML = `<div class="error">Error: ${data.error}</div>`;
                }
            } catch (error) {
                loadingDiv.style.display = 'none';
                resultsDiv.innerHTML = `<div class="error">Network error: ${error.message}</div>`;
            }
        }

        async function testSampleImages() {
            const loadingDiv = document.getElementById('sampleLoading');
            const resultsDiv = document.getElementById('sampleResults');

            loadingDiv.style.display = 'block';
            resultsDiv.innerHTML = '';

            try {
                const response = await fetch(`${API_BASE}/test/vision/samples`);
                const data = await response.json();
                loadingDiv.style.display = 'none';

                if (data.success) {
                    let html = '<h4>Sample Images Results:</h4>';

                    for (const [filename, result] of Object.entries(data.sample_results)) {
                        html += `<div class="product">
                            <h5>📷 ${filename}</h5>
                            <p><strong>Success:</strong> <span class="${result.success ? 'success' : 'error'}">${result.success}</span></p>
                            <p><strong>Products Found:</strong> ${result.products_found}</p>`;

                        if (result.products && result.products.length > 0) {
                            html += '<div style="margin-left: 20px;">';
                            result.products.forEach((product, index) => {
                                html += `<div style="background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 3px;">
                                    <strong>Product ${index + 1}:</strong> ${product.name || 'Unknown'}<br>
                                    <strong>Brand:</strong> ${product.brand || 'Unknown'}<br>
                                    <strong>Price:</strong> ${product.price_zar ? 'R' + product.price_zar : 'Not found'}<br>
                                    <strong>Outlet:</strong> ${product.outlet || 'Unknown'}<br>
                                    <strong>Category:</strong> ${product.category || 'Unknown'}<br>
                                    <strong>Confidence:</strong> ${product.confidence || 0}%
                                </div>`;
                            });
                            html += '</div>';
                        }

                        html += '</div>';
                    }

                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `<div class="error">Error: ${data.error || 'Unknown error'}</div>`;
                }
            } catch (error) {
                loadingDiv.style.display = 'none';
                resultsDiv.innerHTML = `<div class="error">Network error: ${error.message}</div>`;
            }
        }

        function displayVisionResults(container, visionResult, title) {
            let html = `<h4>${title}</h4>`;

            if (visionResult.success) {
                html += `<div class="success">✅ Vision analysis successful!</div>`;
                html += `<p><strong>Products found:</strong> ${visionResult.products ? visionResult.products.length : 0}</p>`;

                if (visionResult.products && visionResult.products.length > 0) {
                    visionResult.products.forEach((product, index) => {
                        html += `<div class="product">
                            <h5>Product ${index + 1}</h5>
                            <p><strong>Name:</strong> ${product.name || 'Unknown'}</p>
                            <p><strong>Brand:</strong> ${product.brand || 'Unknown'}</p>
                            <p><strong>Model:</strong> ${product.model || 'Unknown'}</p>
                            <p><strong>Price:</strong> ${product.price_zar ? 'R' + product.price_zar : 'Not detected'}</p>
                            <p><strong>Outlet:</strong> ${product.outlet || 'Unknown'}</p>
                            <p><strong>Category:</strong> ${product.category || 'Unknown'}</p>
                            <p><strong>Confidence:</strong> ${product.confidence || 0}%</p>
                        </div>`;
                    });
                } else {
                    html += '<p>No products detected in the image.</p>';
                }
            } else {
                html += `<div class="error">❌ Vision analysis failed: ${visionResult.error}</div>`;
            }

            container.innerHTML = html;
        }
    </script>
</body>
</html>