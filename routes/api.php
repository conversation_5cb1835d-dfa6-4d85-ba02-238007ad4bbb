<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ShopController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\PricingController;
use App\Http\Controllers\Api\CartController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\ChatController;
use App\Http\Controllers\Api\CatalogueController;
use App\Http\Controllers\WhatsAppController;
use App\Http\Controllers\WhatsAppWebhookController;
use App\Http\Controllers\SimpleBotController;
use App\Http\Controllers\Api\ChatOrderController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Primary Twilio WhatsApp webhook
Route::get('/whatsapp/webhook', [SimpleBotController::class, 'verify']);
Route::post('/whatsapp/webhook', [SimpleBotController::class, 'handle']);

// Backup webhooks (kept for compatibility)
Route::get('/whatsapp/cloud-webhook', [WhatsAppWebhookController::class, 'verify']);
Route::post('/whatsapp/cloud-webhook', [WhatsAppWebhookController::class, 'handle']);

// Legacy webhooks (kept for compatibility)
Route::post('/whatsapp/legacy-webhook', [WhatsAppController::class, 'webhook']);
Route::post('/whatsapp/ai-webhook', [WhatsAppController::class, 'webhookAI']);

// Guide-specified endpoints
Route::post('/webhooks/whatsapp', [SimpleBotController::class, 'handle']);
Route::post('/bot/send', [WhatsAppController::class, 'sendMessage']);
Route::post('/bot/price/quote', [App\Http\Controllers\BotController::class, 'quote']);
Route::post('/bot/cart/add', [App\Http\Controllers\BotController::class, 'addToCart']);
Route::post('/bot/order/confirm', [App\Http\Controllers\BotController::class, 'confirmOrder']);

// Escalation management endpoints
Route::get('/whatsapp/escalations', [WhatsAppController::class, 'getEscalationQueue']);
Route::post('/whatsapp/escalations/{conversationId}/resolve', [WhatsAppController::class, 'resolveEscalation']);

// Chat Order WhatsApp webhook
Route::post('/chat-orders/webhook', [ChatOrderController::class, 'webhook']);

// Simple Bot Test endpoints
Route::get('/test/simple-bot/flow', [App\Http\Controllers\SimpleBotTestController::class, 'testFlow']);
Route::get('/test/simple-bot/conversation', [App\Http\Controllers\SimpleBotTestController::class, 'simulateConversation']);
Route::post('/test/simple-bot/reset', [App\Http\Controllers\SimpleBotTestController::class, 'resetTestData']);

// Vision API Test endpoints
Route::post('/test/vision/upload', [App\Http\Controllers\TestVisionController::class, 'testImageUpload']);
Route::get('/test/vision/samples', [App\Http\Controllers\TestVisionController::class, 'testSampleImages']);
Route::post('/test/vision/whatsapp-flow', [App\Http\Controllers\TestVisionController::class, 'simulateWhatsAppFlow']);

// Twilio Test endpoints
Route::get('/test/twilio/config', [App\Http\Controllers\TwilioTestController::class, 'testConfig']);
Route::post('/test/twilio/send', [App\Http\Controllers\TwilioTestController::class, 'testSendMessage']);
Route::post('/test/twilio/webhook', [App\Http\Controllers\TwilioTestController::class, 'simulateWebhook']);
Route::post('/test/twilio/flow', [App\Http\Controllers\TwilioTestController::class, 'testCompleteFlow']);



Route::prefix('v1')->group(function () {
    // Shops
    Route::apiResource('shops', ShopController::class)->only(['index', 'show']);
    
    // Products
    Route::get('products', [ProductController::class, 'index']);
    Route::get('products/{product}', [ProductController::class, 'show']);
    
    // Pricing
    Route::post('pricing/quote', [PricingController::class, 'quote']);
    
    // Exchange rates
    Route::get('exchange-rates/current', [PricingController::class, 'currentRate']);
    
    // Chat/AI endpoints
    Route::prefix('chat')->group(function () {
        Route::post('start', [ChatController::class, 'startConversation']);
        Route::post('message', [ChatController::class, 'sendMessage']);
        Route::post('analyze-image', [ChatController::class, 'analyzeImage']);
        Route::get('messages/{conversationId}', [ChatController::class, 'getMessages']);
        Route::post('generate', [ChatController::class, 'generateResponse']);
    });
    

    
    // Chat Orders (WhatsApp/Chat based ordering)
    Route::prefix('chat-orders')->group(function () {
        Route::post('message', [ChatOrderController::class, 'processMessage']);
        Route::get('{sessionId}', [ChatOrderController::class, 'getSession']);
        Route::post('{sessionId}/items', [ChatOrderController::class, 'addItem']);
        Route::delete('{sessionId}/items/{itemId}', [ChatOrderController::class, 'removeItem']);
        Route::post('{sessionId}/calculate', [ChatOrderController::class, 'calculateCost']);
        Route::post('{sessionId}/confirm', [ChatOrderController::class, 'confirmOrder']);
    });
    
    // Catalogues
    Route::get('catalogues', [CatalogueController::class, 'index']);
    Route::get('catalogues/{catalogue}', [CatalogueController::class, 'show']);
    Route::get('catalogues/{catalogue}/pages', [CatalogueController::class, 'getPages']);
    Route::post('catalogues', [CatalogueController::class, 'store']);
    Route::put('catalogues/{catalogue}', [CatalogueController::class, 'update']);
    Route::delete('catalogues/{catalogue}', [CatalogueController::class, 'destroy']);
    
    // Cart (guest access allowed)
    Route::post('cart/items', [CartController::class, 'addItem']);
    Route::get('cart/{cart}', [CartController::class, 'show']);
    Route::delete('cart/items/{cartItem}', [CartController::class, 'removeItem']);
    Route::patch('cart/items/{cartItem}', [CartController::class, 'updateItem']);
    
    // Orders (requires auth)
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('orders', [OrderController::class, 'store']);
        Route::get('orders', [OrderController::class, 'index']);
        Route::get('orders/{order}', [OrderController::class, 'show']);
    });
});
