<?php
/**
 * cPanel-friendly Cron Routes
 * These routes can be called via cPanel cron jobs using wget or curl
 */

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use App\Jobs\ProcessFollowUps;
use App\Models\Lead;
use App\Models\Inquiry;
use App\Models\Notification;

// Security middleware - only allow specific IPs or tokens
Route::middleware('throttle:60,1')->group(function () {

    // Main scheduler route
    Route::get('/cron/schedule', function () {
        try {
            Artisan::call('schedule:run');
            $output = Artisan::output();
            
            return response()->json([
                'status' => 'success',
                'timestamp' => now()->toDateTimeString(),
                'output' => trim($output)
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    });

    // Process notifications
    Route::get('/cron/notifications', function () {
        try {
            Artisan::call('notifications:process');
            $output = Artisan::output();
            
            return response()->json([
                'status' => 'success',
                'timestamp' => now()->toDateTimeString(),
                'processed' => trim($output)
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    });

    // Qualify leads
    Route::get('/cron/qualify-leads', function () {
        try {
            Artisan::call('leads:qualify');
            $output = Artisan::output();
            
            return response()->json([
                'status' => 'success',
                'timestamp' => now()->toDateTimeString(),
                'result' => trim($output)
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    });

    // Process follow-ups
    Route::get('/cron/follow-ups', function () {
        try {
            ProcessFollowUps::dispatch();
            
            return response()->json([
                'status' => 'success',
                'timestamp' => now()->toDateTimeString(),
                'message' => 'Follow-up job dispatched'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    });

    // Process queue (limited)
    Route::get('/cron/queue', function () {
        try {
            // Process up to 5 jobs
            Artisan::call('queue:work', [
                '--max-jobs' => 5,
                '--timeout' => 30,
                '--queue' => 'notifications,default'
            ]);
            
            $output = Artisan::output();
            
            return response()->json([
                'status' => 'success',
                'timestamp' => now()->toDateTimeString(),
                'processed' => trim($output)
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    });

    // System health check
    Route::get('/cron/health', function () {
        $health = [];
        
        try {
            // Database check
            DB::connection()->getPdo();
            $health['database'] = 'connected';
        } catch (Exception $e) {
            $health['database'] = 'failed';
        }
        
        // Business metrics
        $health['metrics'] = [
            'total_leads' => Lead::count(),
            'new_leads_today' => Lead::whereDate('created_at', today())->count(),
            'hot_leads' => Lead::where('quality', 'hot')->count(),
            'pending_inquiries' => Inquiry::where('status', 'open')->count(),
            'overdue_inquiries' => Inquiry::where('response_due_at', '<', now())
                ->whereNotIn('status', ['resolved', 'closed'])->count(),
            'pending_notifications' => Notification::where('status', 'pending')->count(),
            'jobs_in_queue' => DB::table('jobs')->count(),
            'failed_jobs' => DB::table('failed_jobs')->count(),
        ];
        
        $health['timestamp'] = now()->toDateTimeString();
        $health['status'] = $health['database'] === 'connected' ? 'healthy' : 'unhealthy';
        
        // Log health data
        Log::info('Health Check via Route', $health);
        
        return response()->json($health);
    });

    // System stats dashboard
    Route::get('/cron/stats', function () {
        $stats = [
            'leads' => [
                'total' => Lead::count(),
                'by_quality' => [
                    'hot' => Lead::where('quality', 'hot')->count(),
                    'warm' => Lead::where('quality', 'warm')->count(),
                    'cold' => Lead::where('quality', 'cold')->count(),
                ],
                'by_source' => [
                    'whatsapp' => Lead::where('source', 'whatsapp')->count(),
                    'web' => Lead::where('source', 'web')->count(),
                    'referral' => Lead::where('source', 'referral')->count(),
                ],
                'recent' => Lead::where('created_at', '>=', now()->subDays(7))->count(),
            ],
            'inquiries' => [
                'total' => Inquiry::count(),
                'open' => Inquiry::where('status', 'open')->count(),
                'resolved' => Inquiry::where('status', 'resolved')->count(),
                'overdue' => Inquiry::where('response_due_at', '<', now())
                    ->whereNotIn('status', ['resolved', 'closed'])->count(),
            ],
            'notifications' => [
                'total' => Notification::count(),
                'pending' => Notification::where('status', 'pending')->count(),
                'sent_today' => Notification::where('status', 'sent')
                    ->whereDate('sent_at', today())->count(),
            ],
            'system' => [
                'jobs_pending' => DB::table('jobs')->count(),
                'jobs_failed' => DB::table('failed_jobs')->count(),
                'timestamp' => now()->toDateTimeString(),
            ]
        ];
        
        return response()->json($stats, 200, [], JSON_PRETTY_PRINT);
    });
});