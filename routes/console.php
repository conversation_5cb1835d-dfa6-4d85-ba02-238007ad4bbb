<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;
use App\Jobs\ProcessFollowUps;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule automated tasks
Schedule::command('notifications:process')
    ->everyFiveMinutes()
    ->withoutOverlapping()
    ->runInBackground();

Schedule::command('leads:qualify')
    ->hourly()
    ->withoutOverlapping();

Schedule::job(new ProcessFollowUps)
    ->everyTenMinutes()
    ->withoutOverlapping();

// Clean up old notifications and activities (weekly)
Schedule::call(function () {
    // Delete sent notifications older than 30 days
    \App\Models\Notification::where('status', 'sent')
        ->where('sent_at', '<', now()->subDays(30))
        ->delete();
        
    // Delete old lead activities older than 90 days
    \App\Models\LeadActivity::where('created_at', '<', now()->subDays(90))
        ->delete();
})->weekly()->sundays()->at('02:00');

// Generate daily analytics summary
Schedule::call(function () {
    $stats = [
        'date' => now()->toDateString(),
        'new_leads' => \App\Models\Lead::whereDate('created_at', today())->count(),
        'new_inquiries' => \App\Models\Inquiry::whereDate('created_at', today())->count(),
        'notifications_sent' => \App\Models\Notification::where('status', 'sent')
            ->whereDate('sent_at', today())->count(),
        'hot_leads' => \App\Models\Lead::where('quality', 'hot')->count(),
    ];
    
    \Illuminate\Support\Facades\Log::info('Daily Analytics', $stats);
})->dailyAt('23:30');
