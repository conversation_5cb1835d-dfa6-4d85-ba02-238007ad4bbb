<?php

use App\Http\Controllers\LandingController;
use App\Http\Controllers\FileController;
use App\Http\Controllers\ConversationTestController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', [LandingController::class, 'index'])->name('home');
Route::get('/catalogues', [LandingController::class, 'catalogues'])->name('catalogues.index');
Route::get('/catalogues/{catalogue}', [LandingController::class, 'catalogue'])->name('catalogues.show');

// Serve uploaded files with CORS headers
Route::get('/uploads/{path}', [FileController::class, 'serveUpload'])->where('path', '.*');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Test interface for chat orders
    Route::get('/test/chat-orders', function () {
    return view('chat-order-test');
})->name('test.chat-orders');



require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
