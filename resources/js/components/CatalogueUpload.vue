<template>
  <div class="catalogue-upload-component">
    <div class="bg-white rounded-lg shadow-lg p-6">
      <h2 class="text-2xl font-bold text-gray-900 mb-6">Upload Catalogue</h2>
      
      <!-- Shop Selection -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">Select Shop</label>
        <select 
          v-model="form.shop_id" 
          class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          required
        >
          <option value="">Choose a shop...</option>
          <option v-for="shop in shops" :key="shop.id" :value="shop.id">
            {{ shop.name }}
          </option>
        </select>
      </div>
      
      <!-- Title -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">Catalogue Title</label>
        <input 
          v-model="form.title"
          type="text" 
          class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          placeholder="e.g., Black Friday Specials 2024"
          required
        />
      </div>
      
      <!-- Description -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">Description (Optional)</label>
        <textarea 
          v-model="form.description"
          rows="3"
          class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          placeholder="Describe the catalogue contents..."
        ></textarea>
      </div>
      
      <!-- Validity Dates -->
      <div class="grid grid-cols-2 gap-4 mb-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Valid From</label>
          <input 
            v-model="form.valid_from"
            type="date" 
            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Valid To</label>
          <input 
            v-model="form.valid_to"
            type="date" 
            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
        </div>
      </div>
      
      <!-- Upload Type Selection -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-4">Upload Type</label>
        <div class="grid grid-cols-2 gap-4">
          <button
            @click="uploadType = 'pdf'"
            :class="[
              'p-4 border-2 rounded-lg transition-all',
              uploadType === 'pdf' 
                ? 'border-orange-500 bg-orange-50' 
                : 'border-gray-300 hover:border-gray-400'
            ]"
          >
            <svg class="w-8 h-8 mx-auto mb-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p class="font-medium">PDF Document</p>
            <p class="text-sm text-gray-500 mt-1">Upload a single PDF file</p>
          </button>
          
          <button
            @click="uploadType = 'images'"
            :class="[
              'p-4 border-2 rounded-lg transition-all',
              uploadType === 'images' 
                ? 'border-orange-500 bg-orange-50' 
                : 'border-gray-300 hover:border-gray-400'
            ]"
          >
            <svg class="w-8 h-8 mx-auto mb-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p class="font-medium">Image Gallery</p>
            <p class="text-sm text-gray-500 mt-1">Upload multiple images</p>
          </button>
        </div>
      </div>
      
      <!-- File Upload Area -->
      <div v-if="uploadType" class="mb-6">
        <!-- PDF Upload -->
        <div v-if="uploadType === 'pdf'" class="space-y-4">
          <div 
            @drop="handlePdfDrop"
            @dragover.prevent
            @dragenter.prevent
            class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-orange-500 transition-colors"
          >
            <input 
              ref="pdfInput"
              type="file" 
              accept=".pdf"
              @change="handlePdfSelect"
              class="hidden"
            />
            
            <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            
            <p class="text-gray-600 mb-2">
              Drop your PDF file here or 
              <button @click="$refs.pdfInput.click()" class="text-orange-500 hover:text-orange-600 font-medium">
                browse
              </button>
            </p>
            <p class="text-sm text-gray-500">Maximum file size: 50MB</p>
            
            <div v-if="pdfFile" class="mt-4 p-3 bg-gray-100 rounded-lg">
              <p class="text-sm font-medium">{{ pdfFile.name }}</p>
              <p class="text-xs text-gray-500">{{ formatFileSize(pdfFile.size) }}</p>
            </div>
          </div>
        </div>
        
        <!-- Images Upload -->
        <div v-if="uploadType === 'images'" class="space-y-4">
          <div 
            @drop="handleImagesDrop"
            @dragover.prevent
            @dragenter.prevent
            class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-orange-500 transition-colors"
          >
            <input 
              ref="imagesInput"
              type="file" 
              accept="image/*"
              multiple
              @change="handleImagesSelect"
              class="hidden"
            />
            
            <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            
            <p class="text-gray-600 mb-2">
              Drop your images here or 
              <button @click="$refs.imagesInput.click()" class="text-orange-500 hover:text-orange-600 font-medium">
                browse
              </button>
            </p>
            <p class="text-sm text-gray-500">Maximum 10MB per image, up to 50 images</p>
          </div>
          
          <!-- Image Preview Grid -->
          <div v-if="imageFiles.length > 0" class="grid grid-cols-4 gap-4">
            <div v-for="(image, index) in imageFiles" :key="index" class="relative group">
              <img 
                :src="getImagePreview(image)" 
                :alt="`Page ${index + 1}`"
                class="w-full h-32 object-cover rounded-lg"
              />
              <button 
                @click="removeImage(index)"
                class="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              <p class="text-xs text-center mt-1">Page {{ index + 1 }}</p>
            </div>
          </div>
        </div>
        
        <!-- Cover Image (Optional) -->
        <div class="mt-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Cover Image (Optional)</label>
          <input 
            type="file" 
            accept="image/*"
            @change="handleCoverSelect"
            class="w-full px-4 py-2 border border-gray-300 rounded-lg"
          />
        </div>
      </div>
      
      <!-- Submit Button -->
      <div class="flex justify-end space-x-4">
        <button 
          @click="resetForm"
          class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button 
          @click="submitCatalogue"
          :disabled="!canSubmit || isUploading"
          :class="[
            'px-6 py-2 rounded-lg font-medium transition-colors',
            canSubmit && !isUploading
              ? 'bg-orange-500 text-white hover:bg-orange-600'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          ]"
        >
          <span v-if="!isUploading">Upload Catalogue</span>
          <span v-else class="flex items-center">
            <svg class="animate-spin h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Uploading...
          </span>
        </button>
      </div>
      
      <!-- Success Message -->
      <div v-if="successMessage" class="mt-4 p-4 bg-green-100 text-green-700 rounded-lg">
        {{ successMessage }}
      </div>
      
      <!-- Error Message -->
      <div v-if="errorMessage" class="mt-4 p-4 bg-red-100 text-red-700 rounded-lg">
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import axios from 'axios'

// State
const shops = ref([])
const form = ref({
  shop_id: '',
  title: '',
  description: '',
  valid_from: '',
  valid_to: '',
})
const uploadType = ref('')
const pdfFile = ref(null)
const imageFiles = ref([])
const coverFile = ref(null)
const isUploading = ref(false)
const successMessage = ref('')
const errorMessage = ref('')
const imagePreviews = ref([])

// Computed
const canSubmit = computed(() => {
  return form.value.shop_id && 
         form.value.title && 
         uploadType.value &&
         ((uploadType.value === 'pdf' && pdfFile.value) ||
          (uploadType.value === 'images' && imageFiles.value.length > 0))
})

// Methods
const fetchShops = async () => {
  try {
    const response = await axios.get('/api/v1/shops')
    shops.value = response.data.data
  } catch (error) {
    console.error('Failed to fetch shops:', error)
  }
}

const handlePdfDrop = (e) => {
  e.preventDefault()
  const file = e.dataTransfer.files[0]
  if (file && file.type === 'application/pdf') {
    pdfFile.value = file
  }
}

const handlePdfSelect = (e) => {
  const file = e.target.files[0]
  if (file) {
    pdfFile.value = file
  }
}

const handleImagesDrop = (e) => {
  e.preventDefault()
  const files = Array.from(e.dataTransfer.files).filter(file => 
    file.type.startsWith('image/')
  )
  addImages(files)
}

const handleImagesSelect = (e) => {
  const files = Array.from(e.target.files)
  addImages(files)
}

const addImages = (files) => {
  const maxImages = 50
  const remaining = maxImages - imageFiles.value.length
  const toAdd = files.slice(0, remaining)
  
  toAdd.forEach(file => {
    imageFiles.value.push(file)
    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      imagePreviews.value.push(e.target.result)
    }
    reader.readAsDataURL(file)
  })
}

const removeImage = (index) => {
  imageFiles.value.splice(index, 1)
  imagePreviews.value.splice(index, 1)
}

const getImagePreview = (image) => {
  const index = imageFiles.value.indexOf(image)
  return imagePreviews.value[index] || ''
}

const handleCoverSelect = (e) => {
  const file = e.target.files[0]
  if (file) {
    coverFile.value = file
  }
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i]
}

const submitCatalogue = async () => {
  if (!canSubmit.value || isUploading.value) return
  
  isUploading.value = true
  errorMessage.value = ''
  successMessage.value = ''
  
  try {
    const formData = new FormData()
    formData.append('shop_id', form.value.shop_id)
    formData.append('title', form.value.title)
    formData.append('description', form.value.description || '')
    formData.append('valid_from', form.value.valid_from || '')
    formData.append('valid_to', form.value.valid_to || '')
    formData.append('type', uploadType.value)
    
    if (uploadType.value === 'pdf' && pdfFile.value) {
      formData.append('pdf_file', pdfFile.value)
    } else if (uploadType.value === 'images') {
      imageFiles.value.forEach((file, index) => {
        formData.append(`images[${index}]`, file)
      })
    }
    
    if (coverFile.value) {
      formData.append('cover_image', coverFile.value)
    }
    
    const response = await axios.post('/api/v1/catalogues', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    
    if (response.data.success) {
      successMessage.value = 'Catalogue uploaded successfully!'
      resetForm()
      
      // Clear success message after 5 seconds
      setTimeout(() => {
        successMessage.value = ''
      }, 5000)
    }
  } catch (error) {
    console.error('Upload error:', error)
    errorMessage.value = error.response?.data?.error || 'Failed to upload catalogue. Please try again.'
  } finally {
    isUploading.value = false
  }
}

const resetForm = () => {
  form.value = {
    shop_id: '',
    title: '',
    description: '',
    valid_from: '',
    valid_to: '',
  }
  uploadType.value = ''
  pdfFile.value = null
  imageFiles.value = []
  coverFile.value = null
  imagePreviews.value = []
}

// Lifecycle
onMounted(() => {
  fetchShops()
})
</script>