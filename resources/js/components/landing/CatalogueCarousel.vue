<template>
  <section id="catalogues-section" class="py-16 bg-slate-50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold text-slate-900 mb-4">{{ title }}</h2>
        <p class="text-lg text-slate-600">Browse the latest catalogues from South African retailers</p>
      </div>
      
      <!-- Catalogue Categories Filter -->
      <div class="flex flex-wrap justify-center gap-3 mb-8">
        <button 
          v-for="category in categories" 
          :key="category"
          @click="selectedCategory = category"
          :class="[
            'px-4 py-2 rounded-full font-medium transition-all',
            selectedCategory === category 
              ? 'bg-blue-600 text-white shadow-lg' 
              : 'bg-white text-slate-700 hover:bg-blue-50 hover:text-blue-600 border border-slate-200'
          ]"
        >
          {{ category }}
        </button>
      </div>
      
      <!-- Carousel Container -->
      <div class="relative">
        <!-- Navigation buttons -->
        <button 
          @click="scrollLeft"
          :disabled="scrollPosition <= 0"
          :class="[
            'absolute left-0 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-white rounded-full shadow-lg border border-slate-200 transition-all',
            scrollPosition <= 0 ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-xl hover:bg-blue-50'
          ]"
        >
          <svg class="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        
        <button 
          @click="scrollRight"
          :disabled="scrollPosition >= maxScroll"
          :class="[
            'absolute right-0 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-white rounded-full shadow-lg border border-slate-200 transition-all',
            scrollPosition >= maxScroll ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-xl hover:bg-blue-50'
          ]"
        >
          <svg class="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
        
        <!-- Scrollable container -->
        <div 
          ref="carouselContainer"
          class="overflow-x-auto scrollbar-hide scroll-smooth"
          style="scrollbar-width: none; -ms-overflow-style: none;"
          @scroll="updateScrollPosition"
        >
          <div class="flex space-x-6 pb-4" style="width: fit-content;">
            <CatalogueCard
              v-for="catalogue in filteredCatalogues"
              :key="catalogue.id"
              :catalogue="catalogue"
              @open="$emit('open', $event)"
              @share="$emit('share', $event)"
              @send-to-chat="$emit('send-to-chat', $event)"
            />
          </div>
        </div>
        
        <!-- Scroll indicators -->
        <div class="flex justify-center mt-6 space-x-2">
          <div 
            v-for="(indicator, index) in scrollIndicators" 
            :key="index"
            :class="[
              'w-2 h-2 rounded-full transition-all',
              indicator ? 'bg-blue-600' : 'bg-slate-300'
            ]"
          ></div>
        </div>
      </div>
      
      <!-- View All Link -->
      <div class="text-center mt-12">
        <button class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold">
          View All Catalogues
          <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import CatalogueCard from './CatalogueCard.vue'
import type { CatalogueDoc } from '@/types/catalogue'

interface Props {
  catalogues: CatalogueDoc[]
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Featured Catalogues'
})

const emit = defineEmits<{
  open: [catalogue: CatalogueDoc]
  share: [catalogue: CatalogueDoc]
  'send-to-chat': [catalogue: CatalogueDoc]
}>()

// State
const selectedCategory = ref('All')
const scrollPosition = ref(0)
const maxScroll = ref(0)
const carouselContainer = ref<HTMLElement>()

// Categories
const categories = ref([
  'All',
  'Electronics',
  'Home & Garden',
  'Groceries',
  'Fashion',
  'Sports',
  'Books & Media'
])

// Filtered catalogues based on selected category
const filteredCatalogues = computed(() => {
  if (selectedCategory.value === 'All') {
    return props.catalogues
  }
  // In a real app, filter by category
  return props.catalogues.filter(catalogue => 
    catalogue.title.toLowerCase().includes(selectedCategory.value.toLowerCase())
  )
})

// Scroll indicators
const scrollIndicators = computed(() => {
  const total = Math.ceil(filteredCatalogues.value.length / 4) // Assuming 4 items visible
  const current = Math.floor(scrollPosition.value / (maxScroll.value / total))
  return Array.from({ length: total }, (_, index) => index === current)
})

// Methods
const scrollLeft = () => {
  if (carouselContainer.value) {
    const scrollAmount = carouselContainer.value.clientWidth * 0.8
    carouselContainer.value.scrollBy({ left: -scrollAmount, behavior: 'smooth' })
  }
}

const scrollRight = () => {
  if (carouselContainer.value) {
    const scrollAmount = carouselContainer.value.clientWidth * 0.8
    carouselContainer.value.scrollBy({ left: scrollAmount, behavior: 'smooth' })
  }
}

const updateScrollPosition = () => {
  if (carouselContainer.value) {
    scrollPosition.value = carouselContainer.value.scrollLeft
    maxScroll.value = carouselContainer.value.scrollWidth - carouselContainer.value.clientWidth
  }
}

// Auto-scroll functionality
let autoScrollInterval: number | null = null

const startAutoScroll = () => {
  autoScrollInterval = window.setInterval(() => {
    if (scrollPosition.value >= maxScroll.value) {
      // Reset to beginning
      carouselContainer.value?.scrollTo({ left: 0, behavior: 'smooth' })
    } else {
      scrollRight()
    }
  }, 5000) // Auto-scroll every 5 seconds
}

const stopAutoScroll = () => {
  if (autoScrollInterval) {
    clearInterval(autoScrollInterval)
    autoScrollInterval = null
  }
}

onMounted(() => {
  updateScrollPosition()
  startAutoScroll()
  
  // Add resize listener
  window.addEventListener('resize', updateScrollPosition)
})

onUnmounted(() => {
  stopAutoScroll()
  window.removeEventListener('resize', updateScrollPosition)
})
</script>

<style scoped>
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
</style>