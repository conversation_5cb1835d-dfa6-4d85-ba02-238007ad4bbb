<template>
  <!-- Chat Bubble -->
  <div v-if="!open" 
       @click="$emit('update:open', true)"
       class="fixed bottom-6 right-6 z-50 cursor-pointer group">
    <div class="w-16 h-16 bg-youze-orange rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 flex items-center justify-center group-hover:scale-110">
      <svg class="w-8 h-8 text-youze-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
      </svg>
    </div>
    <!-- Notification badge -->
    <div v-if="unreadCount > 0" class="absolute -top-2 -right-2 w-6 h-6 bg-youze-coral text-youze-white rounded-full flex items-center justify-center text-sm font-bold animate-pulse">
      {{ unreadCount > 9 ? '9+' : unreadCount }}
    </div>
  </div>

  <!-- Chat Window -->
  <div v-if="open" class="fixed bottom-6 right-6 w-96 h-[600px] bg-white rounded-2xl shadow-2xl border border-gray-200 flex flex-col z-50 max-h-[80vh]">
    <!-- Header -->
    <div class="flex items-center justify-between p-4 border-b border-youze-gray-200 bg-youze-orange text-youze-white rounded-t-2xl">
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-youze-white/20 rounded-full flex items-center justify-center">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        <div>
          <h3 class="font-semibold">YouzeAfrika Assistant</h3>
          <p class="text-sm opacity-90">{{ isTyping ? 'typing...' : 'Online' }}</p>
        </div>
      </div>
      <button @click="$emit('update:open', false)" class="p-1 hover:bg-youze-white/20 rounded">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- Messages Area -->
    <div ref="messagesContainer" class="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50">
      <!-- Welcome Message -->
      <div v-if="messages.length === 0" class="text-center py-8">
        <div class="w-16 h-16 bg-youze-orange/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-youze-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        </div>
        <h4 class="font-semibold text-youze-navy mb-2">Welcome! 👋</h4>
        <p class="text-youze-gray-600 text-sm">Ask me for any product. I can price it for you in USD.</p>
      </div>

      <!-- Messages -->
      <div v-for="message in messages" :key="message.id" 
           :class="['flex', message.role === 'user' ? 'justify-end' : 'justify-start']">
        <div :class="[
          'max-w-[80%] p-3 rounded-2xl shadow-sm',
          message.role === 'user' 
            ? 'bg-orange-500 text-white' 
            : 'bg-white text-gray-900 border border-gray-200'
        ]">
          <p class="text-sm">{{ message.content }}</p>
          <div class="text-xs opacity-70 mt-1">{{ formatTime(message.timestamp) }}</div>
        </div>
      </div>

      <!-- Typing indicator -->
      <div v-if="isTyping" class="flex justify-start">
        <div class="bg-youze-gray-100 p-3 rounded-2xl">
          <div class="flex space-x-1">
            <div class="w-2 h-2 bg-youze-gray-400 rounded-full animate-bounce" style="animation-delay: 0s"></div>
            <div class="w-2 h-2 bg-youze-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
            <div class="w-2 h-2 bg-youze-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div v-if="messages.length === 0" class="px-4 pb-2">
      <div class="flex flex-wrap gap-2">
        <button 
          v-for="action in quickActions" 
          :key="action.text"
          @click="sendQuickAction(action)"
          class="px-3 py-1.5 bg-youze-gray-100 text-youze-gray-700 rounded-full text-sm hover:bg-youze-orange-100 hover:text-youze-orange transition-colors"
        >
          {{ action.text }}
        </button>
      </div>
    </div>

    <!-- Input Area -->
    <div class="p-4 border-t border-youze-gray-200">
      <div class="flex space-x-2">
        <!-- File upload -->
        <label class="p-2 text-gray-500 hover:text-orange-500 cursor-pointer transition-colors">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
          </svg>
          <input type="file" class="hidden" @change="handleFileUpload" accept="image/*">
        </label>
        
        <!-- Text input -->
        <input
          v-model="inputMessage"
          type="text"
          placeholder="Ask me anything..."
          class="flex-1 px-4 py-2 border border-youze-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-youze-orange focus:border-transparent"
          @keyup.enter="sendMessage"
        />
        
        <!-- Send button -->
        <button 
          @click="sendMessage"
          :disabled="!inputMessage.trim()"
          :class="[
            'p-2 rounded-full transition-colors',
            inputMessage.trim() 
              ? 'bg-orange-500 text-white hover:bg-orange-600' 
              : 'bg-gray-200 text-gray-400 cursor-not-allowed'
          ]"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, onMounted } from 'vue'
import axios from 'axios'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  products?: any[]
}

interface Props {
  open: boolean
  conversationId?: string | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:open': [value: boolean]
  'send-message': [message: any]
}>()

// State
const messages = ref<Message[]>([])
const inputMessage = ref('')
const isTyping = ref(false)
const unreadCount = ref(0)
const messagesContainer = ref<HTMLElement>()
const currentConversationId = ref<number | null>(null)
const sessionId = ref<string | null>(null)

// Quick actions
const quickActions = [
  { text: 'Show me TVs', action: 'search' },
  { text: 'Upload Page', action: 'upload' },
  { text: 'Price Check', action: 'price' }
]

// Methods
const sendMessage = async () => {
  if (!inputMessage.value.trim()) return
  
  const messageText = inputMessage.value.trim()
  const userMessage: Message = {
    id: Date.now().toString(),
    role: 'user',
    content: messageText,
    timestamp: new Date()
  }
  
  messages.value.push(userMessage)
  inputMessage.value = ''
  scrollToBottom()
  
  // Start typing indicator
  isTyping.value = true
  
  try {
    const response = await axios.post('/api/v1/chat/message', {
      conversation_id: currentConversationId.value,
      message: messageText
    })
    
    if (response.data.success) {
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: response.data.response.text,
        timestamp: new Date(response.data.response.timestamp),
        products: response.data.response.products
      }
      
      messages.value.push(aiMessage)
      emit('send-message', aiMessage)
      scrollToBottom()
    }
  } catch (error) {
    console.error('Error sending message:', error)
    
    // Fallback message
    const errorMessage: Message = {
      id: (Date.now() + 1).toString(),
      role: 'assistant',
      content: 'I apologize, but I\'m having trouble processing your message right now. Please try again.',
      timestamp: new Date()
    }
    
    messages.value.push(errorMessage)
    scrollToBottom()
  } finally {
    isTyping.value = false
  }
}

const sendQuickAction = (action: any) => {
  inputMessage.value = action.text
  sendMessage()
}

const handleFileUpload = async (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  // Show user message with image
  const userMessage: Message = {
    id: Date.now().toString(),
    role: 'user',
    content: '📷 [Image uploaded]',
    timestamp: new Date()
  }
  
  messages.value.push(userMessage)
  scrollToBottom()
  
  // Start typing indicator
  isTyping.value = true
  
  try {
    const formData = new FormData()
    formData.append('conversation_id', currentConversationId.value?.toString() || '')
    formData.append('message', inputMessage.value || 'What do you see in this image?')
    formData.append('image', file)
    
    const response = await axios.post('/api/v1/chat/message', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    
    if (response.data.success) {
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: response.data.response.text,
        timestamp: new Date(response.data.response.timestamp),
        products: response.data.response.products
      }
      
      messages.value.push(aiMessage)
      scrollToBottom()
    }
    
    // Clear input
    inputMessage.value = ''
    
  } catch (error) {
    console.error('Error uploading image:', error)
    
    const errorMessage: Message = {
      id: (Date.now() + 1).toString(),
      role: 'assistant',
      content: 'I had trouble analyzing your image. Please try again or describe what you\'re looking for.',
      timestamp: new Date()
    }
    
    messages.value.push(errorMessage)
    scrollToBottom()
  } finally {
    isTyping.value = false
  }
}


const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

// Initialize conversation
const initConversation = async () => {
  try {
    const response = await axios.post('/api/v1/chat/start', {
      session_id: sessionId.value
    })
    
    if (response.data.success) {
      currentConversationId.value = response.data.conversation_id
      sessionId.value = response.data.session_id
      
      // Load existing messages if any
      if (response.data.messages && response.data.messages.length > 0) {
        messages.value = response.data.messages.map((msg: any) => ({
          id: msg.id.toString(),
          role: msg.role,
          content: msg.content,
          timestamp: new Date(msg.timestamp),
          products: msg.products
        }))
        
        nextTick(() => scrollToBottom())
      }
    }
  } catch (error) {
    console.error('Error initializing conversation:', error)
  }
}

// Watch for open state changes
watch(() => props.open, (isOpen) => {
  if (isOpen) {
    unreadCount.value = 0
    
    // Initialize conversation if not already done
    if (!currentConversationId.value) {
      initConversation()
    }
    
    nextTick(() => scrollToBottom())
  }
})

// Initialize on component mount
onMounted(() => {
  // Generate a session ID if not already set
  if (!sessionId.value) {
    sessionId.value = 'web_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }
})
</script>