<template>
  <div class="relative">
    <!-- Main Search Input -->
    <div class="relative bg-white rounded-xl border-2 border-gray-200 focus-within:border-orange-500 transition-colors shadow-lg">
      <div class="flex items-center p-4">
        <!-- Search Icon -->
        <svg class="w-6 h-6 text-gray-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
        
        <!-- Input Field -->
        <input
          v-model="searchQuery"
          type="text"
          :placeholder="placeholder"
          class="flex-1 text-lg text-youze-navy placeholder-youze-gray-500 bg-transparent border-none outline-none"
          @keyup.enter="handleSubmit"
          @focus="showSuggestions = true"
        />
        
        <!-- Voice Search Button -->
        <button 
          @click="startVoiceSearch"
          :class="[
            'p-2 mr-2 rounded-lg transition-colors',
            isListening ? 'bg-red-100 text-red-600' : 'text-gray-500 hover:text-orange-500 hover:bg-orange-50'
          ]"
          :title="isListening ? 'Stop listening' : 'Voice search'"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
          </svg>
        </button>
        
        <!-- Image Upload Button -->
        <label class="p-2 mr-2 text-gray-500 hover:text-orange-500 hover:bg-orange-50 rounded-lg cursor-pointer transition-colors">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <input 
            ref="fileInput"
            type="file" 
            class="hidden" 
            accept="image/*" 
            @change="handleFileUpload"
          />
        </label>
        
        <!-- Search Button -->
        <button 
          @click="handleSubmit"
          class="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors font-semibold"
        >
          Search
        </button>
      </div>
    </div>
    
    <!-- Search Suggestions/Quick Actions -->
    <div v-if="showSuggestions && (suggestions.length > 0 || quickActions.length > 0)" 
         class="absolute top-full left-0 right-0 mt-2 bg-youze-white border border-youze-gray-200 rounded-xl shadow-xl z-50 max-h-96 overflow-y-auto">
      
      <!-- Quick Actions -->
      <div v-if="quickActions.length > 0" class="p-4 border-b border-youze-gray-100">
        <h4 class="text-sm font-semibold text-youze-gray-700 mb-3">Quick Actions</h4>
        <div class="flex flex-wrap gap-2">
          <button 
            v-for="action in quickActions" 
            :key="action.text"
            @click="handleQuickAction(action)"
            class="px-3 py-1.5 bg-youze-gray-100 text-youze-gray-700 rounded-full text-sm hover:bg-youze-orange-100 hover:text-youze-orange transition-colors"
          >
            {{ action.text }}
          </button>
        </div>
      </div>
      
      <!-- Search Suggestions -->
      <div v-if="suggestions.length > 0" class="p-2">
        <h4 class="px-2 py-2 text-sm font-semibold text-youze-gray-700">Suggestions</h4>
        <button 
          v-for="suggestion in suggestions" 
          :key="suggestion"
          @click="selectSuggestion(suggestion)"
          class="w-full text-left px-3 py-2 text-youze-gray-700 hover:bg-youze-gray-100 rounded-lg transition-colors"
        >
          <span class="flex items-center">
            <svg class="w-4 h-4 text-youze-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            {{ suggestion }}
          </span>
        </button>
      </div>
    </div>
    
    <!-- Voice Search Indicator -->
    <div v-if="isListening" class="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-red-600 text-white px-4 py-2 rounded-full text-sm font-medium animate-pulse">
      🎤 Listening...
    </div>
  </div>
  
  <!-- Click away overlay -->
  <div v-if="showSuggestions" @click="showSuggestions = false" class="fixed inset-0 z-40"></div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface Props {
  placeholder?: string
}

interface QuickAction {
  text: string
  action: string
  params?: any
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Search products or upload catalogue images...'
})

const emit = defineEmits<{
  submit: [query: string]
  upload: [file: File]
}>()

// State
const searchQuery = ref('')
const showSuggestions = ref(false)
const isListening = ref(false)
const fileInput = ref<HTMLInputElement>()

// Quick actions based on popular searches
const quickActions = ref<QuickAction[]>([
  { text: 'Show me TVs', action: 'search', params: 'televisions' },
  { text: 'Upload Catalogue Page', action: 'upload' },
  { text: 'Makro Electronics', action: 'shop', params: 'makro' },
  { text: 'Game Specials', action: 'shop', params: 'game' },
  { text: 'Laptops & Computers', action: 'category', params: 'electronics' },
])

// Search suggestions (could be fetched from API)
const suggestions = computed(() => {
  if (!searchQuery.value || searchQuery.value.length < 2) return []
  
  const mockSuggestions = [
    'Samsung Smart TV 55 inch',
    'Apple iPhone 14',
    'HP Laptop i5',
    'Sony PlayStation 5',
    'LG Washing Machine',
    'Samsung Galaxy S23',
    'Dell Monitor 27 inch',
    'Canon Camera DSLR'
  ]
  
  return mockSuggestions.filter(suggestion => 
    suggestion.toLowerCase().includes(searchQuery.value.toLowerCase())
  ).slice(0, 5)
})

// Methods
const handleSubmit = () => {
  if (searchQuery.value.trim()) {
    emit('submit', searchQuery.value.trim())
    showSuggestions.value = false
    searchQuery.value = ''
  }
}

const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    emit('upload', file)
    // Reset file input
    if (fileInput.value) {
      fileInput.value.value = ''
    }
  }
}

const handleQuickAction = (action: QuickAction) => {
  switch (action.action) {
    case 'search':
      searchQuery.value = action.params
      handleSubmit()
      break
    case 'upload':
      fileInput.value?.click()
      break
    case 'shop':
    case 'category':
      // Navigate or filter by shop/category
      console.log('Navigate to:', action.action, action.params)
      break
  }
  showSuggestions.value = false
}

const selectSuggestion = (suggestion: string) => {
  searchQuery.value = suggestion
  handleSubmit()
}

const startVoiceSearch = () => {
  if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
    alert('Voice search is not supported in your browser')
    return
  }
  
  if (isListening.value) {
    stopVoiceSearch()
    return
  }
  
  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
  const recognition = new SpeechRecognition()
  
  recognition.continuous = false
  recognition.interimResults = false
  recognition.lang = 'en-US'
  
  recognition.onstart = () => {
    isListening.value = true
  }
  
  recognition.onresult = (event) => {
    const transcript = event.results[0][0].transcript
    searchQuery.value = transcript
    isListening.value = false
    handleSubmit()
  }
  
  recognition.onerror = (event) => {
    console.error('Speech recognition error:', event.error)
    isListening.value = false
  }
  
  recognition.onend = () => {
    isListening.value = false
  }
  
  recognition.start()
}

const stopVoiceSearch = () => {
  isListening.value = false
  // The recognition will automatically stop when setting isListening to false
}

// Click outside to close suggestions
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showSuggestions.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// Extend window interface for speech recognition
declare global {
  interface Window {
    SpeechRecognition: any
    webkitSpeechRecognition: any
  }
}
</script>