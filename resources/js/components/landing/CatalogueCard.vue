<template>
  <div class="flex-shrink-0 w-80 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-slate-100 group">
    <!-- Cover Image -->
    <div class="relative overflow-hidden rounded-t-2xl">
      <img 
        :src="catalogue.cover_url || '/images/placeholder-catalogue.jpg'"
        :alt="catalogue.title"
        class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500"
        @error="handleImageError"
      />
      
      <!-- Type Badge -->
      <div class="absolute top-3 left-3">
        <span :class="[
          'px-2 py-1 rounded-full text-xs font-semibold',
          catalogue.type === 'pdf' 
            ? 'bg-red-100 text-red-700' 
            : 'bg-blue-100 text-blue-700'
        ]">
          {{ catalogue.type.toUpperCase() }}
        </span>
      </div>
      
      <!-- Shop Logo -->
      <div class="absolute top-3 right-3 w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center">
        <img 
          :src="`/images/shops/${catalogue.shop_slug}-logo.png`"
          :alt="catalogue.shop_slug"
          class="w-6 h-6 object-contain"
          @error="showShopInitial = true"
        />
        <span v-if="showShopInitial" class="text-sm font-bold text-slate-700">
          {{ catalogue.shop_slug.charAt(0).toUpperCase() }}
        </span>
      </div>
      
      <!-- Quick Actions Overlay -->
      <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-3">
        <button 
          @click="$emit('open', catalogue)"
          class="p-3 bg-white/90 text-slate-900 rounded-full hover:bg-white hover:scale-110 transition-all"
          title="Open Catalogue"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
        </button>
        
        <button 
          @click="$emit('send-to-chat', catalogue)"
          class="p-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 hover:scale-110 transition-all"
          title="Send to Chat"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        </button>
        
        <button 
          @click="$emit('share', catalogue)"
          class="p-3 bg-white/90 text-slate-900 rounded-full hover:bg-white hover:scale-110 transition-all"
          title="Share Catalogue"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Content -->
    <div class="p-6">
      <!-- Title and Shop -->
      <div class="mb-4">
        <h3 class="text-lg font-semibold text-slate-900 mb-1 line-clamp-2 group-hover:text-blue-600 transition-colors">
          {{ catalogue.title }}
        </h3>
        <p class="text-sm text-slate-500 capitalize">{{ catalogue.shop_slug }}</p>
      </div>
      
      <!-- Meta Information -->
      <div class="flex items-center justify-between text-sm text-slate-600 mb-4">
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <span v-if="catalogue.type === 'pdf'">
            {{ catalogue.page_count }} pages
          </span>
          <span v-else>
            {{ catalogue.images?.length || 0 }} images
          </span>
        </div>
        
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ formatDate(catalogue.updated_at) }}</span>
        </div>
      </div>
      
      <!-- Action Buttons -->
      <div class="flex space-x-2">
        <button 
          @click="$emit('open', catalogue)"
          class="flex-1 bg-slate-100 text-slate-700 py-2 px-4 rounded-lg hover:bg-slate-200 transition-colors font-medium text-sm"
        >
          Open
        </button>
        <button 
          @click="$emit('send-to-chat', catalogue)"
          class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium text-sm"
        >
          Send to Chat
        </button>
        <button 
          @click="$emit('share', catalogue)"
          class="p-2 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { CatalogueDoc } from '@/types/catalogue'

interface Props {
  catalogue: CatalogueDoc
}

defineProps<Props>()

const emit = defineEmits<{
  open: [catalogue: CatalogueDoc]
  share: [catalogue: CatalogueDoc]
  'send-to-chat': [catalogue: CatalogueDoc]
}>()

const showShopInitial = ref(false)

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/placeholder-catalogue.jpg'
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) return 'Today'
  if (diffDays === 1) return 'Yesterday'
  if (diffDays < 7) return `${diffDays} days ago`
  if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`
  
  return date.toLocaleDateString()
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>