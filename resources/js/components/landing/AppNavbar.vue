<template>
  <nav class="sticky top-0 z-50 bg-youze-white/95 backdrop-blur-md border-b border-youze-gray-200">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <div class="flex-shrink-0">
          <Link href="/" class="flex items-center space-x-3">
            <img src="/logo.jpeg" alt="Youze Afrika" class="w-8 h-8 rounded-lg object-cover" />
            <span class="text-xl font-bold" style="color: var(--youze-navy)">Youze Afrika</span>
          </Link>
        </div>
        
        <!-- Desktop Navigation -->
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-8">
            <button @click="openShopsModal" class="text-youze-gray-700 hover:text-youze-orange transition-colors font-medium">
              Shops
            </button>
            <button @click="openCataloguesModal" class="text-youze-gray-700 hover:text-youze-orange transition-colors font-medium">
              Catalogues
            </button>
            <button @click="openHowItWorksModal" class="text-youze-gray-700 hover:text-youze-orange transition-colors font-medium">
              How it works
            </button>
            <button class="text-youze-gray-700 hover:text-youze-orange transition-colors font-medium">
              Track Order
            </button>
          </div>
        </div>

        <!-- Right side -->
        <div class="flex items-center space-x-4">
          <!-- Chat CTA -->
          <button
            @click="$emit('open-chat')"
            class="hidden sm:inline-flex items-center px-4 py-2 bg-youze-orange text-youze-white rounded-lg hover:bg-youze-orange-dark transition-colors font-medium"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            Open Chat
          </button>

          <!-- User Menu -->
          <div v-if="user" class="relative">
            <button @click="showUserMenu = !showUserMenu" class="flex items-center space-x-2 text-youze-gray-700 hover:text-youze-navy">
              <div class="w-8 h-8 bg-youze-gray-300 rounded-full flex items-center justify-center">
                <span class="text-sm font-medium text-youze-navy">{{ user.name?.[0]?.toUpperCase() }}</span>
              </div>
            </button>

            <!-- User Dropdown -->
            <div v-if="showUserMenu" class="absolute right-0 mt-2 w-48 bg-youze-white rounded-md shadow-lg py-1 z-50 border border-youze-gray-200">
              <Link href="/dashboard" class="block px-4 py-2 text-sm text-youze-gray-700 hover:bg-youze-orange-50 hover:text-youze-orange">Dashboard</Link>
              <Link href="/profile" class="block px-4 py-2 text-sm text-youze-gray-700 hover:bg-youze-orange-50 hover:text-youze-orange">Profile</Link>
              <Link href="/logout" method="post" class="block px-4 py-2 text-sm text-youze-gray-700 hover:bg-youze-orange-50 hover:text-youze-orange">Logout</Link>
            </div>
          </div>

          <!-- Auth Links -->
          <div v-else class="hidden sm:flex items-center space-x-4">
            <Link href="/login" class="text-youze-gray-700 hover:text-youze-orange transition-colors font-medium">Login</Link>
            <Link href="/register" class="bg-youze-orange text-youze-white px-4 py-2 rounded-lg hover:bg-youze-orange-dark transition-colors font-medium">Register</Link>
          </div>

          <!-- Mobile menu button -->
          <button @click="showMobileMenu = !showMobileMenu" class="md:hidden p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path v-if="!showMobileMenu" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <div v-if="showMobileMenu" class="md:hidden border-t border-youze-gray-200 py-4">
        <div class="space-y-3">
          <button class="block w-full text-left text-youze-gray-700 hover:text-youze-orange font-medium">Shops</button>
          <button class="block w-full text-left text-youze-gray-700 hover:text-youze-orange font-medium">Catalogues</button>
          <button class="block w-full text-left text-youze-gray-700 hover:text-youze-orange font-medium">How it works</button>
          <button class="block w-full text-left text-youze-gray-700 hover:text-youze-orange font-medium">Track Order</button>
          <button
            @click="$emit('open-chat')"
            class="w-full mt-4 bg-youze-orange text-youze-white px-4 py-2 rounded-lg hover:bg-youze-orange-dark transition-colors font-medium"
          >
            Open Chat
          </button>
          <div v-if="!user" class="pt-4 border-t border-youze-gray-200 space-y-2">
            <Link href="/login" class="block text-youze-gray-700 hover:text-youze-orange font-medium">Login</Link>
            <Link href="/register" class="block text-youze-gray-700 hover:text-youze-orange font-medium">Register</Link>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Link } from '@inertiajs/vue3'

interface Props {
  user?: {
    id: number
    name: string
    email: string
  } | null
}

defineProps<Props>()

const emit = defineEmits<{
  'open-chat': []
  'open-cart': []
}>()

const showMobileMenu = ref(false)
const showUserMenu = ref(false)

const openShopsModal = () => {
  // TODO: Implement shops modal
  console.log('Open shops modal')
}

const openCataloguesModal = () => {
  // TODO: Implement catalogues modal
  console.log('Open catalogues modal')
}

const openHowItWorksModal = () => {
  // TODO: Implement how it works modal
  console.log('Open how it works modal')
}
</script>
