<template>
  <div v-if="open" class="fixed inset-0 z-50 overflow-hidden">
    <!-- Backdrop -->
    <div @click="$emit('update:open', false)" class="absolute inset-0 bg-black/50"></div>
    
    <!-- Drawer -->
    <div class="absolute right-0 top-0 h-full w-96 bg-white shadow-2xl transform transition-transform duration-300">
      <!-- Header -->
      <div class="flex items-center justify-between p-4 border-b border-slate-200">
        <h2 class="text-lg font-semibold text-slate-900">Shopping Cart</h2>
        <button @click="$emit('update:open', false)" class="p-2 hover:bg-slate-100 rounded">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <!-- Content -->
      <div class="flex-1 overflow-y-auto p-4">
        <div v-if="items.length === 0" class="text-center py-12">
          <svg class="w-16 h-16 text-slate-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6H19" />
          </svg>
          <p class="text-slate-500">Your cart is empty</p>
        </div>
        
        <div v-for="item in items" :key="item.id" class="mb-4 p-4 border border-slate-200 rounded-lg">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h4 class="font-medium text-slate-900">{{ item.name }}</h4>
              <p class="text-sm text-slate-500">Qty: {{ item.quantity }}</p>
              <p class="font-semibold text-blue-600">${{ item.price }}</p>
            </div>
            <button class="p-2 text-red-600 hover:bg-red-50 rounded">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>
      
      <!-- Footer -->
      <div v-if="items.length > 0" class="p-4 border-t border-slate-200">
        <div class="flex items-center justify-between mb-4">
          <span class="font-semibold text-slate-900">Total:</span>
          <span class="text-xl font-bold text-blue-600">${{ total }}</span>
        </div>
        <button 
          @click="$emit('request-order')"
          class="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors font-semibold"
        >
          Request Order
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  open: boolean
  items: any[]
}

defineProps<Props>()

const emit = defineEmits<{
  'update:open': [value: boolean]
  'request-order': []
}>()

const total = 0 // Calculate total from items
</script>