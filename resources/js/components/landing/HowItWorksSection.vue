<template>
  <section class="py-16 bg-youze-gray-50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold text-youze-navy mb-4">How It Works</h2>
        <p class="text-lg text-youze-gray-600 max-w-2xl mx-auto">
          Get South African products delivered to Zimbabwe in three simple steps
        </p>
      </div>
      
      <div class="grid md:grid-cols-3 gap-8 lg:gap-12">
        <div 
          v-for="(step, index) in steps" 
          :key="index"
          class="relative text-center group"
        >
          <!-- Step Connection Line -->
          <div 
            v-if="index < steps.length - 1"
            class="hidden md:block absolute top-16 left-1/2 w-full h-0.5 bg-gradient-to-r from-youze-orange to-youze-coral transform translate-x-1/2 z-0"
          ></div>
          
          <!-- Step Icon -->
          <div class="relative z-10 w-32 h-32 mx-auto mb-6 bg-gradient-to-br from-youze-orange to-youze-coral rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
            <div class="w-20 h-20 bg-youze-white rounded-full flex items-center justify-center">
              <component :is="step.icon" class="w-10 h-10 text-youze-orange" />
            </div>
            <!-- Step Number -->
            <div class="absolute -top-2 -right-2 w-8 h-8 bg-youze-coral text-youze-white rounded-full flex items-center justify-center font-bold text-sm">
              {{ index + 1 }}
            </div>
          </div>
          
          <!-- Step Content -->
          <div class="relative z-10">
            <h3 class="text-xl font-semibold text-youze-navy mb-3">{{ step.title }}</h3>
            <p class="text-youze-gray-600 leading-relaxed">{{ step.description }}</p>
            
            <!-- Features List -->
            <ul class="mt-4 space-y-1 text-sm text-youze-gray-500">
              <li v-for="feature in step.features" :key="feature" class="flex items-center">
                <svg class="w-4 h-4 text-youze-success mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                {{ feature }}
              </li>
            </ul>
          </div>
        </div>
      </div>
      
      <!-- CTA Section -->
      <div class="mt-16 text-center bg-youze-white rounded-2xl p-8 border border-youze-gray-200">
        <h3 class="text-2xl font-semibold text-youze-navy mb-4">Ready to get started?</h3>
        <p class="text-youze-gray-600 mb-6">Join thousands of Zimbabweans shopping from South Africa</p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <button class="bg-youze-orange text-youze-white px-6 py-3 rounded-lg hover:bg-youze-orange-dark transition-colors font-semibold">
            Start Shopping Now
          </button>
          <button class="bg-white text-blue-600 border-2 border-blue-600 px-6 py-3 rounded-lg hover:bg-blue-50 transition-colors font-semibold">
            Watch Demo Video
          </button>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { h } from 'vue'

const steps = [
  {
    title: 'Find & Browse',
    description: 'Browse catalogues or search for products from major South African stores. Upload images or ask our AI assistant for help.',
    features: [
      'Search by image',
      'Voice search support',
      'AI-powered assistance',
      'Live catalogue updates'
    ],
    icon: () => h('svg', { 
      fill: 'none', 
      stroke: 'currentColor', 
      viewBox: '0 0 24 24',
      class: 'w-10 h-10'
    }, [
      h('path', { 
        'stroke-linecap': 'round', 
        'stroke-linejoin': 'round', 
        'stroke-width': '2', 
        d: 'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z' 
      })
    ])
  },
  {
    title: 'Get USD Price',
    description: 'Our AI instantly converts ZAR prices to USD, including logistics, duties, and our service fee for complete transparency.',
    features: [
      'Live exchange rates',
      'Transparent pricing',
      'No hidden fees',
      'Instant calculations'
    ],
    icon: () => h('svg', { 
      fill: 'none', 
      stroke: 'currentColor', 
      viewBox: '0 0 24 24',
      class: 'w-10 h-10'
    }, [
      h('path', { 
        'stroke-linecap': 'round', 
        'stroke-linejoin': 'round', 
        'stroke-width': '2', 
        d: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1' 
      })
    ])
  },
  {
    title: 'Request & Deliver',
    description: 'Add items to cart and submit your order request. We handle purchasing, shipping, and customs for seamless delivery to Zimbabwe.',
    features: [
      'Professional procurement',
      'Secure payment processing',
      'Customs handling',
      '7-14 day delivery'
    ],
    icon: () => h('svg', { 
      fill: 'none', 
      stroke: 'currentColor', 
      viewBox: '0 0 24 24',
      class: 'w-10 h-10'
    }, [
      h('path', { 
        'stroke-linecap': 'round', 
        'stroke-linejoin': 'round', 
        'stroke-width': '2', 
        d: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10' 
      })
    ])
  }
]
</script>