<template>
  <section class="relative overflow-hidden bg-gradient-to-br from-orange-100 via-gray-50 to-pink-100 pt-16 pb-24">
    <!-- Background pattern -->
    <div class="absolute inset-0 opacity-5">
      <div class="absolute top-0 left-0 w-64 h-64 bg-youze-orange rounded-full -translate-x-32 -translate-y-32"></div>
      <div class="absolute bottom-0 right-0 w-96 h-96 bg-youze-coral rounded-full translate-x-48 translate-y-48"></div>
    </div>
    
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative">
      <div class="lg:grid lg:grid-cols-12 lg:gap-8 items-center">
        <!-- Left Column -->
        <div class="lg:col-span-6">
          <div class="text-center lg:text-left">
            <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold text-youze-navy leading-tight">
              Buy from SA.
              <span class="text-youze-orange">Get a Zim price,</span>
              <span class="bg-gradient-to-r from-youze-orange to-youze-coral bg-clip-text text-transparent">instantly.</span>
            </h1>
            
            <p class="mt-6 text-xl text-youze-gray-700 max-w-2xl mx-auto lg:mx-0">
              Browse South African store catalogues, get instant USD pricing via our AI assistant, 
              and request orders hassle-free from Zimbabwe.
            </p>
            
            <!-- Search Bar -->
            <div class="mt-8 max-w-2xl mx-auto lg:mx-0">
              <HeroSearch 
                placeholder="Search products or upload catalogue images..."
                @submit="handleSearch"
                @upload="handleImageUpload"
              />
            </div>
            
            <!-- CTAs -->
            <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <button 
                @click="scrollToCatalogues"
                class="inline-flex items-center px-6 py-3 bg-youze-orange text-youze-white rounded-lg hover:bg-youze-orange-dark transition-colors font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                Browse Catalogues
              </button>
              
              <button 
                @click="$emit('open-chat')"
                class="inline-flex items-center px-6 py-3 bg-youze-white text-youze-orange border-2 border-youze-orange rounded-lg hover:bg-youze-orange-50 transition-colors font-semibold text-lg"
              >
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                Chat for Pricing
              </button>
              
              <button class="inline-flex items-center px-6 py-3 bg-youze-gray-100 text-youze-gray-700 rounded-lg hover:bg-youze-gray-200 transition-colors font-semibold text-lg">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Track My Order
              </button>
            </div>
          </div>
        </div>
        
        <!-- Right Column -->
        <div class="mt-12 lg:mt-0 lg:col-span-6">
          <div class="relative">
            <!-- Featured Catalogue Carousel -->
            <div class="bg-youze-white rounded-2xl shadow-2xl p-6 border border-youze-gray-200">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-youze-navy">Featured Catalogues</h3>
                <span class="text-sm text-youze-gray-500">Swipe to explore</span>
              </div>
              
              <div class="space-y-4">
                <div v-for="catalogue in featuredCatalogues" :key="catalogue.id" 
                     class="flex items-center space-x-4 p-3 border border-youze-gray-200 rounded-lg hover:shadow-md hover:border-youze-orange transition-all cursor-pointer">
                  <img :src="catalogue.cover_url" :alt="catalogue.title" class="w-16 h-16 object-cover rounded-lg">
                  <div class="flex-1">
                    <h4 class="font-medium text-youze-navy">{{ catalogue.title }}</h4>
                    <p class="text-sm text-youze-gray-600">{{ catalogue.shop_slug }}</p>
                  </div>
                  <button class="p-2 text-youze-orange hover:bg-youze-orange-50 rounded-lg transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            
            <!-- Quick Quote Demo Card -->
            <div class="mt-6 bg-gradient-to-r from-orange-500 to-pink-500 rounded-2xl p-6 text-white shadow-lg">
              <div class="flex items-center justify-between mb-3">
                <h3 class="font-semibold text-white">Quick Quote Demo</h3>
                <div class="w-3 h-3 bg-white rounded-full animate-pulse"></div>
              </div>
              <div class="space-y-2 text-sm text-white/90">
                <div class="flex justify-between">
                  <span>Hisense 40" TV (Makro)</span>
                  <span>R 4,299</span>
                </div>
                <div class="flex justify-between">
                  <span>USD Price + Logistics</span>
                  <span class="font-semibold text-white">$285</span>
                </div>
              </div>
              <button class="mt-4 w-full bg-white text-orange-600 py-2 rounded-lg font-semibold hover:bg-orange-50 transition-colors">
                Get Real Quote
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import HeroSearch from './HeroSearch.vue'
import type { CatalogueDoc } from '@/types/catalogue'

const emit = defineEmits<{
  'open-chat': []
}>()

// Mock featured catalogues for demo
const featuredCatalogues = ref<CatalogueDoc[]>([
  {
    id: 1,
    shop_slug: 'makro',
    title: 'Makro Electronics Specials',
    type: 'pdf',
    cover_url: '/images/catalogue-covers/makro-placeholder.jpg',
    page_count: 24,
    updated_at: '2024-08-29T10:00:00Z'
  },
  {
    id: 2,
    shop_slug: 'game',
    title: 'Game Home & Garden',
    type: 'images',
    cover_url: '/images/catalogue-covers/game-placeholder.jpg',
    images: ['cover1.jpg', 'page1.jpg'],
    updated_at: '2024-08-28T15:30:00Z'
  },
  {
    id: 3,
    shop_slug: 'shoprite',
    title: 'Shoprite Monthly Deals',
    type: 'pdf',
    cover_url: '/images/catalogue-covers/shoprite-placeholder.jpg',
    page_count: 18,
    updated_at: '2024-08-27T09:15:00Z'
  }
])

const handleSearch = (query: string) => {
  console.log('Search query:', query)
  // Implement product search
}

const handleImageUpload = (file: File) => {
  console.log('Image upload:', file)
  // Implement image-based product search
}

const scrollToCatalogues = () => {
  const cataloguesSection = document.querySelector('#catalogues-section')
  cataloguesSection?.scrollIntoView({ behavior: 'smooth' })
}
</script>