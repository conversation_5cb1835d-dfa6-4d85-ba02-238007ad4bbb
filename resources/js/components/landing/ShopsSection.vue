<template>
  <section class="py-16 bg-youze-white">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold text-youze-navy mb-4">Shop from South Africa's Top Retailers</h2>
        <p class="text-lg text-youze-gray-600 max-w-2xl mx-auto">
          Browse catalogues from major South African stores and get instant USD pricing with delivery to Zimbabwe.
        </p>
      </div>
      
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-12">
        <div 
          v-for="shop in displayShops" 
          :key="shop.id"
          @click="filterByShop(shop)"
          class="group cursor-pointer bg-youze-white border border-youze-gray-200 rounded-xl p-4 hover:shadow-lg hover:border-youze-orange transition-all duration-300 text-center"
        >
          <div class="w-20 h-20 mx-auto mb-3 bg-youze-gray-50 rounded-xl flex items-center justify-center group-hover:bg-youze-orange-50 transition-colors p-2">
            <img 
              v-if="shop.logo_url" 
              :src="shop.logo_url" 
              :alt="shop.name"
              class="max-w-full max-h-full object-contain"
              @error="handleImageError"
            />
            <div v-else class="w-12 h-12 bg-youze-gray-300 rounded-lg flex items-center justify-center">
              <span class="text-youze-gray-600 font-bold text-lg">{{ shop.name.charAt(0) }}</span>
            </div>
          </div>
          <h3 class="font-semibold text-youze-navy group-hover:text-youze-orange transition-colors text-sm">{{ shop.name }}</h3>
          <p class="text-xs text-youze-gray-500 mt-1">{{ shop.catalogue_count || 0 }} catalogues</p>
        </div>
      </div>
      
      <!-- Stats Row -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 bg-youze-gray-50 rounded-2xl p-8">
        <div class="text-center">
          <div class="text-3xl font-bold text-youze-orange mb-2">{{ stats.totalShops }}+</div>
          <div class="text-youze-gray-600">Partner Stores</div>
        </div>
        <div class="text-center">
          <div class="text-3xl font-bold text-youze-orange mb-2">{{ stats.totalProducts }}K+</div>
          <div class="text-youze-gray-600">Products Available</div>
        </div>
        <div class="text-center">
          <div class="text-3xl font-bold text-youze-orange mb-2">{{ stats.totalCatalogues }}+</div>
          <div class="text-youze-gray-600">Active Catalogues</div>
        </div>
        <div class="text-center">
          <div class="text-3xl font-bold text-youze-orange mb-2">{{ stats.avgDeliveryTime }}</div>
          <div class="text-youze-gray-600">Avg Delivery Time</div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { Shop } from '@/types/catalogue'

// Props from parent component
interface Props {
  shops?: Shop[]
}

const props = withDefaults(defineProps<Props>(), {
  shops: () => []
})

// Computed property to show shops with fallback data
const displayShops = computed(() => {
  if (props.shops && props.shops.length > 0) {
    return props.shops.slice(0, 12) // Limit to 12 shops for grid layout
  }
  
  // Fallback data if no shops provided
  return [
    {
      id: 1,
      name: 'Makro',
      slug: 'makro',
      logo_url: '/uploads/shops/logos/makro.jpg',
      description: 'Wholesale and retail chain',
      active: true,
      catalogue_count: 15
    },
    {
      id: 2,
      name: 'Game',
      slug: 'game',
      logo_url: '/uploads/shops/logos/game.jpg',
      description: 'Electronics and appliances',
      active: true,
      catalogue_count: 12
    },
    {
      id: 3,
      name: 'Checkers',
      slug: 'checkers',
      logo_url: '/uploads/shops/logos/checkers.jpg',
      description: 'Premium supermarket',
      active: true,
      catalogue_count: 6
    },
    {
      id: 4,
      name: 'Pick n Pay',
      slug: 'picknpay',
      logo_url: '/uploads/shops/logos/picknpay.jpg',
      description: 'Family retailer',
      active: true,
      catalogue_count: 10
    },
    {
      id: 5,
      name: 'Woolworths',
      slug: 'woolworths',
      logo_url: '/uploads/shops/logos/woolworths.jpg',
      description: 'Premium retail',
      active: true,
      catalogue_count: 7
    },
    {
      id: 6,
      name: 'Takealot',
      slug: 'takealot',
      logo_url: '/uploads/shops/logos/takealot.jpg',
      description: 'Online marketplace',
      active: true,
      catalogue_count: 20
    },
    {
      id: 7,
      name: 'Dis-Chem',
      slug: 'dischem',
      logo_url: '/uploads/shops/logos/dischem.jpg',
      description: 'Pharmacy and health',
      active: true,
      catalogue_count: 5
    },
    {
      id: 8,
      name: 'Clicks',
      slug: 'clicks',
      logo_url: '/uploads/shops/logos/clicks.jpg',
      description: 'Pharmacy and beauty',
      active: true,
      catalogue_count: 8
    },
    {
      id: 9,
      name: 'Mr Price',
      slug: 'mrprice',
      logo_url: '/uploads/shops/logos/mrprice.jpg',
      description: 'Fashion and lifestyle',
      active: true,
      catalogue_count: 12
    },
    {
      id: 10,
      name: 'Sportscene',
      slug: 'sportscene',
      logo_url: '/uploads/shops/logos/sportscene.jpg',
      description: 'Sports and lifestyle',
      active: true,
      catalogue_count: 6
    },
    {
      id: 11,
      name: 'Incredible Connection',
      slug: 'incredible',
      logo_url: '/uploads/shops/logos/incredible.jpg',
      description: 'Technology and electronics',
      active: true,
      catalogue_count: 9
    },
    {
      id: 12,
      name: 'Builders Warehouse',
      slug: 'builderswarehouse',
      logo_url: '/uploads/shops/logos/builderswarehouse.jpg',
      description: 'Home improvement',
      active: true,
      catalogue_count: 4
    }
  ]
})

const stats = ref({
  totalShops: displayShops.value.length,
  totalProducts: '50',
  totalCatalogues: displayShops.value.reduce((sum, shop) => sum + (shop.catalogue_count || 0), 0),
  avgDeliveryTime: '7-14 days'
})

const filterByShop = (shop: Shop) => {
  // Navigate to catalogues page filtered by shop
  window.location.href = `/catalogues?shop=${shop.id}`
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
  // The fallback div will show instead
}
</script>