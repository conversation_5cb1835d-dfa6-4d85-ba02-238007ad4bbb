// Catalogue document (PDF or image set)
export interface CatalogueDoc {
  id: number
  shop_slug: string       // 'makro'
  title: string
  type: 'pdf' | 'images'
  cover_url: string
  file_url?: string       // pdf url
  images?: string[]       // for image set
  page_count?: number
  updated_at: string      // ISO
}

// Shareable reference to a specific page/region
export interface DocRef {
  doc_id: number
  page?: number           // for PDF
  image_idx?: number      // for images
  region?: {x:number; y:number; w:number; h:number} // optional crop
  token: string           // short, signed token
  url: string             // generated public URI
}

// Chat message payloads
export type ChatMessage =
  | { type:'text'; text:string }
  | { type:'image'; url:string }
  | { type:'doc_ref'; ref: DocRef }
  | { type:'product_card'; product_id:number }
  | { type:'quote_card'; quote_id:number }

// Shop interface
export interface Shop {
  id: number
  name: string
  slug: string
  logo_url: string
  description?: string
  active: boolean
}

// Product interface (basic)
export interface Product {
  id: number
  name: string
  description?: string
  image_url: string
  shop_id: number
  category_id: number
  variants: ProductVariant[]
}

export interface ProductVariant {
  id: number
  product_id: number
  name: string
  sku: string
  sa_price_zar: number
}

// Quote interface
export interface Quote {
  id: number
  variant_id: number
  qty: number
  base_usd: number
  logistics_usd: number
  duties_usd: number
  markup_usd: number
  total_usd: number
  zw_price_usd: number
  fx_rate: number
  created_at: string
}