<template>
  <Head title="YouzeAfrika - Shop South Africa from Zimbabwe" />
  
  <!-- Main layout -->
  <div class="min-h-screen bg-gradient-to-b from-slate-50 to-white">
    <!-- Top Navigation -->
    <AppNavbar 
      :user="$page.props.auth.user" 
      @open-chat="openChat" 
      @open-cart="openCart" 
    />
    
    <!-- Hero Section -->
    <HeroSection @open-chat="openChat" />
    
    <!-- Shop Row -->
    <ShopsSection :shops="shops" />
    
    <!-- Catalog Explorer -->
    <CatalogueCarousel 
      :catalogues="catalogues" 
      title="Featured Catalogues"
      @open="openCatalogue"
      @share="shareCatalogue"
      @send-to-chat="sendCatalogueToChat"
    />
    
    <!-- How It Works -->
    <HowItWorksSection />
    
    <!-- Chat Widget -->
    <ChatWidget 
      v-model:open="isChatOpen"
      :conversation-id="conversationId"
      @send-message="handleChatMessage"
    />
    
    <!-- Cart Drawer -->
    <CartDrawer 
      v-model:open="isCartOpen"
      :items="cartItems"
      @request-order="handleOrderRequest"
    />
    
    <!-- Trust Section -->
    <TrustSection />
    
    <!-- Footer -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Head } from '@inertiajs/vue3'
import AppNavbar from '@/components/landing/AppNavbar.vue'
import HeroSection from '@/components/landing/HeroSection.vue'
import ShopsSection from '@/components/landing/ShopsSection.vue'
import CatalogueCarousel from '@/components/landing/CatalogueCarousel.vue'
import HowItWorksSection from '@/components/landing/HowItWorksSection.vue'
import ChatWidget from '@/components/landing/ChatWidget.vue'
import CartDrawer from '@/components/landing/CartDrawer.vue'
import TrustSection from '@/components/landing/TrustSection.vue'
import AppFooter from '@/components/landing/AppFooter.vue'

// Props from backend
interface Props {
  catalogues: CatalogueDoc[]
  shops: Shop[]
  featured_products: Product[]
}

const props = defineProps<Props>()

// State
const isChatOpen = ref(false)
const isCartOpen = ref(false)
const conversationId = ref<string | null>(null)
const cartItems = ref([])

// Methods
const openChat = () => {
  isChatOpen.value = true
}

const openCart = () => {
  isCartOpen.value = true
}

const openCatalogue = (catalogue: CatalogueDoc) => {
  // Navigate to catalogue viewer using slug
  window.open(`/catalogues/${catalogue.slug}`, '_blank')
}

const shareCatalogue = (catalogue: CatalogueDoc) => {
  // Generate shareable URI and show modal
  console.log('Share catalogue:', catalogue)
}

const sendCatalogueToChat = (catalogue: CatalogueDoc) => {
  // Send catalogue reference to chat
  openChat()
  // Add catalogue reference to chat
  console.log('Send to chat:', catalogue)
}

const handleChatMessage = (message: any) => {
  console.log('Chat message:', message)
}

const handleOrderRequest = () => {
  console.log('Order request')
}

onMounted(() => {
  // Initialize any required data
})
</script>

<style scoped>
/* Custom styles if needed */
</style>