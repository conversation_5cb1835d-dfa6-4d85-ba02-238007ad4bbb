<template>
  <div class="min-h-screen bg-gradient-to-br from-orange-50 to-orange-100">
    <!-- Header -->
    <header class="bg-white shadow-lg border-b border-orange-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between py-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Catalogues</h1>
            <p class="text-gray-600 mt-1">Browse all available catalogues from our partner shops</p>
          </div>
          
          <Link 
            href="/" 
            class="inline-flex items-center px-4 py-2 text-sm font-medium text-orange-600 bg-orange-50 border border-orange-300 rounded-md hover:bg-orange-100 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors duration-200"
          >
            <ArrowLeftIcon class="w-4 h-4 mr-2" />
            Back to Home
          </Link>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      
      <!-- Filters & Search -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="flex-1 relative">
            <MagnifyingGlassIcon class="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input 
              v-model="searchQuery"
              type="text" 
              placeholder="Search catalogues..." 
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
          </div>
          <div class="flex gap-2">
            <select v-model="selectedShop" class="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-gray-900">
              <option value="" class="text-gray-900">All Shops</option>
              <option v-for="shop in shops" :key="shop.id" :value="shop.id" class="text-gray-900">
                {{ shop.name }}
              </option>
            </select>
            <select v-model="selectedType" class="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-gray-900">
              <option value="" class="text-gray-900">All Types</option>
              <option value="pdf" class="text-gray-900">PDF</option>
              <option value="images" class="text-gray-900">Images</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Catalogues Grid -->
      <div v-if="catalogues && catalogues.data.length > 0">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <div 
            v-for="catalogue in catalogues.data" 
            :key="catalogue.id"
            class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
          >
            <!-- Catalogue Cover -->
            <div class="relative h-48 bg-gray-200">
              <img 
                :src="catalogue.cover_url" 
                :alt="catalogue.title"
                class="w-full h-full object-cover"
                @error="$event.target.src = '/images/catalogue-placeholder.jpg'"
              />
              <div class="absolute top-2 right-2">
                <span 
                  :class="getTypeBadgeColor(catalogue.type)"
                  class="px-2 py-1 text-xs font-medium rounded-full"
                >
                  {{ catalogue.type.toUpperCase() }}
                </span>
              </div>
            </div>
            
            <!-- Catalogue Info -->
            <div class="p-4">
              <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">
                {{ catalogue.title }}
              </h3>
              
              <div class="flex items-center text-sm text-gray-600 mb-2">
                <img 
                  :src="catalogue.shop.logo_url" 
                  :alt="catalogue.shop.name"
                  class="w-6 h-6 object-contain rounded mr-2"
                  @error="$event.target.style.display = 'none'"
                />
                <a 
                  v-if="catalogue.shop.website"
                  :href="catalogue.shop.website"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="font-medium text-blue-600 hover:text-blue-800 hover:underline inline-flex items-center gap-1"
                >
                  {{ catalogue.shop.name }}
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
                <span v-else class="font-medium">{{ catalogue.shop.name }}</span>
              </div>
              
              <p v-if="catalogue.description" class="text-sm text-gray-600 mb-3 line-clamp-2">
                {{ catalogue.description }}
              </p>
              
              <div class="flex justify-between items-center text-xs text-gray-500 mb-3">
                <span>{{ catalogue.page_count }} pages</span>
                <span>{{ catalogue.created_at }}</span>
              </div>
              
              <!-- Action Button -->
              <Link 
                :href="`/catalogues/${catalogue.slug}`"
                class="w-full bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors duration-200 text-center block"
              >
                View Catalogue
              </Link>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div v-if="catalogues.last_page > 1" class="mt-8">
          <div class="flex justify-center space-x-1">
            <Link
              v-for="page in catalogues.links"
              :key="page.label"
              :href="page.url"
              :class="[
                'px-3 py-2 text-sm rounded-md border',
                page.active
                  ? 'bg-orange-600 text-white border-orange-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              ]"
              v-html="page.label"
            />
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="bg-white rounded-lg shadow-lg p-12 text-center">
        <FolderIcon class="w-20 h-20 text-gray-400 mx-auto mb-6" />
        <h2 class="text-2xl font-bold text-gray-900 mb-4">No Catalogues Found</h2>
        <p class="text-gray-600 mb-6 max-w-2xl mx-auto">
          We couldn't find any catalogues matching your criteria. Try adjusting your filters or check back later.
        </p>
        <Link 
          href="/"
          class="inline-flex items-center px-6 py-3 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors duration-200"
        >
          <HomeIcon class="w-5 h-5 mr-2" />
          Go to Homepage
        </Link>
      </div>
      
    </main>
  </div>
</template>

<script setup>
import { Link } from '@inertiajs/vue3'
import { ArrowLeftIcon, FolderIcon, HomeIcon, MagnifyingGlassIcon } from '@heroicons/vue/24/outline'
import { computed, ref } from 'vue'

const props = defineProps({
  catalogues: Object,
  shops: Array,
  filters: Object
})

// Create reactive filters
const searchQuery = ref(props.filters?.search || '')
const selectedShop = ref(props.filters?.shop || '')
const selectedType = ref(props.filters?.type || '')

// Helper function to format dates
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString()
}

// Helper function to get type badge color
const getTypeBadgeColor = (type) => {
  return type === 'pdf' 
    ? 'bg-red-100 text-red-800' 
    : 'bg-blue-100 text-blue-800'
}
</script>