<template>
  <div class="min-h-screen bg-gradient-to-br from-orange-50 to-orange-100">
    <!-- Header -->
    <header class="bg-white shadow-lg border-b border-orange-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between py-6">
          <!-- Back Button & Title -->
          <div class="flex items-center space-x-4">
            <Link 
              href="/catalogues" 
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-orange-600 bg-orange-50 border border-orange-300 rounded-md hover:bg-orange-100 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors duration-200"
            >
              <ArrowLeftIcon class="w-4 h-4 mr-2" />
              Back to Catalogues
            </Link>
            
            <div>
              <h1 class="text-2xl font-bold text-gray-900">{{ catalogue.title }}</h1>
              <p v-if="catalogue.shop" class="text-sm text-gray-600">
                by {{ catalogue.shop.name }}
              </p>
            </div>
          </div>
          
          <!-- Shop Logo -->
          <div v-if="catalogue.shop?.logo_url" class="flex-shrink-0">
            <img 
              :src="catalogue.shop.logo_url" 
              :alt="catalogue.shop.name"
              class="h-12 w-auto object-contain"
            />
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        
        <!-- Catalogue Content (Left Side - 3/4 width) -->
        <div class="lg:col-span-3">
          <!-- PDF Viewer -->
          <div v-if="catalogue.type === 'pdf' && catalogue.pdf_url" class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-6">
              <h2 class="text-lg font-semibold text-gray-900 mb-4">PDF Catalogue</h2>
              <div class="w-full h-96 border border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                <div class="text-center">
                  <DocumentIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p class="text-gray-600 mb-4">PDF Viewer</p>
                  <a 
                    :href="catalogue.pdf_url" 
                    target="_blank"
                    class="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors duration-200"
                  >
                    <EyeIcon class="w-4 h-4 mr-2" />
                    Open PDF
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Image Viewer -->
          <div v-else-if="catalogue.type === 'images' && catalogue.images?.length" class="space-y-6">
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
              <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                  <h2 class="text-lg font-semibold text-gray-900">
                    Catalogue Images ({{ catalogue.images.length }} pages)
                  </h2>
                  <div class="flex items-center space-x-2">
                    <button 
                      @click="currentImageIndex = Math.max(0, currentImageIndex - 1)"
                      :disabled="currentImageIndex === 0"
                      class="p-2 text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronLeftIcon class="w-5 h-5" />
                    </button>
                    <span class="text-sm text-gray-600">
                      {{ currentImageIndex + 1 }} / {{ catalogue.images.length }}
                    </span>
                    <button 
                      @click="currentImageIndex = Math.min(catalogue.images.length - 1, currentImageIndex + 1)"
                      :disabled="currentImageIndex === catalogue.images.length - 1"
                      class="p-2 text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronRightIcon class="w-5 h-5" />
                    </button>
                  </div>
                </div>
                
                <!-- Main Image Display -->
                <div class="w-full bg-gray-50 rounded-lg flex items-center justify-center" style="min-height: 600px;">
                  <img 
                    :src="catalogue.images[currentImageIndex]" 
                    :alt="`Page ${currentImageIndex + 1}`"
                    class="max-w-full max-h-full object-contain"
                    style="max-height: 600px;"
                  />
                </div>
                
                <!-- Thumbnail Navigation -->
                <div v-if="catalogue.images.length > 1" class="mt-4 flex space-x-2 overflow-x-auto pb-2">
                  <button
                    v-for="(image, index) in catalogue.images"
                    :key="index"
                    @click="currentImageIndex = index"
                    :class="[
                      'flex-shrink-0 w-16 h-20 rounded border-2 overflow-hidden transition-all duration-200',
                      currentImageIndex === index 
                        ? 'border-orange-500 ring-2 ring-orange-200' 
                        : 'border-gray-300 hover:border-gray-400'
                    ]"
                  >
                    <img 
                      :src="image" 
                      :alt="`Thumbnail ${index + 1}`"
                      class="w-full h-full object-cover"
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- No Content Available -->
          <div v-else class="bg-white rounded-lg shadow-lg p-8 text-center">
            <ExclamationTriangleIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Content Available</h3>
            <p class="text-gray-600">This catalogue doesn't have any viewable content at the moment.</p>
          </div>
        </div>

        <!-- Sidebar (Right Side - 1/4 width) -->
        <div class="lg:col-span-1 space-y-6">
          
          <!-- Catalogue Info -->
          <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Catalogue Details</h3>
            
            <div class="space-y-3">
              <div v-if="catalogue.description">
                <p class="text-sm font-medium text-gray-700">Description</p>
                <p class="text-sm text-gray-600">{{ catalogue.description }}</p>
              </div>
              
              <div v-if="catalogue.page_count">
                <p class="text-sm font-medium text-gray-700">Pages</p>
                <p class="text-sm text-gray-600">{{ catalogue.page_count }} pages</p>
              </div>
              
              <div v-if="catalogue.valid_to">
                <p class="text-sm font-medium text-gray-700">Valid Until</p>
                <p class="text-sm text-gray-600">{{ formatDate(catalogue.valid_to) }}</p>
              </div>
              
              <div>
                <p class="text-sm font-medium text-gray-700">Type</p>
                <span :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  catalogue.type === 'pdf' 
                    ? 'bg-red-100 text-red-800' 
                    : 'bg-blue-100 text-blue-800'
                ]">
                  {{ catalogue.type?.toUpperCase() }}
                </span>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
            
            <div class="space-y-3">
              <button class="w-full flex items-center justify-center px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors duration-200">
                <ChatBubbleLeftRightIcon class="w-4 h-4 mr-2" />
                Send to Chat
              </button>
              
              <button 
                v-if="catalogue.pdf_url"
                @click="downloadCatalogue"
                class="w-full flex items-center justify-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200"
              >
                <ArrowDownTrayIcon class="w-4 h-4 mr-2" />
                Download PDF
              </button>
              
              <button class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors duration-200">
                <ShareIcon class="w-4 h-4 mr-2" />
                Share Catalogue
              </button>
            </div>
          </div>

          <!-- Shop Info -->
          <div v-if="catalogue.shop" class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Shop Information</h3>
            
            <div class="flex items-center space-x-3 mb-4">
              <img 
                v-if="catalogue.shop.logo_url"
                :src="catalogue.shop.logo_url" 
                :alt="catalogue.shop.name"
                class="w-10 h-10 object-contain"
              />
              <div>
                <p class="font-medium text-gray-900">{{ catalogue.shop.name }}</p>
                <p class="text-sm text-gray-600">@{{ catalogue.shop.slug }}</p>
              </div>
            </div>
            
            <div class="space-y-2">
              <a 
                v-if="catalogue.shop.website"
                :href="catalogue.shop.website"
                target="_blank"
                rel="noopener noreferrer"
                class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Visit {{ catalogue.shop.name }} Website
              </a>
              
              <Link 
                :href="`/shops/${catalogue.shop.slug}`"
                class="w-full flex items-center justify-center px-4 py-2 bg-orange-50 text-orange-700 border border-orange-200 rounded-md hover:bg-orange-100 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors duration-200"
              >
                <EyeIcon class="w-4 h-4 mr-2" />
                View Shop Details
              </Link>
            </div>
          </div>
          
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Link } from '@inertiajs/vue3'
import { 
  ArrowLeftIcon, 
  EyeIcon, 
  ShareIcon, 
  ChevronLeftIcon, 
  ChevronRightIcon,
  ArrowDownTrayIcon,
  ChatBubbleLeftRightIcon,
  DocumentIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'

// Props
const props = defineProps({
  catalogue: {
    type: Object,
    required: true
  }
})

// State
const currentImageIndex = ref(0)

// Methods
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('en-ZA', {
    year: 'numeric',
    month: 'long', 
    day: 'numeric'
  })
}

const downloadCatalogue = () => {
  if (props.catalogue.pdf_url) {
    window.open(props.catalogue.pdf_url, '_blank')
  }
}
</script>