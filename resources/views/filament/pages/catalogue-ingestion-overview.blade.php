<x-filament-panels::page>
<div class="space-y-6">
    {{-- Stats Overview --}}
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
        @foreach ($this->getStats() as $stat)
            <div class="fi-section-content-ctn overflow-hidden shadow rounded-xl bg-white dark:bg-gray-900 ring-1 ring-gray-950/5 dark:ring-white/10">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            @if ($stat->getColor() === 'success')
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            @elseif ($stat->getColor() === 'warning')
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            @elseif ($stat->getColor() === 'danger')
                                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            @elseif ($stat->getColor() === 'info')
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            @else
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                    </svg>
                                </div>
                            @endif
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{{ $stat->getLabel() }}</dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">{{ $stat->getValue() }}</dd>
                            </dl>
                        </div>
                    </div>
                    @if ($stat->getDescription())
                        <div class="mt-3">
                            <p class="text-sm text-gray-600 dark:text-gray-300">{{ $stat->getDescription() }}</p>
                        </div>
                    @endif
                </div>
            </div>
        @endforeach
    </div>

    {{-- Main Content Grid --}}
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {{-- Status Breakdown --}}
        <div class="fi-section-content-ctn overflow-hidden shadow rounded-xl bg-white dark:bg-gray-900 ring-1 ring-gray-950/5 dark:ring-white/10">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-semibold text-gray-900 dark:text-white mb-4">Processing Status</h3>
                @php $statusBreakdown = $this->getStatusBreakdown() @endphp
                <div class="space-y-4">
                    @foreach([
                        'pending' => ['color' => 'gray', 'label' => 'Pending'],
                        'processing' => ['color' => 'blue', 'label' => 'Processing'],
                        'extracting' => ['color' => 'indigo', 'label' => 'Extracting'],
                        'review' => ['color' => 'yellow', 'label' => 'Ready for Review'],
                        'approved' => ['color' => 'green', 'label' => 'Approved'],
                        'failed' => ['color' => 'red', 'label' => 'Failed']
                    ] as $status => $config)
                        @if($statusBreakdown[$status] > 0)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-{{ $config['color'] }}-500 rounded-full"></div>
                                <span class="text-sm font-semibold text-gray-700 dark:text-gray-200">{{ $config['label'] }}</span>
                            </div>
                            <span class="text-lg font-semibold text-{{ $config['color'] }}-600">{{ $statusBreakdown[$status] }}</span>
                        </div>
                        @endif
                    @endforeach
                </div>
            </div>
        </div>

        {{-- AI Confidence Distribution --}}
        <div class="fi-section-content-ctn overflow-hidden shadow rounded-xl bg-white dark:bg-gray-900 ring-1 ring-gray-950/5 dark:ring-white/10">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-semibold text-gray-900 dark:text-white mb-4">AI Confidence Distribution</h3>
                @php $confidence = $this->getConfidenceDistribution() @endphp
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-semibold text-gray-700 dark:text-gray-200">High Confidence (≥80%)</span>
                        <span class="text-lg font-semibold text-green-600">{{ $confidence['high'] }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-semibold text-gray-700 dark:text-gray-200">Medium Confidence (60-79%)</span>
                        <span class="text-lg font-semibold text-yellow-600">{{ $confidence['medium'] }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-semibold text-gray-700 dark:text-gray-200">Low Confidence (<60%)</span>
                        <span class="text-lg font-semibold text-red-600">{{ $confidence['low'] }}</span>
                    </div>
                    
                    {{-- Visual Distribution --}}
                    @php 
                        $total = $confidence['high'] + $confidence['medium'] + $confidence['low'];
                        $highPct = $total > 0 ? ($confidence['high'] / $total) * 100 : 0;
                        $medPct = $total > 0 ? ($confidence['medium'] / $total) * 100 : 0;
                        $lowPct = $total > 0 ? ($confidence['low'] / $total) * 100 : 0;
                    @endphp
                    <div class="mt-4">
                        <div class="flex space-x-1 h-4 bg-gray-200 rounded-full overflow-hidden">
                            @if($highPct > 0)
                                <div class="bg-green-500 h-full" style="width: {{ $highPct }}%" title="High: {{ $confidence['high'] }}"></div>
                            @endif
                            @if($medPct > 0)
                                <div class="bg-yellow-500 h-full" style="width: {{ $medPct }}%" title="Medium: {{ $confidence['medium'] }}"></div>
                            @endif
                            @if($lowPct > 0)
                                <div class="bg-red-500 h-full" style="width: {{ $lowPct }}%" title="Low: {{ $confidence['low'] }}"></div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Recent Ingestions --}}
    <div class="fi-section-content-ctn overflow-hidden shadow rounded-xl bg-white dark:bg-gray-900 ring-1 ring-gray-950/5 dark:ring-white/10">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-semibold text-gray-900 dark:text-white mb-4">Recent Processing Jobs</h3>
            <div class="fi-ta-content relative divide-y divide-gray-200 overflow-x-auto dark:divide-white/10 dark:border-t-white/10">
                <table class="fi-ta-table w-full table-auto divide-y divide-gray-200 text-start dark:divide-white/5">
                    <thead class="bg-gray-50 dark:bg-white/5">
                        <tr class="divide-x divide-gray-200 dark:divide-white/5">
                            <th class="fi-ta-header-cell px-3 py-3.5 sm:px-6 text-start">
                                <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-start">
                                    <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">Catalogue</span>
                                </span>
                            </th>
                            <th class="fi-ta-header-cell px-3 py-3.5 sm:px-6 text-start">
                                <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-start">
                                    <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">Shop</span>
                                </span>
                            </th>
                            <th class="fi-ta-header-cell px-3 py-3.5 sm:px-6 text-start">
                                <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-start">
                                    <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">Status</span>
                                </span>
                            </th>
                            <th class="fi-ta-header-cell px-3 py-3.5 sm:px-6 text-start">
                                <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-start">
                                    <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">Progress</span>
                                </span>
                            </th>
                            <th class="fi-ta-header-cell px-3 py-3.5 sm:px-6 text-start">
                                <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-start">
                                    <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">Products</span>
                                </span>
                            </th>
                            <th class="fi-ta-header-cell px-3 py-3.5 sm:px-6 text-start">
                                <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-start">
                                    <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">Confidence</span>
                                </span>
                            </th>
                            <th class="fi-ta-header-cell px-3 py-3.5 sm:px-6 text-start">
                                <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-start">
                                    <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">Age</span>
                                </span>
                            </th>
                            <th class="fi-ta-header-cell px-3 py-3.5 sm:px-6 text-start">
                                <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-start">
                                    <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">Actions</span>
                                </span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 whitespace-nowrap dark:divide-white/5">
                        @foreach ($this->getRecentIngestions() as $ingestion)
                            <tr class="fi-ta-row [@media(hover:hover)]:transition [@media(hover:hover)]:duration-75 hover:bg-gray-50 dark:hover:bg-white/5">
                                <td class="fi-ta-cell p-0 first-of-type:ps-1 last-of-type:pe-1 sm:first-of-type:ps-3 sm:last-of-type:pe-3">
                                    <div class="fi-ta-col-wrp px-3 py-4">
                                        <div class="fi-ta-text-item inline-flex items-center gap-1.5">
                                            <span class="fi-ta-text-item-label text-sm leading-6 text-gray-950 dark:text-white">
                                                {{ Str::limit($ingestion['catalogue_title'], 30) }}
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td class="fi-ta-cell p-0 first-of-type:ps-1 last-of-type:pe-1 sm:first-of-type:ps-3 sm:last-of-type:pe-3">
                                    <div class="fi-ta-col-wrp px-3 py-4">
                                        <div class="fi-ta-text-item inline-flex items-center gap-1.5">
                                            <span class="fi-ta-text-item-label text-sm leading-6 text-gray-950 dark:text-white">
                                                {{ $ingestion['shop_name'] }}
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td class="fi-ta-cell p-0 first-of-type:ps-1 last-of-type:pe-1 sm:first-of-type:ps-3 sm:last-of-type:pe-3">
                                    <div class="fi-ta-col-wrp px-3 py-4">
                                        <div class="fi-ta-text-item inline-flex items-center gap-1.5">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                @if($ingestion['status'] === 'approved') bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                                @elseif($ingestion['status'] === 'review') bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100
                                                @elseif(in_array($ingestion['status'], ['processing', 'extracting'])) bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100
                                                @elseif($ingestion['status'] === 'failed') bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                                @else bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100 @endif">
                                                {{ ucfirst($ingestion['status']) }}
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td class="fi-ta-cell p-0 first-of-type:ps-1 last-of-type:pe-1 sm:first-of-type:ps-3 sm:last-of-type:pe-3">
                                    <div class="fi-ta-col-wrp px-3 py-4">
                                        <div class="fi-ta-text-item inline-flex items-center gap-1.5">
                                            <div class="flex items-center space-x-2">
                                                <div class="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                                    <div class="bg-blue-600 dark:bg-blue-400 h-2 rounded-full" style="width: {{ min($ingestion['progress'], 100) }}%"></div>
                                                </div>
                                                <span class="text-xs text-gray-500 dark:text-gray-400">{{ round($ingestion['progress']) }}%</span>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="fi-ta-cell p-0 first-of-type:ps-1 last-of-type:pe-1 sm:first-of-type:ps-3 sm:last-of-type:pe-3">
                                    <div class="fi-ta-col-wrp px-3 py-4">
                                        <div class="fi-ta-text-item inline-flex items-center gap-1.5">
                                            <span class="fi-ta-text-item-label text-sm leading-6 text-gray-950 dark:text-white">
                                                {{ $ingestion['products_found'] }} / {{ $ingestion['products_approved'] }}
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td class="fi-ta-cell p-0 first-of-type:ps-1 last-of-type:pe-1 sm:first-of-type:ps-3 sm:last-of-type:pe-3">
                                    <div class="fi-ta-col-wrp px-3 py-4">
                                        <div class="fi-ta-text-item inline-flex items-center gap-1.5">
                                            @if($ingestion['confidence_score'])
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                                    @if($ingestion['confidence_score'] >= 80) bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                                    @elseif($ingestion['confidence_score'] >= 60) bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100
                                                    @else bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 @endif">
                                                    {{ round($ingestion['confidence_score']) }}%
                                                </span>
                                            @else
                                                <span class="text-gray-400 dark:text-gray-500">-</span>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td class="fi-ta-cell p-0 first-of-type:ps-1 last-of-type:pe-1 sm:first-of-type:ps-3 sm:last-of-type:pe-3">
                                    <div class="fi-ta-col-wrp px-3 py-4">
                                        <div class="fi-ta-text-item inline-flex items-center gap-1.5">
                                            <span class="fi-ta-text-item-label text-sm leading-6 text-gray-500 dark:text-gray-400">
                                                {{ $ingestion['created_at'] }}
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td class="fi-ta-cell p-0 first-of-type:ps-1 last-of-type:pe-1 sm:first-of-type:ps-3 sm:last-of-type:pe-3">
                                    <div class="fi-ta-col-wrp px-3 py-4">
                                        <div class="fi-ta-actions gap-3 flex items-center justify-end">
                                            <div class="fi-ta-actions-container flex shrink-0 items-center gap-3">
                                    @if($ingestion['status'] === 'pending')
                                        <button wire:click="startProcessing({{ $ingestion['id'] }})" 
                                                class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                            Start
                                        </button>
                                    @elseif($ingestion['status'] === 'review')
                                        @if(($ingestion['ready_for_import'] ?? 0) > 0)
                                            <div class="flex gap-2">
                                                <button wire:click="importApprovedProducts({{ $ingestion['id'] }})" 
                                                        class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                                    Import {{ $ingestion['ready_for_import'] }} Products
                                                </button>
                                                @if(($ingestion['products_found'] - $ingestion['products_approved']) > 0)
                                                    <button wire:click="bulkApproveProducts({{ $ingestion['id'] }})" 
                                                            class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                        Approve Remaining
                                                    </button>
                                                @endif
                                            </div>
                                        @else
                                            <button wire:click="bulkApproveProducts({{ $ingestion['id'] }})" 
                                                    class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                Approve All
                                            </button>
                                        @endif
                                    @endif
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    {{-- Available Catalogues for Processing --}}
    <div class="fi-section-content-ctn overflow-hidden shadow rounded-xl bg-white dark:bg-gray-900 ring-1 ring-gray-950/5 dark:ring-white/10">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-semibold text-gray-900 dark:text-white mb-4">Available Catalogues for Processing</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach ($this->getAvailableCatalogues() as $catalogue)
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow bg-white dark:bg-gray-800">
                        <div class="flex justify-between items-start mb-3">
                            <div class="flex-1">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ Str::limit($catalogue['title'], 25) }}</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $catalogue['shop_name'] }}</p>
                            </div>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                {{ ucfirst($catalogue['type']) }}
                            </span>
                        </div>
                        <div class="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400 mb-3">
                            <span>{{ $catalogue['page_count'] ?? 0 }} pages</span>
                            <span>{{ $catalogue['created_at'] }}</span>
                        </div>
                        <button wire:click="startNewIngestion({{ $catalogue['id'] }})" 
                                class="w-full inline-flex justify-center items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            Extract Products
                        </button>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    {{-- Pending Products Review --}}
    <div class="fi-section-content-ctn overflow-hidden shadow rounded-xl bg-white dark:bg-gray-900 ring-1 ring-gray-950/5 dark:ring-white/10">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg leading-6 font-semibold text-gray-900 dark:text-white">Products Awaiting Review</h3>
                <a href="{{ route('filament.admin.pages.product-review') }}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    View All Reviews
                </a>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach (array_slice($this->getPendingProducts(), 0, 6) as $product)
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow bg-white dark:bg-gray-800">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ Str::limit($product['name'], 30) }}</h4>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                @if($product['confidence_score'] >= 80) bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                @elseif($product['confidence_score'] >= 60) bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100
                                @else bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 @endif">
                                {{ round($product['confidence_score']) }}%
                            </span>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mb-2">{{ $product['shop_name'] }} • {{ $product['catalogue_title'] }}</p>
                        <div class="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400 mb-3">
                            @if($product['price'])
                                <span class="font-medium text-green-600 dark:text-green-400">${{ number_format($product['price'], 2) }}</span>
                            @else
                                <span class="text-gray-400 dark:text-gray-500">No price</span>
                            @endif
                            <span>Page {{ $product['page_number'] }}</span>
                        </div>
                        @if($product['brand'])
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                                {{ $product['brand'] }}
                            </span>
                        @endif
                        @if($product['category'])
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                                {{ $product['category'] }}
                            </span>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>
</x-filament-panels::page>