<x-filament-panels::page>
<div class="space-y-6">
    {{-- Stats Overview --}}
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
        @foreach ($this->getStats() as $stat)
            <div class="fi-section-content-ctn overflow-hidden shadow rounded-xl bg-white dark:bg-gray-900 ring-1 ring-gray-950/5 dark:ring-white/10">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            @if ($stat->getColor() === 'success')
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            @elseif ($stat->getColor() === 'warning')
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            @elseif ($stat->getColor() === 'danger')
                                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            @elseif ($stat->getColor() === 'info')
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            @else
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                    </svg>
                                </div>
                            @endif
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{{ $stat->getLabel() }}</dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">{{ $stat->getValue() }}</dd>
                            </dl>
                        </div>
                    </div>
                    @if ($stat->getDescription())
                        <div class="mt-3">
                            <p class="text-sm text-gray-600 dark:text-gray-300">{{ $stat->getDescription() }}</p>
                        </div>
                    @endif
                </div>
            </div>
        @endforeach
    </div>

    {{-- High Priority Products --}}
    <div class="fi-section-content-ctn overflow-hidden shadow rounded-xl bg-white dark:bg-gray-900 ring-1 ring-gray-950/5 dark:ring-white/10">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-semibold text-gray-900 dark:text-white mb-4">High Priority Products</h3>
            <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">Products with high confidence scores, valuable prices, or complete data requiring immediate attention.</p>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach (array_slice($this->getHighPriorityProducts(), 0, 9) as $product)
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">
                        <div class="flex justify-between items-start mb-3">
                            <div class="flex-1">
                                <h4 class="text-sm font-medium text-gray-900 mb-1">{{ Str::limit($product['name'], 40) }}</h4>
                                <p class="text-xs text-gray-500">{{ $product['shop_name'] }}</p>
                            </div>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                @if($product['confidence_score'] >= 90) bg-green-100 text-green-800
                                @elseif($product['confidence_score'] >= 70) bg-yellow-100 text-yellow-800
                                @else bg-red-100 text-red-800 @endif">
                                {{ round($product['confidence_score']) }}%
                            </span>
                        </div>

                        <div class="space-y-2 mb-4">
                            @if($product['price'])
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-500">Price</span>
                                    <span class="text-sm font-medium text-green-600">${{ number_format($product['price'], 2) }}</span>
                                </div>
                            @endif
                            @if($product['brand'])
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-500">Brand</span>
                                    <span class="text-sm text-gray-900">{{ $product['brand'] }}</span>
                                </div>
                            @endif
                            @if($product['sku'])
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-500">SKU</span>
                                    <span class="text-sm font-mono text-gray-700">{{ $product['sku'] }}</span>
                                </div>
                            @endif
                            @if($product['category'])
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-500">Category</span>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs bg-blue-100 text-blue-800">{{ $product['category'] }}</span>
                                </div>
                            @endif
                        </div>

                        <div class="flex flex-wrap gap-1 mb-3">
                            @foreach($product['priority_reasons'] as $reason)
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-purple-100 text-purple-800">
                                    {{ $reason }}
                                </span>
                            @endforeach
                        </div>

                        <div class="flex space-x-2">
                            <button wire:click="approveProduct({{ $product['id'] }})" 
                                    class="flex-1 inline-flex justify-center items-center px-3 py-2 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Approve
                            </button>
                            <button wire:click="rejectProduct({{ $product['id'] }})" 
                                    class="flex-1 inline-flex justify-center items-center px-3 py-2 border border-transparent text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                                Reject
                            </button>
                        </div>
                        <div class="text-xs text-gray-500 mt-2 flex justify-between">
                            <span>Page {{ $product['page_number'] }}</span>
                            <span>{{ $product['created_at'] }}</span>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    {{-- Products by Ingestion --}}
    <div class="space-y-6">
        @foreach ($this->getProductsByIngestion() as $ingestion)
            <div class="fi-section-content-ctn overflow-hidden shadow rounded-xl bg-white dark:bg-gray-900 ring-1 ring-gray-950/5 dark:ring-white/10">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <h3 class="text-lg leading-6 font-semibold text-gray-900 dark:text-white">{{ $ingestion['catalogue_title'] }}</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-300">{{ $ingestion['shop_name'] }} • {{ $ingestion['pending_count'] }} products pending review</p>
                            <p class="text-xs text-gray-500">Average confidence: {{ round($ingestion['avg_confidence']) }}% • {{ $ingestion['created_at'] }}</p>
                        </div>
                        <div class="flex space-x-2">
                            <button wire:click="bulkApproveIngestion({{ $ingestion['ingestion_id'] }})" 
                                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Approve All ({{ $ingestion['pending_count'] }})
                            </button>
                            <button wire:click="bulkRejectIngestion({{ $ingestion['ingestion_id'] }})" 
                                    class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                                Reject All
                            </button>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                        @foreach ($ingestion['products'] as $product)
                            <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow">
                                <div class="flex justify-between items-start mb-2">
                                    <h4 class="text-sm font-medium text-gray-900">{{ Str::limit($product['name'], 25) }}</h4>
                                    <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium
                                        @if($product['confidence_score'] >= 80) bg-green-100 text-green-800
                                        @elseif($product['confidence_score'] >= 60) bg-yellow-100 text-yellow-800
                                        @else bg-red-100 text-red-800 @endif">
                                        {{ round($product['confidence_score']) }}%
                                    </span>
                                </div>

                                <div class="space-y-1 mb-3 text-xs">
                                    @if($product['price'])
                                        <div class="flex justify-between">
                                            <span class="text-gray-500">Price:</span>
                                            <span class="font-medium text-green-600">${{ number_format($product['price'], 2) }}</span>
                                        </div>
                                    @endif
                                    @if($product['brand'])
                                        <div class="flex justify-between">
                                            <span class="text-gray-500">Brand:</span>
                                            <span class="text-gray-900">{{ Str::limit($product['brand'], 15) }}</span>
                                        </div>
                                    @endif
                                    @if($product['category'])
                                        <div class="flex justify-between">
                                            <span class="text-gray-500">Category:</span>
                                            <span class="text-blue-600">{{ Str::limit($product['category'], 15) }}</span>
                                        </div>
                                    @endif
                                </div>

                                <div class="flex space-x-1">
                                    <button wire:click="approveProduct({{ $product['id'] }})" 
                                            class="flex-1 inline-flex justify-center items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700">
                                        ✓
                                    </button>
                                    <button wire:click="rejectProduct({{ $product['id'] }})" 
                                            class="flex-1 inline-flex justify-center items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700">
                                        ✕
                                    </button>
                                </div>
                                <div class="text-xs text-gray-400 mt-1 text-center">
                                    Page {{ $product['page_number'] }}
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    @if(empty($this->getProductsByIngestion()))
        <div class="fi-section-content-ctn overflow-hidden shadow rounded-xl bg-white dark:bg-gray-900 ring-1 ring-gray-950/5 dark:ring-white/10">
            <div class="px-4 py-5 sm:p-6 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No products awaiting review</h3>
                <p class="mt-1 text-sm text-gray-500">All extracted products have been reviewed.</p>
                <div class="mt-6">
                    <a href="{{ route('filament.admin.pages.catalogue-ingestion-overview') }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        Start New Extraction
                    </a>
                </div>
            </div>
        </div>
    @endif
</div>
</x-filament-panels::page>