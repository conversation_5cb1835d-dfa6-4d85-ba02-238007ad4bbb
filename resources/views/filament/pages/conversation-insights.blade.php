<x-filament-panels::page>
<div class="space-y-6">
    {{-- Stats Overview --}}
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
        @foreach ($this->getStats() as $stat)
            <div class="fi-section-content-ctn overflow-hidden shadow rounded-xl bg-white dark:bg-gray-900 ring-1 ring-gray-950/5 dark:ring-white/10">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            @if ($stat->getColor() === 'success')
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            @elseif ($stat->getColor() === 'warning')
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            @elseif ($stat->getColor() === 'danger')
                                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            @else
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
                                        <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
                                    </svg>
                                </div>
                            @endif
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{{ $stat->getLabel() }}</dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white">{{ $stat->getValue() }}</dd>
                            </dl>
                        </div>
                    </div>
                    @if ($stat->getDescription())
                        <div class="mt-3">
                            <p class="text-sm text-gray-600 dark:text-gray-300">{{ $stat->getDescription() }}</p>
                        </div>
                    @endif
                </div>
            </div>
        @endforeach
    </div>

    {{-- Main Content Grid --}}
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {{-- Intent Breakdown --}}
        <div class="fi-section-content-ctn overflow-hidden shadow rounded-xl bg-white dark:bg-gray-900 ring-1 ring-gray-950/5 dark:ring-white/10">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-semibold text-gray-900 dark:text-white mb-4">Intent Detection Breakdown</h3>
                <div class="space-y-4">
                    @foreach ($this->getIntentBreakdown() as $intent => $data)
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-semibold text-gray-700 dark:text-gray-200">{{ $intent }}</span>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ $data['count'] }} conversations</span>
                                </div>
                                <div class="mt-1 flex items-center space-x-2">
                                    <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                        <div class="bg-primary-600 h-2 rounded-full" style="width: {{ min($data['completion_rate'], 100) }}%"></div>
                                    </div>
                                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400">{{ round($data['completion_rate']) }}% completion</span>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        {{-- AI Performance --}}
        <div class="fi-section-content-ctn overflow-hidden shadow rounded-xl bg-white dark:bg-gray-900 ring-1 ring-gray-950/5 dark:ring-white/10">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-semibold text-gray-900 dark:text-white mb-4">AI Performance</h3>
                @php $performance = $this->getAIPerformance() @endphp
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-semibold text-gray-700 dark:text-gray-200">AI Processed</span>
                        <span class="text-lg font-semibold text-blue-600">{{ number_format($performance['total_ai_processed']) }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-semibold text-gray-700 dark:text-gray-200">Avg Confidence</span>
                        <span class="text-lg font-semibold text-green-600">{{ $performance['avg_confidence'] }}%</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-semibold text-gray-700 dark:text-gray-200">High Confidence Rate</span>
                        <span class="text-lg font-semibold text-green-600">{{ $performance['high_confidence_rate'] }}%</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-semibold text-gray-700 dark:text-gray-200">Avg Satisfaction</span>
                        <span class="text-lg font-semibold text-purple-600">{{ $performance['avg_satisfaction'] }}/5</span>
                    </div>
                    
                    {{-- Confidence Distribution --}}
                    <div class="mt-4">
                        <div class="text-sm font-semibold text-gray-700 dark:text-gray-200 mb-2">Confidence Distribution</div>
                        <div class="flex space-x-1">
                            <div class="flex-1 bg-green-500 h-4 rounded-l-md" title="High: {{ $performance['confidence_distribution']['high'] }}"></div>
                            <div class="flex-1 bg-yellow-500 h-4" title="Medium: {{ $performance['confidence_distribution']['medium'] }}"></div>
                            <div class="flex-1 bg-red-500 h-4 rounded-r-md" title="Low: {{ $performance['confidence_distribution']['low'] }}"></div>
                        </div>
                        <div class="flex justify-between text-xs font-medium text-gray-500 dark:text-gray-400 mt-1">
                            <span>High ({{ $performance['confidence_distribution']['high'] }})</span>
                            <span>Medium ({{ $performance['confidence_distribution']['medium'] }})</span>
                            <span>Low ({{ $performance['confidence_distribution']['low'] }})</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- High Priority Conversations --}}
    <div class="fi-section-content-ctn overflow-hidden shadow rounded-xl bg-white dark:bg-gray-900 ring-1 ring-gray-950/5 dark:ring-white/10">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-semibold text-gray-900 dark:text-white mb-4">Recent High-Priority Conversations</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Contact</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Intent</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Confidence</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Messages</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Age</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach ($this->getHighPriorityConversations() as $conversation)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                    ***{{ $conversation['phone'] }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                    {{ $conversation['intent'] }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold
                                        @if($conversation['confidence'] >= 80) bg-green-100 text-green-800
                                        @elseif($conversation['confidence'] >= 60) bg-yellow-100 text-yellow-800
                                        @else bg-red-100 text-red-800 @endif">
                                        {{ $conversation['confidence'] }}%
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold
                                        @if($conversation['status'] === 'successful') bg-green-100 text-green-800
                                        @elseif($conversation['status'] === 'pending') bg-yellow-100 text-yellow-800
                                        @else bg-red-100 text-red-800 @endif">
                                        {{ ucfirst($conversation['status']) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                    {{ $conversation['message_count'] }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-600">
                                    {{ $conversation['created_at'] }}
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    {{-- Follow-up Recommendations --}}
    <div class="fi-section-content-ctn overflow-hidden shadow rounded-xl bg-white dark:bg-gray-900 ring-1 ring-gray-950/5 dark:ring-white/10">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-semibold text-gray-900 dark:text-white mb-4">Follow-up Recommendations</h3>
            <div class="space-y-4">
                @foreach (array_slice($this->getFollowUpRecommendations(), 0, 10) as $recommendation)
                    <div class="border-l-4 border-{{ $recommendation['priority'] === 'high' ? 'red' : ($recommendation['priority'] === 'medium' ? 'yellow' : 'blue') }}-400 bg-{{ $recommendation['priority'] === 'high' ? 'red' : ($recommendation['priority'] === 'medium' ? 'yellow' : 'blue') }}-50 p-4">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm font-semibold text-gray-900 dark:text-white">{{ $recommendation['phone'] }}</span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-gray-200 text-gray-800">
                                        {{ $recommendation['intent'] }}
                                    </span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        @if($recommendation['priority'] === 'high') bg-red-100 text-red-800
                                        @elseif($recommendation['priority'] === 'medium') bg-yellow-100 text-yellow-800
                                        @else bg-blue-100 text-blue-800 @endif">
                                        {{ ucfirst($recommendation['priority']) }} Priority
                                    </span>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">{{ $recommendation['reason'] }}</p>
                                <div class="flex items-center space-x-4 mt-2 text-xs font-medium text-gray-500 dark:text-gray-400">
                                    <span>{{ $recommendation['message_count'] }} messages</span>
                                    <span>{{ $recommendation['age'] }}</span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <span class="inline-flex items-center px-3 py-1 rounded-md text-sm font-semibold bg-blue-100 text-blue-800">
                                    {{ ucfirst(str_replace('_', ' ', $recommendation['action'])) }}
                                </span>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    {{-- 7-Day Trends --}}
    <div class="fi-section-content-ctn overflow-hidden shadow rounded-xl bg-white dark:bg-gray-900 ring-1 ring-gray-950/5 dark:ring-white/10">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-semibold text-gray-900 dark:text-white mb-4">7-Day Trends</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Conversations</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Intents Detected</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Completed</th>
                            <th class="px-6 py-3 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">High Confidence</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach ($this->getTrends() as $day)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">{{ $day['date'] }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $day['conversations'] }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $day['intents_detected'] }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $day['completed'] }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $day['high_confidence'] }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
</x-filament-panels::page>