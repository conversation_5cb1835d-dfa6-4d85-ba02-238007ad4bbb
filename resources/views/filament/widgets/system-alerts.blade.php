<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            System Alerts
        </x-slot>

        <div class="space-y-3">
            @forelse($alerts as $alert)
                <div class="flex items-center justify-between p-4 rounded-lg 
                    @if($alert['type'] === 'warning') bg-amber-50 border border-amber-200 dark:bg-amber-900/20 dark:border-amber-800 
                    @elseif($alert['type'] === 'info') bg-blue-50 border border-blue-200 dark:bg-blue-900/20 dark:border-blue-800
                    @elseif($alert['type'] === 'success') bg-green-50 border border-green-200 dark:bg-green-900/20 dark:border-green-800
                    @else bg-red-50 border border-red-200 dark:bg-red-900/20 dark:border-red-800 @endif">
                    
                    <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                            @if($alert['type'] === 'warning')
                                <x-heroicon-m-exclamation-triangle class="w-5 h-5 text-amber-600 dark:text-amber-400" />
                            @elseif($alert['type'] === 'info')
                                <x-heroicon-m-information-circle class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                            @elseif($alert['type'] === 'success')
                                <x-heroicon-m-check-circle class="w-5 h-5 text-green-600 dark:text-green-400" />
                            @else
                                <x-heroicon-m-x-circle class="w-5 h-5 text-red-600 dark:text-red-400" />
                            @endif
                        </div>
                        
                        <div class="flex-1">
                            <h4 class="font-medium 
                                @if($alert['type'] === 'warning') text-amber-900 dark:text-amber-100
                                @elseif($alert['type'] === 'info') text-blue-900 dark:text-blue-100
                                @elseif($alert['type'] === 'success') text-green-900 dark:text-green-100
                                @else text-red-900 dark:text-red-100 @endif">
                                {{ $alert['title'] }}
                            </h4>
                            <p class="mt-1 text-sm 
                                @if($alert['type'] === 'warning') text-amber-700 dark:text-amber-300
                                @elseif($alert['type'] === 'info') text-blue-700 dark:text-blue-300
                                @elseif($alert['type'] === 'success') text-green-700 dark:text-green-300
                                @else text-red-700 dark:text-red-300 @endif">
                                {{ $alert['message'] }}
                            </p>
                        </div>
                    </div>
                    
                    @if(isset($alert['action']) && isset($alert['action_url']))
                        <div class="flex-shrink-0">
                            <a href="{{ $alert['action_url'] }}" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md border 
                                @if($alert['type'] === 'warning') border-amber-300 text-amber-800 hover:bg-amber-100 dark:border-amber-700 dark:text-amber-200 dark:hover:bg-amber-900/50
                                @elseif($alert['type'] === 'info') border-blue-300 text-blue-800 hover:bg-blue-100 dark:border-blue-700 dark:text-blue-200 dark:hover:bg-blue-900/50
                                @elseif($alert['type'] === 'success') border-green-300 text-green-800 hover:bg-green-100 dark:border-green-700 dark:text-green-200 dark:hover:bg-green-900/50
                                @else border-red-300 text-red-800 hover:bg-red-100 dark:border-red-700 dark:text-red-200 dark:hover:bg-red-900/50 @endif">
                                {{ $alert['action'] }}
                            </a>
                        </div>
                    @endif
                </div>
            @empty
                <div class="flex items-center justify-center p-8 text-gray-500 dark:text-gray-400">
                    <div class="text-center">
                        <x-heroicon-o-check-circle class="w-8 h-8 mx-auto mb-2 text-green-500" />
                        <p class="text-sm font-medium">All systems running smoothly!</p>
                    </div>
                </div>
            @endforelse
        </div>
    </x-filament::section>
</x-filament-widgets::widget>