<div class="space-y-4">
    @if($catalogue->type === 'images' && $catalogue->images)
        <div class="grid grid-cols-1 gap-4">
            @foreach($catalogue->images as $index => $image)
                <div class="relative">
                    <p class="text-sm text-gray-600 mb-2">Page {{ $index + 1 }}</p>
                    <img 
                        src="{{ $image }}" 
                        alt="Page {{ $index + 1 }}" 
                        class="w-full h-auto rounded-lg shadow-lg"
                        loading="lazy"
                    />
                </div>
            @endforeach
        </div>
    @elseif($catalogue->type === 'pdf' && $catalogue->pdf_url)
        <div class="h-96">
            <iframe 
                src="{{ $catalogue->pdf_url }}" 
                class="w-full h-full rounded-lg"
                title="{{ $catalogue->title }}"
            ></iframe>
        </div>
        <div class="mt-4">
            <a 
                href="{{ $catalogue->pdf_url }}" 
                target="_blank"
                class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
            >
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Download PDF
            </a>
        </div>
    @else
        <p class="text-gray-500">No preview available</p>
    @endif
    
    <div class="border-t pt-4 mt-6">
        <dl class="grid grid-cols-2 gap-4 text-sm">
            <div>
                <dt class="font-medium text-gray-600">Shop:</dt>
                <dd class="text-gray-900">
                    @if($catalogue->shop && $catalogue->shop->website)
                        <a href="{{ $catalogue->shop->website }}" 
                           target="_blank" 
                           rel="noopener noreferrer"
                           class="text-blue-600 hover:text-blue-800 hover:underline inline-flex items-center gap-1">
                            {{ $catalogue->shop->name }}
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                        </a>
                    @else
                        {{ $catalogue->shop->name ?? 'N/A' }}
                    @endif
                </dd>
            </div>
            <div>
                <dt class="font-medium text-gray-600">Type:</dt>
                <dd class="text-gray-900">{{ ucfirst($catalogue->type) }}</dd>
            </div>
            <div>
                <dt class="font-medium text-gray-600">Pages:</dt>
                <dd class="text-gray-900">{{ $catalogue->page_count ?? 'N/A' }}</dd>
            </div>
            @if($catalogue->valid_from)
                <div>
                    <dt class="font-medium text-gray-600">Valid From:</dt>
                    <dd class="text-gray-900">{{ $catalogue->valid_from->format('M d, Y') }}</dd>
                </div>
            @endif
            @if($catalogue->valid_to)
                <div>
                    <dt class="font-medium text-gray-600">Valid Until:</dt>
                    <dd class="text-gray-900">{{ $catalogue->valid_to->format('M d, Y') }}</dd>
                </div>
            @endif
        </dl>
    </div>
</div>