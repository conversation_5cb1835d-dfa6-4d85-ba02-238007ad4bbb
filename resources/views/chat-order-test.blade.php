<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Order Test - YouzeAfrika</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto p-4">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-center mb-8">Chat Order Test Interface</h1>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Chat Interface -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-semibold mb-4">Chat Simulator</h2>
                    
                    <div id="chat-messages" class="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto mb-4">
                        <div class="text-center text-gray-500">Start chatting...</div>
                    </div>
                    
                    <div class="flex gap-2">
                        <input type="text" id="message-input" placeholder="Type your message..." 
                               class="flex-1 border rounded-lg px-3 py-2">
                        <button onclick="sendMessage()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                            Send
                        </button>
                    </div>
                    
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Upload Image</label>
                        <input type="file" id="image-input" accept="image/*" class="block w-full text-sm">
                        <button onclick="sendImage()" class="mt-2 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">
                            Send Image
                        </button>
                    </div>
                </div>
                
                <!-- Session Info -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-semibold mb-4">Session Information</h2>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Session ID</label>
                        <input type="text" id="session-id" value="test-session-{{ time() }}" 
                               class="w-full border rounded-lg px-3 py-2">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <input type="text" id="phone-number" placeholder="+1234567890" 
                               class="w-full border rounded-lg px-3 py-2">
                    </div>
                    
                    <div class="space-y-2">
                        <button onclick="getSessionInfo()" class="w-full bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                            Get Session Info
                        </button>
                        <button onclick="calculateCost()" class="w-full bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600">
                            Calculate Cost
                        </button>
                        <button onclick="confirmOrder()" class="w-full bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">
                            Confirm Order
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Response Display -->
            <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <h2 class="text-xl font-semibold mb-4">Latest Response</h2>
                <pre id="response-display" class="bg-gray-50 rounded-lg p-4 text-sm overflow-auto max-h-64">
No response yet...
                </pre>
            </div>
            
            <!-- Quick Test Buttons -->
            <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <h2 class="text-xl font-semibold mb-4">Quick Tests</h2>
                <div class="flex flex-wrap gap-2">
                    <button onclick="quickTest('Hello')" class="bg-blue-500 text-white px-3 py-1 rounded">Hello</button>
                    <button onclick="quickTest('iPhone case')" class="bg-blue-500 text-white px-3 py-1 rounded">Search iPhone case</button>
                    <button onclick="quickTest('laptop')" class="bg-blue-500 text-white px-3 py-1 rounded">Search laptop</button>
                    <button onclick="quickTest('1')" class="bg-green-500 text-white px-3 py-1 rounded">Select Item 1</button>
                    <button onclick="quickTest('calculate')" class="bg-orange-500 text-white px-3 py-1 rounded">Calculate</button>
                    <button onclick="quickTest('confirm')" class="bg-red-500 text-white px-3 py-1 rounded">Confirm</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const sessionId = document.getElementById('session-id').value;
        
        function addMessage(sender, message, isBot = false) {
            const chatMessages = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `mb-2 ${isBot ? 'text-blue-600' : 'text-gray-800'}`;
            messageDiv.innerHTML = `<strong>${sender}:</strong> <span class="whitespace-pre-wrap">${message}</span>`;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        function displayResponse(response) {
            document.getElementById('response-display').textContent = JSON.stringify(response, null, 2);
        }
        
        async function sendMessage() {
            const messageInput = document.getElementById('message-input');
            const message = messageInput.value.trim();
            if (!message) return;
            
            addMessage('You', message);
            messageInput.value = '';
            
            try {
                const response = await fetch('/api/v1/chat-orders/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        session_id: document.getElementById('session-id').value,
                        message: message,
                        channel: 'chat',
                        customer_phone: document.getElementById('phone-number').value
                    })
                });
                
                const data = await response.json();
                displayResponse(data);
                
                if (data.message) {
                    addMessage('Bot', data.message, true);
                }
                
            } catch (error) {
                console.error('Error:', error);
                addMessage('System', 'Error: ' + error.message);
            }
        }
        
        async function sendImage() {
            const imageInput = document.getElementById('image-input');
            const file = imageInput.files[0];
            if (!file) return;
            
            addMessage('You', `[Image: ${file.name}]`);
            
            const formData = new FormData();
            formData.append('session_id', document.getElementById('session-id').value);
            formData.append('image', file);
            formData.append('channel', 'chat');
            
            try {
                const response = await fetch('/api/v1/chat-orders/message', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: formData
                });
                
                const data = await response.json();
                displayResponse(data);
                
                if (data.message) {
                    addMessage('Bot', data.message, true);
                }
                
            } catch (error) {
                console.error('Error:', error);
                addMessage('System', 'Error: ' + error.message);
            }
        }
        
        async function getSessionInfo() {
            try {
                const response = await fetch(`/api/v1/chat-orders/${document.getElementById('session-id').value}`);
                const data = await response.json();
                displayResponse(data);
            } catch (error) {
                console.error('Error:', error);
            }
        }
        
        async function calculateCost() {
            try {
                const response = await fetch(`/api/v1/chat-orders/${document.getElementById('session-id').value}/calculate`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });
                const data = await response.json();
                displayResponse(data);
                
                if (data.formatted) {
                    addMessage('Bot', data.formatted, true);
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }
        
        async function confirmOrder() {
            try {
                const response = await fetch(`/api/v1/chat-orders/${document.getElementById('session-id').value}/confirm`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        customer_name: 'Test Customer',
                        customer_phone: document.getElementById('phone-number').value
                    })
                });
                const data = await response.json();
                displayResponse(data);
                
                if (data.message) {
                    addMessage('Bot', data.message, true);
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }
        
        function quickTest(message) {
            document.getElementById('message-input').value = message;
            sendMessage();
        }
        
        // Allow Enter key to send message
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Initialize chat
        window.onload = function() {
            addMessage('System', 'Chat interface loaded. Start by saying "Hello"!');
        }
    </script>
</body>
</html>