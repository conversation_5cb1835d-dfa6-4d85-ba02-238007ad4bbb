<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>WhatsApp Bot Conversation Tester - Youzeafrika</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(180deg, #00a884 0%, #00a884 127px, #d9dbd5 127px, #d9dbd5 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            height: 95vh;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
        }

        .sidebar {
            width: 300px;
            background: #f0f2f5;
            border-right: 1px solid #e9edef;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            background: #00a884;
            color: white;
        }

        .sidebar-header h3 {
            font-size: 18px;
            font-weight: 500;
        }

        .test-scenarios {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }

        .scenario-btn {
            display: block;
            width: 100%;
            padding: 12px;
            margin-bottom: 10px;
            background: white;
            border: 1px solid #e9edef;
            border-radius: 8px;
            cursor: pointer;
            text-align: left;
            transition: background 0.2s;
        }

        .scenario-btn:hover {
            background: #f8f9fa;
        }

        .scenario-btn.active {
            background: #00a884;
            color: white;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 16px 20px;
            background: #f0f2f5;
            border-bottom: 1px solid #e9edef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-header h2 {
            font-size: 16px;
            font-weight: 500;
        }

        .phone-input {
            padding: 8px 12px;
            border: 1px solid #e9edef;
            border-radius: 5px;
            font-size: 14px;
        }

        .chat-controls {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            background: #00a884;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #008f6f;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAATklEQVQYV2N89+4dAzYwCkr8h4kzMWABjEDFUHEkRSCFIHFkRSCFIDaKQpBAIAuJUs0IksDlJJI8RpLCkZeXB3Mg0aYjByhRvmNNogDZFBsK6m1uvwAAAABJRU5ErkJggg==');
        }

        .message {
            max-width: 65%;
            margin-bottom: 12px;
            clear: both;
            display: flex;
            align-items: flex-end;
        }

        .message.user {
            justify-content: flex-end;
            margin-left: auto;
        }

        .message.assistant {
            justify-content: flex-start;
            margin-right: auto;
        }

        .message-bubble {
            padding: 8px 12px;
            border-radius: 7.5px;
            position: relative;
            word-wrap: break-word;
            max-width: 100%;
        }

        .message.user .message-bubble {
            background: #d9fdd3;
            border-top-right-radius: 2px;
            margin-left: 50px;
        }

        .message.assistant .message-bubble {
            background: white;
            border-top-left-radius: 2px;
            box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
            margin-right: 50px;
        }

        .message.user .message-time {
            text-align: right;
        }

        .message.assistant .message-time {
            text-align: left;
        }

        .message-time {
            font-size: 11px;
            color: #667781;
            margin-top: 4px;
            text-align: right;
        }

        .input-container {
            padding: 10px 20px;
            background: #f0f2f5;
            border-top: 1px solid #e9edef;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 10px 12px;
            border: 1px solid #e9edef;
            border-radius: 21px;
            outline: none;
            font-size: 15px;
            background: white;
        }

        .send-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #00a884;
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }

        .send-btn:hover {
            background: #008f6f;
        }

        .context-panel {
            width: 300px;
            background: #f8f9fa;
            border-left: 1px solid #e9edef;
            padding: 20px;
            overflow-y: auto;
        }

        .context-panel h3 {
            font-size: 16px;
            margin-bottom: 15px;
            color: #075e54;
        }

        .context-item {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 5px;
            font-size: 13px;
        }

        .context-label {
            font-weight: bold;
            color: #667781;
            margin-bottom: 5px;
        }

        .loading {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #667781;
            border-radius: 50%;
            animation: pulse 1.5s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 60%, 100% { opacity: 1; }
            30% { opacity: 0.1; }
        }

        .typing-indicator {
            padding: 8px 12px;
            background: white;
            border-radius: 7.5px;
            border-top-left-radius: 0;
            box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
            display: inline-block;
        }

        .typing-indicator span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #667781;
            margin: 0 2px;
            animation: typing 1.4s infinite;
        }

        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-indicator span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(1);
                opacity: 0.5;
            }
            40% {
                transform: scale(1.3);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>Test Scenarios</h3>
            </div>
            <div class="test-scenarios">
                <button class="scenario-btn" onclick="runScenario('greeting')">
                    <strong>Greeting Flow</strong>
                    <div style="font-size: 12px; color: #667781; margin-top: 4px;">Test welcome messages</div>
                </button>
                <button class="scenario-btn" onclick="runScenario('product')">
                    <strong>Product Search</strong>
                    <div style="font-size: 12px; color: #667781; margin-top: 4px;">Search for products</div>
                </button>
                <button class="scenario-btn" onclick="runScenario('pricing')">
                    <strong>Pricing & Delivery</strong>
                    <div style="font-size: 12px; color: #667781; margin-top: 4px;">Get quotes with delivery</div>
                </button>
                <button class="scenario-btn" onclick="runScenario('order')">
                    <strong>Complete Order</strong>
                    <div style="font-size: 12px; color: #667781; margin-top: 4px;">Full order flow</div>
                </button>
                <button class="scenario-btn" onclick="runScenario('support')">
                    <strong>Human Support</strong>
                    <div style="font-size: 12px; color: #667781; margin-top: 4px;">Escalation to agent</div>
                </button>

                <hr style="margin: 20px 0; border: none; border-top: 1px solid #e9edef;">

                <div style="padding: 0 5px;">
                    <div style="font-size: 12px; color: #667781; margin-bottom: 10px;">Quick Messages:</div>
                    <button class="scenario-btn" onclick="sendQuickMessage('hello')">Hello</button>
                    <button class="scenario-btn" onclick="sendQuickMessage('help')">Help</button>
                    <button class="scenario-btn" onclick="sendQuickMessage('Samsung TV')">Samsung TV</button>
                    <button class="scenario-btn" onclick="sendQuickMessage('iPhone 15')">iPhone 15</button>
                    <button class="scenario-btn" onclick="sendQuickMessage('agent')">Talk to Agent</button>
                </div>
            </div>
        </div>

        <div class="chat-container">
            <div class="chat-header">
                <h2>WhatsApp Bot Tester</h2>
                <div class="chat-controls">
                    <input type="text" id="phone" class="phone-input" value="whatsapp:263775240298" placeholder="Phone number">
                    <button class="btn" onclick="loadHistory()">Load History</button>
                    <button class="btn btn-danger" onclick="resetConversation()">Reset</button>
                </div>
            </div>

            <div class="messages-container" id="messages">
                <div style="text-align: center; padding: 50px; color: #667781;">
                    <h3>Welcome to WhatsApp Bot Tester</h3>
                    <p style="margin-top: 10px;">Start typing or select a test scenario</p>
                </div>
            </div>

            <div id="typing-indicator" style="display: none; padding: 0 20px;">
                <div class="message assistant">
                    <div class="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>

            <div class="input-container">
                <input type="text" id="message-input" class="message-input" placeholder="Type a message..." onkeypress="handleKeyPress(event)">
                <button class="send-btn" onclick="sendMessage()">
                    <svg viewBox="0 0 24 24" width="24" height="24">
                        <path fill="currentColor" d="M1.101 21.757L23.8 12.028 1.101 2.3l.011 7.912 13.623 1.816-13.623 1.817-.011 7.912z"></path>
                    </svg>
                </button>
            </div>
        </div>

        <div class="context-panel">
            <h3>Conversation Context</h3>
            <div id="context-info">
                <div class="context-item">
                    <div class="context-label">Stage:</div>
                    <div id="context-stage">Not started</div>
                </div>
                <div class="context-item">
                    <div class="context-label">Status:</div>
                    <div id="context-status">New</div>
                </div>
                <div class="context-item">
                    <div class="context-label">Context Data:</div>
                    <pre id="context-data" style="font-size: 11px; overflow-x: auto;">None</pre>
                </div>
                <div class="context-item">
                    <div class="context-label">Cart ID:</div>
                    <div id="context-cart">None</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const messagesContainer = document.getElementById('messages');
        const messageInput = document.getElementById('message-input');
        const phoneInput = document.getElementById('phone');
        const typingIndicator = document.getElementById('typing-indicator');

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Add user message to chat
            addMessage('user', message);
            messageInput.value = '';

            // Show typing indicator
            showTyping();

            try {
                const response = await fetch('/api/conversation-test/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        phone: phoneInput.value,
                        message: message
                    })
                });

                const data = await response.json();

                // Hide typing indicator
                hideTyping();

                if (data.success) {
                    // Add bot responses
                    if (data.responses && data.responses.length > 0) {
                        data.responses.forEach(response => {
                            addMessage('assistant', response.content);
                        });
                    }

                    // Update context
                    updateContext(data.context);
                } else {
                    addMessage('assistant', 'Sorry, an error occurred: ' + data.error);
                }

            } catch (error) {
                hideTyping();
                addMessage('assistant', 'Connection error. Please try again.');
                console.error('Error:', error);
            }
        }

        function addMessage(sender, text) {
            // Clear welcome message if exists
            if (messagesContainer.querySelector('div[style*="text-align: center"]')) {
                messagesContainer.innerHTML = '';
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'message-bubble';

            // Add sender label for clarity
            if (sender === 'assistant') {
                const senderLabel = document.createElement('div');
                senderLabel.style.fontSize = '10px';
                senderLabel.style.color = '#667781';
                senderLabel.style.marginBottom = '2px';
                senderLabel.textContent = '🤖 Bot';
                messageDiv.appendChild(senderLabel);
            } else if (sender === 'user') {
                const senderLabel = document.createElement('div');
                senderLabel.style.fontSize = '10px';
                senderLabel.style.color = '#667781';
                senderLabel.style.marginBottom = '2px';
                senderLabel.style.textAlign = 'right';
                senderLabel.textContent = 'You 👤';
                messageDiv.appendChild(senderLabel);
            }

            // Convert line breaks and format text
            bubbleDiv.innerHTML = formatMessage(text);

            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

            messageDiv.appendChild(bubbleDiv);
            messageDiv.appendChild(timeDiv);

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // Add a small delay to make the message appearance more natural
            bubbleDiv.style.opacity = '0';
            bubbleDiv.style.transform = 'scale(0.8)';
            setTimeout(() => {
                bubbleDiv.style.transition = 'all 0.2s ease';
                bubbleDiv.style.opacity = '1';
                bubbleDiv.style.transform = 'scale(1)';
            }, 50);
        }

        function formatMessage(text) {
            // Convert markdown-style formatting
            return text
                .replace(/\n/g, '<br>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/•/g, '&bull;');
        }

        function showTyping() {
            typingIndicator.style.display = 'block';
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function hideTyping() {
            typingIndicator.style.display = 'none';
        }

        async function resetConversation() {
            if (!confirm('Reset conversation history?')) return;

            try {
                const response = await fetch('/api/conversation-test/reset', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        phone: phoneInput.value
                    })
                });

                const data = await response.json();

                if (data.success) {
                    messagesContainer.innerHTML = `
                        <div style="text-align: center; padding: 50px; color: #667781;">
                            <h3>Conversation Reset</h3>
                            <p style="margin-top: 10px;">Start fresh!</p>
                        </div>
                    `;
                    updateContext(null);
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }

        async function loadHistory() {
            try {
                const response = await fetch('/api/conversation-test/history', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        phone: phoneInput.value
                    })
                });

                const data = await response.json();

                if (data.success && data.messages) {
                    messagesContainer.innerHTML = '';
                    data.messages.forEach(msg => {
                        addMessage(msg.sender, msg.message);
                    });
                    updateContext(data.context);
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }

        function updateContext(context) {
            if (context) {
                document.getElementById('context-stage').textContent = context.stage || 'Not set';
                document.getElementById('context-status').textContent = context.status || 'Active';
                document.getElementById('context-data').textContent = context.context ? JSON.stringify(context.context, null, 2) : 'None';
                document.getElementById('context-cart').textContent = context.cart_id || 'None';
            } else {
                document.getElementById('context-stage').textContent = 'Not started';
                document.getElementById('context-status').textContent = 'New';
                document.getElementById('context-data').textContent = 'None';
                document.getElementById('context-cart').textContent = 'None';
            }
        }

        async function runScenario(scenario) {
            const scenarios = {
                greeting: ['hello', 'how are you?', 'what can you do?'],
                product: ['hello', 'I need a Samsung TV', 'what sizes do you have?', 'price for 55 inch'],
                pricing: ['iPhone 15 Pro', 'how much with delivery to Harare?', 'what about Bulawayo?'],
                order: ['Samsung Galaxy S24', 'add to cart', 'checkout', 'confirm order'],
                support: ['hello', 'I have a problem with my order', 'I need to speak to someone', 'agent']
            };

            const messages = scenarios[scenario];
            if (!messages) return;

            // Clear current messages
            messagesContainer.innerHTML = '';

            for (const msg of messages) {
                // Add user message
                addMessage('user', msg);

                // Show typing
                showTyping();

                // Send message
                await new Promise(resolve => setTimeout(resolve, 1000));

                try {
                    const response = await fetch('/api/conversation-test/send', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        },
                        body: JSON.stringify({
                            phone: phoneInput.value,
                            message: msg
                        })
                    });

                    const data = await response.json();
                    hideTyping();

                    if (data.success && data.responses) {
                        data.responses.forEach(response => {
                            addMessage('assistant', response.content);
                        });
                        updateContext(data.context);
                    }
                } catch (error) {
                    hideTyping();
                    console.error('Error:', error);
                }

                // Wait before next message
                await new Promise(resolve => setTimeout(resolve, 1500));
            }
        }

        function sendQuickMessage(message) {
            messageInput.value = message;
            sendMessage();
        }

        // Load history on page load
        window.addEventListener('load', loadHistory);
    </script>
</body>
</html>