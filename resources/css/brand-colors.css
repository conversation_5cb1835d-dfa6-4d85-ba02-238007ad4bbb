/* YouzeAfrika Brand Colors extracted from logo */
:root {
  /* Primary Brand Colors */
  --youze-navy: #1e2a3a;
  --youze-navy-light: #2d3748;
  --youze-navy-dark: #0f1419;
  
  /* Orange Gradient */
  --youze-orange: #f97316;
  --youze-orange-light: #fb923c;
  --youze-orange-dark: #ea580c;
  --youze-orange-50: #fff7ed;
  --youze-orange-100: #ffedd5;
  --youze-orange-200: #fed7aa;
  
  /* Coral/Pink */
  --youze-coral: #fb7185;
  --youze-coral-light: #fda4af;
  --youze-coral-dark: #f43f5e;
  --youze-coral-50: #fdf2f8;
  --youze-coral-100: #fce7f3;
  
  /* Supporting Colors with Better Contrast */
  --youze-white: #ffffff;
  --youze-gray-50: #f9fafb;
  --youze-gray-100: #f3f4f6;
  --youze-gray-200: #e5e7eb;
  --youze-gray-300: #d1d5db;
  --youze-gray-400: #9ca3af;
  --youze-gray-500: #6b7280;
  --youze-gray-600: #4b5563;
  --youze-gray-700: #374151;
  --youze-gray-800: #1f2937;
  --youze-gray-900: #111827;
  
  /* Semantic Colors */
  --youze-success: #10b981;
  --youze-warning: #f59e0b;
  --youze-error: #ef4444;
  --youze-info: #3b82f6;
}

/* Custom TailwindCSS utilities */
.bg-youze-navy { background-color: var(--youze-navy) !important; }
.bg-youze-navy-light { background-color: var(--youze-navy-light) !important; }
.bg-youze-orange { background-color: var(--youze-orange) !important; }
.bg-youze-orange-light { background-color: var(--youze-orange-light) !important; }
.bg-youze-orange-50 { background-color: var(--youze-orange-50) !important; }
.bg-youze-orange-100 { background-color: var(--youze-orange-100) !important; }
.bg-youze-coral { background-color: var(--youze-coral) !important; }
.bg-youze-coral-50 { background-color: var(--youze-coral-50) !important; }

.text-youze-navy { color: var(--youze-navy) !important; }
.text-youze-orange { color: var(--youze-orange) !important; }
.text-youze-coral { color: var(--youze-coral) !important; }
.text-youze-gray-600 { color: var(--youze-gray-600) !important; }
.text-youze-gray-700 { color: var(--youze-gray-700) !important; }
.text-youze-gray-900 { color: var(--youze-gray-900) !important; }

.border-youze-navy { border-color: var(--youze-navy) !important; }
.border-youze-orange { border-color: var(--youze-orange) !important; }
.border-youze-coral { border-color: var(--youze-coral) !important; }
.border-youze-gray-200 { border-color: var(--youze-gray-200) !important; }

.hover\:bg-youze-orange-100:hover { background-color: var(--youze-orange-100) !important; }
.hover\:text-youze-orange:hover { color: var(--youze-orange) !important; }
.hover\:border-youze-orange:hover { border-color: var(--youze-orange) !important; }

.from-youze-orange { --tw-gradient-from: var(--youze-orange) var(--tw-gradient-from-position); }
.to-youze-coral { --tw-gradient-to: var(--youze-coral) var(--tw-gradient-to-position); }
.from-youze-navy { --tw-gradient-from: var(--youze-navy) var(--tw-gradient-from-position); }
.to-youze-navy-dark { --tw-gradient-to: var(--youze-navy-dark) var(--tw-gradient-to-position); }

/* Focus states */
.focus\:ring-youze-orange:focus { --tw-ring-color: var(--youze-orange); }
.focus\:border-youze-orange:focus { border-color: var(--youze-orange) !important; }