<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Pricing Configuration
    |--------------------------------------------------------------------------
    |
    | Configure pricing calculations for the YouzeAfrika system
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Markup Configuration
    |--------------------------------------------------------------------------
    */
    'markup_percentage' => env('MARKUP_PERCENTAGE', 50),
    'dynamic_markup' => env('DYNAMIC_MARKUP_ENABLED', false),

    // Tiered markup based on product price
    'markup_tiers' => [
        'low' => ['max_price' => 100, 'percentage' => 60],    // Under $100 USD
        'medium' => ['max_price' => 500, 'percentage' => 50], // $100-500 USD
        'high' => ['max_price' => 1000, 'percentage' => 40],  // $500-1000 USD
        'premium' => ['max_price' => null, 'percentage' => 30], // Over $1000 USD
    ],

    /*
    |--------------------------------------------------------------------------
    | Shipping Configuration
    |--------------------------------------------------------------------------
    */
    'base_shipping_usd' => env('BASE_SHIPPING_USD', 10),
    'shipping_per_kg_usd' => env('SHIPPING_PER_KG_USD', 2),
    'free_shipping_threshold' => env('FREE_SHIPPING_THRESHOLD_USD', 200),

    // Shipping zones
    'shipping_zones' => [
        'harare' => ['base_cost' => 10, 'per_kg' => 2],
        'bulawayo' => ['base_cost' => 12, 'per_kg' => 2.5],
        'other_cities' => ['base_cost' => 15, 'per_kg' => 3],
    ],

    /*
    |--------------------------------------------------------------------------
    | Duties and Taxes
    |--------------------------------------------------------------------------
    */
    'duties_percentage' => env('DUTIES_PERCENTAGE', 25), // Zimbabwe import duty
    'vat_percentage' => env('VAT_PERCENTAGE', 15),       // Zimbabwe VAT
    'apply_duties_threshold' => env('DUTIES_THRESHOLD_USD', 0), // Apply duties from $0

    /*
    |--------------------------------------------------------------------------
    | Fees Configuration
    |--------------------------------------------------------------------------
    */
    'handling_fee_usd' => env('HANDLING_FEE_USD', 5),
    'payment_processing_percentage' => env('PAYMENT_PROCESSING_PERCENTAGE', 3),
    'insurance_percentage' => env('INSURANCE_PERCENTAGE', 1),
    'documentation_fee_usd' => env('DOCUMENTATION_FEE_USD', 2),

    /*
    |--------------------------------------------------------------------------
    | Bulk Discounts
    |--------------------------------------------------------------------------
    */
    'bulk_discount_enabled' => env('BULK_DISCOUNT_ENABLED', true),
    'bulk_discount_tiers' => [
        5 => 0.05,   // 5% discount for 5+ items
        10 => 0.10,  // 10% discount for 10+ items
        20 => 0.15,  // 15% discount for 20+ items
        50 => 0.20,  // 20% discount for 50+ items
    ],

    /*
    |--------------------------------------------------------------------------
    | Quotation Configuration
    |--------------------------------------------------------------------------
    */
    'quotation_validity_days' => env('QUOTATION_VALIDITY_DAYS', 3),
    'quotation_auto_expire' => env('QUOTATION_AUTO_EXPIRE', true),
    'quotation_reminder_hours' => env('QUOTATION_REMINDER_HOURS', 24), // Remind before expiry

    /*
    |--------------------------------------------------------------------------
    | Special Pricing Rules
    |--------------------------------------------------------------------------
    */
    'minimum_order_usd' => env('MINIMUM_ORDER_USD', 20),
    'maximum_single_item_usd' => env('MAXIMUM_SINGLE_ITEM_USD', 5000),
    'high_value_threshold' => env('HIGH_VALUE_THRESHOLD_USD', 500), // For escalation

    /*
    |--------------------------------------------------------------------------
    | Promotions and Discounts
    |--------------------------------------------------------------------------
    */
    'first_time_customer_discount' => env('FIRST_TIME_DISCOUNT_PERCENTAGE', 5),
    'loyalty_discount_enabled' => env('LOYALTY_DISCOUNT_ENABLED', true),
    'seasonal_discount_percentage' => env('SEASONAL_DISCOUNT_PERCENTAGE', 0),

    /*
    |--------------------------------------------------------------------------
    | Weight Estimation (for products without weight data)
    |--------------------------------------------------------------------------
    */
    'weight_estimation' => [
        'default_weight_kg' => 2.0,
        'category_weights' => [
            'electronics' => 1.5,
            'clothing' => 0.5,
            'books' => 0.3,
            'appliances' => 5.0,
            'furniture' => 10.0,
            'toys' => 1.0,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Currency and Exchange
    |--------------------------------------------------------------------------
    */
    'base_currency' => 'USD',
    'source_currency' => 'ZAR',
    'display_currencies' => ['USD', 'ZAR'],
    'exchange_rate_markup' => env('EXCHANGE_RATE_MARKUP_PERCENTAGE', 2), // 2% buffer on exchange rates

    /*
    |--------------------------------------------------------------------------
    | Risk Assessment
    |--------------------------------------------------------------------------
    */
    'risk_assessment' => [
        'high_value_manual_review' => env('HIGH_VALUE_MANUAL_REVIEW', true),
        'new_customer_limit_usd' => env('NEW_CUSTOMER_LIMIT_USD', 300),
        'fraud_detection_enabled' => env('FRAUD_DETECTION_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Pricing Display
    |--------------------------------------------------------------------------
    */
    'display' => [
        'show_breakdown' => env('SHOW_PRICE_BREAKDOWN', true),
        'round_to_nearest' => env('ROUND_TO_NEAREST', 1), // Round to nearest dollar
        'show_savings' => env('SHOW_SAVINGS_CALCULATION', true),
        'compare_with_local' => env('COMPARE_WITH_LOCAL_PRICES', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Settings
    |--------------------------------------------------------------------------
    */
    'integrations' => [
        'exchange_rate_api' => env('EXCHANGE_RATE_API', 'exchangerate-api'),
        'shipping_calculator' => env('SHIPPING_CALCULATOR', 'internal'),
        'tax_calculator' => env('TAX_CALCULATOR', 'internal'),
    ],

];