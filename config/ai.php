<?php

return [

    /*
    |--------------------------------------------------------------------------
    | AI Provider Configuration
    |--------------------------------------------------------------------------
    |
    | Configure the AI provider for the conversational AI system.
    | Supported providers: "openai", "anthropic", "gemini"
    |
    */

    'provider' => env('AI_PROVIDER', 'gemini'),

    /*
    |--------------------------------------------------------------------------
    | OpenAI Configuration
    |--------------------------------------------------------------------------
    */
    'openai' => [
        'api_key' => env('OPENAI_API_KEY'),
        'organization' => env('OPENAI_ORGANIZATION'),
        'model' => env('OPENAI_MODEL', 'gpt-3.5-turbo'),
        'temperature' => env('OPENAI_TEMPERATURE', 0.7),
        'max_tokens' => env('OPENAI_MAX_TOKENS', 500),
        'timeout' => env('OPENAI_TIMEOUT', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Anthropic Claude Configuration
    |--------------------------------------------------------------------------
    */
    'anthropic' => [
        'api_key' => env('ANTHROPIC_API_KEY'),
        'model' => env('ANTHROPIC_MODEL', 'claude-3-sonnet-20240229'),
        'max_tokens' => env('ANTHROPIC_MAX_TOKENS', 500),
        'timeout' => env('ANTHROPIC_TIMEOUT', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Google Gemini Configuration (Primary AI Provider)
    |--------------------------------------------------------------------------
    */
    'gemini' => [
        'api_key' => env('GEMINI_API_KEY'),
        'model' => env('GEMINI_MODEL', 'gemini-pro'),
        'vision_model' => env('GEMINI_VISION_MODEL', 'gemini-pro-vision'),
        'temperature' => env('GEMINI_TEMPERATURE', 0.7),
        'max_tokens' => env('GEMINI_MAX_TOKENS', 500),
        'timeout' => env('GEMINI_TIMEOUT', 30),
        'safety_settings' => [
            'HARM_CATEGORY_HARASSMENT' => 'BLOCK_MEDIUM_AND_ABOVE',
            'HARM_CATEGORY_HATE_SPEECH' => 'BLOCK_MEDIUM_AND_ABOVE',
            'HARM_CATEGORY_SEXUALLY_EXPLICIT' => 'BLOCK_MEDIUM_AND_ABOVE',
            'HARM_CATEGORY_DANGEROUS_CONTENT' => 'BLOCK_MEDIUM_AND_ABOVE',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Intent Detection Configuration
    |--------------------------------------------------------------------------
    */
    'intent_detection' => [
        'confidence_threshold' => env('AI_INTENT_CONFIDENCE_THRESHOLD', 0.6),
        'use_ai_fallback' => env('AI_USE_AI_FALLBACK', true),
        'cache_results' => env('AI_CACHE_INTENT_RESULTS', true),
        'cache_ttl' => env('AI_INTENT_CACHE_TTL', 3600), // 1 hour
    ],

    /*
    |--------------------------------------------------------------------------
    | Entity Extraction Configuration
    |--------------------------------------------------------------------------
    */
    'entity_extraction' => [
        'use_ai_enhancement' => env('AI_ENTITY_AI_ENHANCEMENT', true),
        'confidence_threshold' => env('AI_ENTITY_CONFIDENCE_THRESHOLD', 0.5),
        'cache_results' => env('AI_CACHE_ENTITY_RESULTS', true),
        'cache_ttl' => env('AI_ENTITY_CACHE_TTL', 1800), // 30 minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | Conversation Configuration
    |--------------------------------------------------------------------------
    */
    'conversation' => [
        'max_context_messages' => env('AI_MAX_CONTEXT_MESSAGES', 10),
        'context_window_hours' => env('AI_CONTEXT_WINDOW_HOURS', 24),
        'auto_escalation_enabled' => env('AI_AUTO_ESCALATION_ENABLED', true),
        'escalation_confidence_threshold' => env('AI_ESCALATION_CONFIDENCE_THRESHOLD', 0.3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    */
    'rate_limiting' => [
        'requests_per_minute' => env('AI_REQUESTS_PER_MINUTE', 60),
        'requests_per_hour' => env('AI_REQUESTS_PER_HOUR', 1000),
        'burst_limit' => env('AI_BURST_LIMIT', 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging and Monitoring
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'log_requests' => env('AI_LOG_REQUESTS', true),
        'log_responses' => env('AI_LOG_RESPONSES', true),
        'log_errors' => env('AI_LOG_ERRORS', true),
        'sensitive_data_masking' => env('AI_MASK_SENSITIVE_DATA', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Features
    |--------------------------------------------------------------------------
    */
    'features' => [
        'product_recognition' => env('AI_PRODUCT_RECOGNITION_ENABLED', true),
        'price_calculation' => env('AI_PRICE_CALCULATION_ENABLED', true),
        'sentiment_analysis' => env('AI_SENTIMENT_ANALYSIS_ENABLED', true),
        'language_detection' => env('AI_LANGUAGE_DETECTION_ENABLED', false),
        'translation' => env('AI_TRANSLATION_ENABLED', false),
    ],

];