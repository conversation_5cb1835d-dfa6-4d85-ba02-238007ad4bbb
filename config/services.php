<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'twilio' => [
        'sid' => env('TWILIO_SID'),
        'token' => env('TWILIO_TOKEN'),
        'whatsapp_from' => env('TWILIO_WHATSAPP_FROM'),
        'skip_verification' => env('TWILIO_SKIP_VERIFICATION', false),
        'force_mode' => env('TWILIO_FORCE_MODE', false),
    ],

    'gemini' => [
        'api_key' => env('GEMINI_API_KEY'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Google Services Configuration
    |--------------------------------------------------------------------------
    */
    'google_vision' => [
        'api_key' => env('GOOGLE_VISION_API_KEY'),
        'credentials' => env('GOOGLE_APPLICATION_CREDENTIALS'),
        'project_id' => env('GOOGLE_CLOUD_PROJECT_ID'),
        'enabled' => env('GOOGLE_VISION_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Exchange Rate Services
    |--------------------------------------------------------------------------
    */
    'exchange_rate' => [
        'api_url' => env('EXCHANGE_RATE_API_URL', 'https://api.exchangerate-api.com/v4/latest'),
        'api_key' => env('EXCHANGE_RATE_API_KEY'),
        'backup_api_url' => env('EXCHANGE_RATE_BACKUP_API_URL', 'https://api.fixer.io/latest'),
        'backup_api_key' => env('EXCHANGE_RATE_BACKUP_API_KEY'),
        'cache_ttl' => env('EXCHANGE_RATE_CACHE_TTL', 3600), // 1 hour
    ],

    /*
    |--------------------------------------------------------------------------
    | OCR Services Configuration
    |--------------------------------------------------------------------------
    */
    'tesseract' => [
        'path' => env('TESSERACT_PATH', '/usr/bin/tesseract'),
        'enabled' => env('TESSERACT_ENABLED', true),
        'languages' => env('TESSERACT_LANGUAGES', 'eng'),
        'temp_dir' => env('TESSERACT_TEMP_DIR', storage_path('app/temp')),
    ],

];
