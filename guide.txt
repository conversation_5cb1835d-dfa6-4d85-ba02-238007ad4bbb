Awesome—this is a clean, high-impact WhatsApp flow. Here’s a complete, implementation-ready blueprint that fits your stack (<PERSON><PERSON> backend + Vue admin) and supports links, images, and plain text.

End-to-end flow (state machine)

States: GREET → IDENTIFY → INTENT → CAPTURE_ITEM → ENRICH_ITEM → PRICE → CART_OR_ORDER → ORDER_CONFIRM → DONE
1.	GREET
User: “hi/hello/hey/👋🏽” → Bot: friendly greeting.
2.	IDENTIFY

	•	If phone not in customers, ask: “What’s your name?” → save first_name.
	•	If known, greet by name.

	3.	INTENT
Bot: “What would you like to do today? (Send product name, link, or photo).”
4.	CAPTURE_ITEM (ingest one of:)

	•	Text: “Samsung Galaxy A15 128GB from Game, R3,499”
	•	Link/URL: product page or catalogue page
	•	Image: product photo (optionally with price tag)

	5.	ENRICH_ITEM (the bot must know product name, outlet, price)

	•	Try to fill slots via:
	•	DB match: fuzzy search products by name/URL/outlet.
	•	Link scraper: title + structured data (price/currency); if missing, ask user for price.
	•	Image: OCR/vision to infer name/brand/variant; always confirm price with user if not 100% certain.
	•	If still missing a slot, ask a pointed question:
“What’s the price you see?” / “Which shop (Makro/Game/etc.)?”

	6.	PRICE
Call backend pricing engine → return USD landed price + breakdown (exchange rate, markup, delivery band, duty/fee etc.).
Bot: “Delivered to Harare: USD $X. Add to cart or order now?”
7.	CART_OR_ORDER

	•	Add to Cart → “Added. Send another product or say ‘checkout’.”
	•	Order now → go to confirmation.

	8.	ORDER_CONFIRM
Summarize: items, totals, delivery city, phone, name → “Confirm?”
On “Confirm”, create order (status NEW) and close thread with reference number.
9.	DONE
“Order placed 🎉 Ref: #A1B2. We’ll follow up shortly.”

⸻

Pricing engine (rules set by admin)

Keep it data-driven; no logic hardcoded in bot.

Example formula (editable in DB):
base = source_price * fx_rate
markup = base * markup_percent_by_outlet_or_category
delivery = delivery_table[city|weight_band]
payment_fee = (base + markup) * payment_rate
total_usd = round_up(base + markup + delivery + payment_fee + misc_duty, 0.50)

Rules live in tables (pricing_rules, fx_rates, delivery_bands, outlet_overrides, category_markups). Admin can toggle “force user price” when scraping is unreliable.

⸻

Data model (minimal, extensible)
•	customers: id, phone_e164, first_name, last_name, is_blocked, created_at
•	products: id, name, outlet, source_url, image_url, sku, category, last_seen_price, currency, is_active
•	intake_items: one raw item per user submission
id, customer_id, type[text|link|image], raw_text, raw_url, media_id, extracted_name, extracted_outlet, extracted_price, currency, confidence, status[parsed|needs_price|needs_outlet|ready], created_at
•	carts: id, customer_id, status[open|ordered], created_at
•	cart_items: id, cart_id, product_name, outlet, source_url, price_value, price_currency, computed_total_usd, meta(json), created_at
•	orders: id, customer_id, cart_snapshot(json), total_usd, city, status[NEW|IN_PROGRESS|FULFILLED|CANCELLED], reference, created_at
•	pricing_rules: id, rule_name, scope[global|outlet|category], params(json), active
•	fx_rates: id, pair, rate, source, as_of
•	delivery_bands: id, city, band_key, price_usd
•	audit_logs: id, actor_type[bot|admin|system], actor_id, event, payload(json), created_at

⸻

WhatsApp integration choices
•	Meta WhatsApp Business Cloud API (direct, cheapest at scale)
•	Twilio WhatsApp (easier provisioning, slightly higher per-msg)

Either way, you’ll implement a webhook to receive messages and a send API to reply.

⸻

Bot brain (pragmatic NLU)

Start simple, upgrade later:
1.	Greeting intent: regex on hi|hello|hey|morning|afternoon.
2.	Checkout intent: checkout|buy now|place order.
3.	Price confirmations: detect currency & numbers (e.g., R, ZAR, R3,499, USD 120, ZAR 1299).
4.	Outlet classifier: look for known shop names; else ask.
5.	URL handler: if domain matches known outlets (Makro, Game), use domain-specific parser; else generic scraper (OpenGraph + JSON-LD + relevant selectors).
6.	Image handler: attempt OCR/vision; if uncertain → ask user to confirm price and outlet.

You can optionally call an LLM for entity normalization (“Galaxy A15 128GB” → name=Galaxy A15, storage=128GB), but still show the user what you inferred and ask for confirmation when confidence is low.

⸻

Conversation prompts (examples)
•	New user:
“Hi! I’m Youze Africa 🤖. I help you get USD delivered pricing from SA shops. What’s your name?”
“Thanks, Tapi. Send a product name, link, or photo (e.g., ‘Defy 13-place dishwasher from Makro’).”
•	Missing price:
“I couldn’t see a price on that page. What’s the price you see (e.g., R3499 or ZAR 3499)?”
•	Missing outlet:
“Which shop is this from? (Makro, Game, Takealot, etc.)”
•	Price result:
“Delivered to Harare: $118.50 (FX 18.20, markup 25%, delivery $12). Add to cart or order now?”
•	Order confirm:
“Order: 1× Galaxy A15 (Game, R3499) → $118.50. Deliver to Harare. Confirm?”
•	Done:
“🎉 Order placed! Ref #YA-9K2Q. We’ll reach out for payment & delivery details.”

⸻

Laravel backend: key endpoints

POST /webhooks/whatsapp        # receive messages
POST /bot/send                 # internal: send message via provider
POST /bot/price/quote          # compute USD landed price
POST /bot/cart/add             # add line item
POST /bot/order/confirm        # finalize order
GET  /admin/orders             # Vue admin grid
GET  /admin/pricing/rules      # manage rules

Routing (Laravel routes/api.php):

Route::post('/webhooks/whatsapp', [WhatsappWebhookController::class, 'handle']);
Route::post('/bot/price/quote', [BotController::class, 'quote']);
Route::post('/bot/cart/add', [BotController::class, 'addToCart']);
Route::post('/bot/order/confirm', [BotController::class, 'confirmOrder']);

Webhook handler (skeleton):

public function handle(Request $req) {
$msg = WhatsAppIncoming::fromCloudPayload($req->all());
$customer = CustomerService::identify($msg->phone);
$state = ConversationService::getState($customer->id);

    switch ($state->name) {
        case 'GREET': return BotFlow::greet($customer);
        case 'IDENTIFY': return BotFlow::identify($customer, $msg);
        case 'INTENT': return BotFlow::askForItem($customer, $msg);
        case 'CAPTURE_ITEM': return BotFlow::captureItem($customer, $msg); // text|url|image
        case 'ENRICH_ITEM': return BotFlow::enrichItem($customer);
        case 'PRICE': return BotFlow::price($customer);
        case 'CART_OR_ORDER': return BotFlow::cartOrOrder($customer, $msg);
        case 'ORDER_CONFIRM': return BotFlow::confirm($customer, $msg);
        default: return BotFlow::reset($customer);
    }
}

Pricing service (example):

$total = PricingEngine::for($outlet, $category)
->fx($price_value, $price_currency)       // ZAR→USD
->applyMarkup()                           // outlet/category markups
->addDelivery($city)
->addPaymentFee()
->addDutiesIfAny($category)
->totalRounded(0.5);


⸻

Admin (Vue) essentials
•	Rules Manager: FX rates (manual override or API), outlet/category markups, delivery city bands, payment fees.
•	Orders: NEW/IN_PROGRESS/FULFILLED; export; assign handler.
•	Intake Review: see raw user submissions, OCR/scraper results, confidence, quick-fix buttons (set price/outlet).
•	Catalog Sync (optional): ingest CSV/feeds for known products.

⸻

Scraping/vision strategy (practical)
•	Known outlets: write per-domain adapters (Makro, Game). Prefer JSON-LD/schema.org and clearly labeled price DOM.
•	Fallback: OpenGraph + regex for currency + number.
•	Image: quick OCR (e.g., Tesseract) for tags like “R 3 499”; label detection for brand/product; always ask to confirm price.
•	Always cache the parsed result on intake_items with confidence and what was inferred vs asked.

⸻

Error handling & guardrails
•	If we can’t get price after 1 ask → “I couldn’t find the price. Please send the exact price you see.”
•	If FX missing → use last known rate and say “(FX last updated: 21 Sep 2025)”.
•	If rules disabled → “Pricing temporarily unavailable; we’ll ping you when it’s back.”
•	If user idles >10 minutes mid-flow → summarize and ask to continue or cancel.

⸻

Security, audit, and compliance
•	Log every inbound/outbound message + price calculation inputs/outputs in audit_logs.
•	Validate and sanitize URLs; blocklist domains if needed.
•	Rate-limit per customer to deter scraping abuse.
•	Explicit consent for storing name & order history (simple opt-in message on first run).

⸻

Quick start checklist
1.	Provision WhatsApp (Meta Cloud API or Twilio). Set webhook URL to /api/webhooks/whatsapp.
2.	Create DB tables above; seed fx_rates, delivery_bands, and default pricing_rules.
3.	Implement state store (Redis) keyed by customer_id.
4.	Build Known Outlet Parsers (start with Makro & Game).
5.	Ship Vue Admin for rules + orders.
6.	Test fixtures: 3 sample products (Makro/Game link, text-only, image with price).
7.	Go live; watch audit_logs to tune markups/delivery.

⸻

If you want, I can turn this into a Laravel starter (migrations, seeders, controllers, a minimal PricingEngine class, and a WhatsApp webhook controller) so you can paste into your project and run.
