<?php
/**
 * cPanel Setup Verification Script
 * 
 * Run this script to verify all cPanel components are working:
 * php scripts/verify-cpanel-setup.php
 */

echo "🔍 Verifying cPanel Setup for Youzeafrika\n";
echo str_repeat("=", 50) . "\n\n";

// Test 1: Database Connection
echo "1. Testing Database Connection...\n";
try {
    require_once __DIR__ . '/../vendor/autoload.php';
    $app = require_once __DIR__ . '/../bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    $kernel->bootstrap();
    
    \Illuminate\Support\Facades\DB::connection()->getPdo();
    echo "   ✅ Database connected successfully\n\n";
} catch (Exception $e) {
    echo "   ❌ Database connection failed: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 2: Routes Accessibility
echo "2. Testing Cron Routes...\n";
$routes = [
    '/cron/health' => 'System Health Check',
    '/cron/stats' => 'System Statistics',
    '/cron/schedule' => 'Laravel Scheduler',
    '/cron/notifications' => 'Notification Processing',
    '/cron/qualify-leads' => 'Lead Qualification',
    '/cron/queue' => 'Queue Processing'
];

foreach ($routes as $route => $description) {
    try {
        $request = \Illuminate\Http\Request::create($route, 'GET');
        $response = $app->handle($request);
        $statusCode = $response->getStatusCode();
        
        if ($statusCode === 200) {
            echo "   ✅ $description ($route)\n";
        } else {
            echo "   ❌ $description ($route) - Status: $statusCode\n";
        }
    } catch (Exception $e) {
        echo "   ❌ $description ($route) - Error: " . $e->getMessage() . "\n";
    }
}
echo "\n";

// Test 3: PHP Cron Scripts
echo "3. Testing PHP Cron Scripts...\n";
$scripts = [
    'system-health.php' => 'System Health Monitor',
    'scheduler.php' => 'Laravel Scheduler Runner'
];

foreach ($scripts as $script => $description) {
    $scriptPath = __DIR__ . "/../cron/$script";
    if (file_exists($scriptPath)) {
        echo "   ✅ $description ($script) - File exists\n";
        
        // Test if script is executable (basic syntax check)
        $output = shell_exec("php -l $scriptPath 2>&1");
        if (strpos($output, 'No syntax errors detected') !== false) {
            echo "   ✅ $description - PHP syntax valid\n";
        } else {
            echo "   ❌ $description - PHP syntax error\n";
        }
    } else {
        echo "   ❌ $description ($script) - File not found\n";
    }
}
echo "\n";

// Test 4: Database Tables
echo "4. Checking Database Tables...\n";
$requiredTables = [
    'leads' => 'Lead Management',
    'inquiries' => 'Inquiry Tracking', 
    'notifications' => 'Notification System',
    'conversations' => 'Chat Conversations',
    'jobs' => 'Queue Jobs',
    'failed_jobs' => 'Failed Jobs'
];

foreach ($requiredTables as $table => $description) {
    try {
        $count = \Illuminate\Support\Facades\DB::table($table)->count();
        echo "   ✅ $description ($table) - $count records\n";
    } catch (Exception $e) {
        echo "   ❌ $description ($table) - Table missing or error\n";
    }
}
echo "\n";

// Test 5: File Permissions
echo "5. Checking File Permissions...\n";
$checkPaths = [
    'cron/' => 'Cron Directory',
    'storage/' => 'Storage Directory',
    'bootstrap/cache/' => 'Bootstrap Cache'
];

foreach ($checkPaths as $path => $description) {
    $fullPath = __DIR__ . "/../$path";
    if (is_dir($fullPath)) {
        $perms = substr(sprintf('%o', fileperms($fullPath)), -4);
        $writable = is_writable($fullPath) ? 'writable' : 'not writable';
        echo "   ✅ $description - Permissions: $perms ($writable)\n";
    } else {
        echo "   ❌ $description - Directory not found\n";
    }
}
echo "\n";

// Summary
echo "🎯 Setup Verification Complete!\n";
echo str_repeat("=", 50) . "\n";
echo "For cPanel, add these cron jobs:\n\n";
echo "Every minute:\n";
echo "* * * * * /usr/local/bin/php /home/<USER>/public_html/cron/scheduler.php >/dev/null 2>&1\n\n";
echo "Every 5 minutes:\n";
echo "*/5 * * * * /usr/local/bin/php /home/<USER>/public_html/cron/queue-worker.php >/dev/null 2>&1\n\n";
echo "Every 30 minutes:\n";
echo "*/30 * * * * /usr/local/bin/php /home/<USER>/public_html/cron/system-health.php >/dev/null 2>&1\n\n";
echo "Or use web-based cron alternatives by calling these URLs:\n";
echo "- https://yourdomain.com/cron/schedule (every minute)\n";
echo "- https://yourdomain.com/cron/queue (every 5 minutes)\n";
echo "- https://yourdomain.com/cron/health (every 30 minutes)\n\n";
echo "🚀 Ready for cPanel deployment!\n";