#!/bin/bash

# Youzeafrika Crontab Setup Script
# This script sets up the crontab for Laravel scheduler

APP_DIR="/Applications/XAMPP/xamppfiles/htdocs/youzeafrika"
PHP_PATH=$(which php)

echo "🕐 Setting up Crontab for Youzeafrika..."
echo "Application Directory: $APP_DIR"
echo "PHP Path: $PHP_PATH"

# Create a temporary crontab file
TEMP_CRON=$(mktemp)

# Get existing crontab (if any) excluding our entries
crontab -l 2>/dev/null | grep -v "youzeafrika" > "$TEMP_CRON"

# Add our crontab entries
cat >> "$TEMP_CRON" << EOF

# Youzeafrika Laravel Scheduler - DO NOT EDIT MANUALLY
* * * * * cd $APP_DIR && $PHP_PATH artisan schedule:run >> /dev/null 2>&1

# Youzeafrika Queue Worker Health Check (restart if needed) - every 5 minutes
*/5 * * * * pgrep -f "queue:work" > /dev/null || cd $APP_DIR && $APP_DIR/scripts/start-queue-workers.sh >> $APP_DIR/storage/logs/queue-restart.log 2>&1

# Youzeafrika Log Rotation - weekly on Sunday at 3 AM
0 3 * * 0 cd $APP_DIR && find storage/logs -name "*.log" -type f -mtime +7 -exec gzip {} \; && find storage/logs -name "*.gz" -type f -mtime +30 -delete

EOF

# Install the new crontab
crontab "$TEMP_CRON"

# Clean up
rm "$TEMP_CRON"

echo "✅ Crontab installed successfully!"
echo ""
echo "📋 Current crontab entries:"
crontab -l | grep -A 10 "Youzeafrika"

echo ""
echo "🔍 To verify crontab is working:"
echo "  - tail -f /var/log/cron.log (on Linux)"
echo "  - tail -f /var/log/system.log (on macOS)"
echo "  - Check storage/logs/laravel.log for scheduled task output"
echo ""
echo "⚠️  Make sure cron daemon is running:"
echo "  - sudo service cron start (Linux)"
echo "  - sudo launchctl load -w /System/Library/LaunchDaemons/com.apple.cron.plist (macOS)"