#!/bin/bash

# Youzeafrika Queue Workers Startup Script
# This script starts queue workers for different job types

echo "🚀 Starting Youzeafrika Queue Workers..."

# Get the application directory
APP_DIR="/Applications/XAMPP/xamppfiles/htdocs/youzeafrika"

# Function to start a queue worker
start_worker() {
    local queue_name=$1
    local max_jobs=${2:-1000}
    local timeout=${3:-60}
    local memory=${4:-512}
    local sleep=${5:-3}
    
    echo "Starting $queue_name queue worker..."
    
    nohup php "$APP_DIR/artisan" queue:work database \
        --queue="$queue_name" \
        --max-jobs="$max_jobs" \
        --timeout="$timeout" \
        --memory="$memory" \
        --sleep="$sleep" \
        --tries=3 \
        --daemon \
        > "$APP_DIR/storage/logs/queue-$queue_name.log" 2>&1 &
    
    echo "✅ $queue_name worker started (PID: $!)"
}

# Start high-priority notification workers (2 workers)
start_worker "notifications" 500 30 256 1
start_worker "notifications" 500 30 256 1

# Start default queue worker for general jobs
start_worker "default" 1000 60 512 3

# Start low-priority analytics worker
start_worker "analytics" 100 120 256 10

echo ""
echo "📊 Queue Worker Status:"
php "$APP_DIR/artisan" queue:monitor

echo ""
echo "🎉 All queue workers started successfully!"
echo ""
echo "To monitor:"
echo "  - tail -f storage/logs/queue-*.log"
echo "  - php artisan queue:monitor"
echo "  - php artisan horizon (if using Horizon)"
echo ""
echo "To stop workers:"
echo "  - pkill -f 'queue:work'"
echo "  - php artisan queue:restart"