#!/bin/bash
# System Monitoring Script for cPanel
# Usage: ./scripts/monitor-system.sh

echo "📊 Youzeafrika System Monitor"
echo "=========================="
echo "Timestamp: $(date)"
echo ""

# Check if processes are running
echo "🔍 System Health:"
curl -s http://localhost/cron/health | python3 -m json.tool 2>/dev/null || echo "❌ Health endpoint not accessible"
echo ""

# Check recent logs
echo "📝 Recent System Activity:"
if [ -f "storage/logs/laravel.log" ]; then
    echo "Last 5 log entries:"
    tail -5 storage/logs/laravel.log
else
    echo "❌ Log file not found"
fi
echo ""

# Check database
echo "💾 Database Status:"
php artisan tinker --execute="try { DB::connection()->getPdo(); echo '✅ Database connected'; } catch(Exception \$e) { echo '❌ Database error: ' . \$e->getMessage(); }"
echo ""

# Check queue status
echo "⚡ Queue Status:"
php artisan queue:failed --format=json 2>/dev/null | head -1 || echo "No failed jobs"
echo ""

echo "Monitor complete at $(date)"