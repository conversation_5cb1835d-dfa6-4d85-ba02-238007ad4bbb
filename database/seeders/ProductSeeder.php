<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Shop;
use App\Models\Category;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ProductImage;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $makro = Shop::where('slug', 'makro')->first();
        $game = Shop::where('slug', 'game')->first();

        // Create categories
        $electronics = Category::create([
            'name' => 'Electronics',
            'slug' => 'electronics',
            'shop_id' => null
        ]);

        $tvs = Category::create([
            'name' => 'TVs & Audio',
            'slug' => 'tvs-audio',
            'parent_id' => $electronics->id,
            'shop_id' => $makro->id
        ]);

        $phones = Category::create([
            'name' => 'Mobile Phones',
            'slug' => 'mobile-phones',
            'parent_id' => $electronics->id,
            'shop_id' => $game->id
        ]);

        // Create sample products
        $this->createTVProducts($makro, $tvs);
        $this->createPhoneProducts($game, $phones);
    }

    private function createTVProducts($shop, $category)
    {
        // Samsung 55" QLED TV
        $samsungTV = Product::create([
            'shop_id' => $shop->id,
            'category_id' => $category->id,
            'title' => 'Samsung 55" QLED 4K Smart TV',
            'slug' => 'samsung-55-qled-4k-smart-tv',
            'description' => 'Experience stunning 4K picture quality with Quantum Dot technology. Smart TV with built-in streaming apps.',
            'brand' => 'Samsung',
            'model' => 'QN55Q60C',
            'weight_kg' => 17.5,
            'volume_m3' => 0.12,
            'status' => 'active'
        ]);

        ProductVariant::create([
            'product_id' => $samsungTV->id,
            'sku' => 'SAM-QN55Q60C-ZA',
            'sa_price_zar' => 12999.00,
            'stock_status' => 'in_stock',
            'price_last_seen_at' => now()
        ]);

        ProductImage::create([
            'product_id' => $samsungTV->id,
            'url' => 'https://example.com/samsung-tv.jpg',
            'is_primary' => true
        ]);

        // Hisense 43" LED TV
        $hisenseTV = Product::create([
            'shop_id' => $shop->id,
            'category_id' => $category->id,
            'title' => 'Hisense 43" Full HD LED TV',
            'slug' => 'hisense-43-full-hd-led-tv',
            'description' => 'Affordable Full HD LED TV with crisp picture quality and multiple connectivity options.',
            'brand' => 'Hisense',
            'model' => '43A4G',
            'weight_kg' => 8.2,
            'volume_m3' => 0.08,
            'status' => 'active'
        ]);

        ProductVariant::create([
            'product_id' => $hisenseTV->id,
            'sku' => 'HIS-43A4G-ZA',
            'sa_price_zar' => 4999.00,
            'stock_status' => 'in_stock',
            'price_last_seen_at' => now()
        ]);

        ProductImage::create([
            'product_id' => $hisenseTV->id,
            'url' => 'https://example.com/hisense-tv.jpg',
            'is_primary' => true
        ]);
    }

    private function createPhoneProducts($shop, $category)
    {
        // iPhone 15
        $iphone = Product::create([
            'shop_id' => $shop->id,
            'category_id' => $category->id,
            'title' => 'Apple iPhone 15 128GB',
            'slug' => 'apple-iphone-15-128gb',
            'description' => 'The latest iPhone with Dynamic Island, 48MP camera, and USB-C.',
            'brand' => 'Apple',
            'model' => 'iPhone 15',
            'weight_kg' => 0.171,
            'volume_m3' => 0.0001,
            'status' => 'active'
        ]);

        ProductVariant::create([
            'product_id' => $iphone->id,
            'sku' => 'APPLE-IP15-128-BLU',
            'attrs_json' => ['color' => 'Blue', 'storage' => '128GB'],
            'sa_price_zar' => 18999.00,
            'stock_status' => 'in_stock',
            'price_last_seen_at' => now()
        ]);

        ProductVariant::create([
            'product_id' => $iphone->id,
            'sku' => 'APPLE-IP15-128-WHT',
            'attrs_json' => ['color' => 'White', 'storage' => '128GB'],
            'sa_price_zar' => 18999.00,
            'stock_status' => 'in_stock',
            'price_last_seen_at' => now()
        ]);

        ProductImage::create([
            'product_id' => $iphone->id,
            'url' => 'https://example.com/iphone-15.jpg',
            'is_primary' => true
        ]);

        // Samsung Galaxy S24
        $galaxyS24 = Product::create([
            'shop_id' => $shop->id,
            'category_id' => $category->id,
            'title' => 'Samsung Galaxy S24 256GB',
            'slug' => 'samsung-galaxy-s24-256gb',
            'description' => 'Flagship Android phone with AI features, excellent camera, and long battery life.',
            'brand' => 'Samsung',
            'model' => 'Galaxy S24',
            'weight_kg' => 0.167,
            'volume_m3' => 0.0001,
            'status' => 'active'
        ]);

        ProductVariant::create([
            'product_id' => $galaxyS24->id,
            'sku' => 'SAM-S24-256-BLK',
            'attrs_json' => ['color' => 'Black', 'storage' => '256GB'],
            'sa_price_zar' => 16999.00,
            'stock_status' => 'in_stock',
            'price_last_seen_at' => now()
        ]);

        ProductImage::create([
            'product_id' => $galaxyS24->id,
            'url' => 'https://example.com/galaxy-s24.jpg',
            'is_primary' => true
        ]);
    }
}
