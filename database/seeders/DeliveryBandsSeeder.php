<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\DeliveryBand;

class DeliveryBandsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing bands
        DeliveryBand::truncate();

        // Seed delivery bands for Zimbabwean cities
        $deliveryBands = [
            // Major cities
            ['city' => 'Harare', 'band_key' => 'tier_1', 'price_usd' => 12.00],
            ['city' => 'Bulawayo', 'band_key' => 'tier_1', 'price_usd' => 15.00],
            ['city' => 'Chitungwiza', 'band_key' => 'tier_2', 'price_usd' => 14.00],
            ['city' => 'Mutare', 'band_key' => 'tier_2', 'price_usd' => 18.00],
            ['city' => 'Gweru', 'band_key' => 'tier_2', 'price_usd' => 20.00],

            // Secondary cities
            ['city' => 'Masvingo', 'band_key' => 'tier_3', 'price_usd' => 25.00],
            ['city' => 'Chinhoyi', 'band_key' => 'tier_3', 'price_usd' => 22.00],
            ['city' => 'Bindura', 'band_key' => 'tier_3', 'price_usd' => 25.00],
            ['city' => 'Kadoma', 'band_key' => 'tier_3', 'price_usd' => 25.00],
            ['city' => 'Kwekwe', 'band_key' => 'tier_3', 'price_usd' => 28.00],

            // Smaller towns
            ['city' => 'Victoria Falls', 'band_key' => 'tier_4', 'price_usd' => 35.00],
            ['city' => 'Hwange', 'band_key' => 'tier_4', 'price_usd' => 35.00],
            ['city' => 'Kariba', 'band_key' => 'tier_4', 'price_usd' => 40.00],
            ['city' => 'Chiredzi', 'band_key' => 'tier_4', 'price_usd' => 35.00],
            ['city' => 'Triangle', 'band_key' => 'tier_4', 'price_usd' => 35.00],

            // Remote areas
            ['city' => 'Other', 'band_key' => 'remote', 'price_usd' => 50.00],
        ];

        foreach ($deliveryBands as $band) {
            DeliveryBand::create($band);
        }
    }
}