<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PricingRule;
use App\Models\DeliveryBand;
use App\Models\ExchangeRate;

class SimpleBotSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create basic pricing rules
        PricingRule::updateOrCreate(
            ['rule_name' => 'global_markup', 'scope' => 'global'],
            [
                'scope_type' => 'global',
                'key' => 'markup_percent',
                'value' => '25',
                'value_type' => 'percent',
                'params' => ['markup_percent' => 25],
                'active' => true
            ]
        );

        PricingRule::updateOrCreate(
            ['rule_name' => 'payment_fee', 'scope' => 'global'],
            [
                'scope_type' => 'global',
                'key' => 'payment_rate',
                'value' => '3.5',
                'value_type' => 'percent',
                'params' => ['rate_percent' => 3.5],
                'active' => true
            ]
        );

        // Create outlet-specific markups
        PricingRule::updateOrCreate(
            ['rule_name' => 'outlet_markup_makro', 'scope' => 'outlet'],
            [
                'scope_type' => 'shop',
                'key' => 'markup_percent',
                'value' => '20',
                'value_type' => 'percent',
                'params' => [
                    'outlet' => 'Makro',
                    'markup_percent' => 20
                ],
                'active' => true
            ]
        );

        PricingRule::updateOrCreate(
            ['rule_name' => 'outlet_markup_game', 'scope' => 'outlet'],
            [
                'scope_type' => 'shop',
                'key' => 'markup_percent',
                'value' => '25',
                'value_type' => 'percent',
                'params' => [
                    'outlet' => 'Game',
                    'markup_percent' => 25
                ],
                'active' => true
            ]
        );

        // Create delivery bands
        DeliveryBand::updateOrCreate(
            ['city' => 'Harare'],
            [
                'band_key' => 'harare_standard',
                'price_usd' => 12.00
            ]
        );

        DeliveryBand::updateOrCreate(
            ['city' => 'Bulawayo'],
            [
                'band_key' => 'bulawayo_standard',
                'price_usd' => 15.00
            ]
        );

        DeliveryBand::updateOrCreate(
            ['city' => 'Mutare'],
            [
                'band_key' => 'mutare_standard',
                'price_usd' => 18.00
            ]
        );

        // Create current exchange rate
        ExchangeRate::updateOrCreate(
            ['from_currency' => 'ZAR', 'to_currency' => 'USD'],
            [
                'rate' => 0.055, // 1 ZAR = 0.055 USD (approx 18.2 ZAR per USD)
                'usd_per_zar' => 0.055,
                'source' => 'manual',
                'effective_at' => now(),
                'is_official' => true,
                'is_active' => true
            ]
        );

        $this->command->info('Simple bot data seeded successfully!');
    }
}
