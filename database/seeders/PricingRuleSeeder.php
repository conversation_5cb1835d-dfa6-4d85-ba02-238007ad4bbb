<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PricingRule;

class PricingRuleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Default global pricing rules
        $rules = [
            ['key' => 'markup_percent', 'value' => '50.0', 'value_type' => 'percent'],
            ['key' => 'duty_percent', 'value' => '15.0', 'value_type' => 'percent'],
            ['key' => 'handling_percent', 'value' => '5.0', 'value_type' => 'percent'],
            ['key' => 'handling_fixed_usd', 'value' => '5.0', 'value_type' => 'fixed'],
            ['key' => 'payment_percent', 'value' => '3.0', 'value_type' => 'percent'],
            ['key' => 'payment_fixed_usd', 'value' => '1.0', 'value_type' => 'fixed'],
            ['key' => 'floor_margin_usd', 'value' => '10.0', 'value_type' => 'fixed'],
        ];

        foreach ($rules as $rule) {
            PricingRule::create([
                'scope_type' => 'global',
                'scope_id' => null,
                'key' => $rule['key'],
                'value' => $rule['value'],
                'value_type' => $rule['value_type'],
                'effective_from' => now(),
                'effective_to' => null,
            ]);
        }
    }
}
