<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ExchangeRate;
use Carbon\Carbon;

class ExchangeRateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        ExchangeRate::create([
            'source' => 'Reserve Bank of South Africa',
            'usd_per_zar' => 0.055000, // Example rate: 1 ZAR = 0.055 USD (approx 18 ZAR per USD)
            'effective_at' => Carbon::now(),
            'is_official' => true
        ]);
    }
}
