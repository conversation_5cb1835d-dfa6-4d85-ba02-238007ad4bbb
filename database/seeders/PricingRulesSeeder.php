<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PricingRule;

class PricingRulesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing rules
        PricingRule::truncate();

        // Global markup rule
        PricingRule::create([
            'scope_type' => 'global',
            'scope_id' => null,
            'key' => 'global_markup',
            'value' => '25.0',
            'value_type' => 'percent',
        ]);

        // Outlet-specific markups
        $outletMarkups = [
            'Makro' => 20.0,
            'Game' => 25.0,
            'Takealot' => 30.0,
            'Pick n Pay' => 22.0,
            'Woolworths' => 35.0,
        ];

        foreach ($outletMarkups as $outlet => $markup) {
            PricingRule::create([
                'scope_type' => 'shop',
                'scope_id' => null,
                'key' => "outlet_markup_{$outlet}",
                'value' => (string) $markup,
                'value_type' => 'percent',
            ]);
        }

        // Payment processing fee
        PricingRule::create([
            'scope_type' => 'global',
            'scope_id' => null,
            'key' => 'payment_fee',
            'value' => '3.5',
            'value_type' => 'percent',
        ]);

        // Category-specific markups
        $categoryMarkups = [
            'Electronics' => 30.0,
            'Appliances' => 25.0,
            'Fashion' => 35.0,
            'Sports' => 25.0,
            'Home & Garden' => 20.0,
        ];

        foreach ($categoryMarkups as $category => $markup) {
            PricingRule::create([
                'scope_type' => 'category',
                'scope_id' => null,
                'key' => "category_markup_{$category}",
                'value' => (string) $markup,
                'value_type' => 'percent',
            ]);
        }

        // Force user price rule (for when scraping is unreliable)
        PricingRule::create([
            'scope_type' => 'global',
            'scope_id' => null,
            'key' => 'force_user_price',
            'value' => json_encode([
                'enabled' => false,
                'message' => 'Please provide the exact price you see'
            ]),
            'value_type' => 'json',
        ]);
    }
}