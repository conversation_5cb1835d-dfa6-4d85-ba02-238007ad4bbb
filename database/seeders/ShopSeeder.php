<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Shop;

class ShopSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $shops = [
            [
                'name' => 'Makro',
                'slug' => 'makro',
                'logo_url' => '/uploads/shops/logos/makro.jpg',
                'website' => 'https://www.makro.co.za',
                'status' => 'active'
            ],
            [
                'name' => 'Game',
                'slug' => 'game',
                'logo_url' => '/uploads/shops/logos/game.jpg',
                'website' => 'https://www.game.co.za',
                'status' => 'active'
            ],
            [
                'name' => 'Checkers',
                'slug' => 'checkers',
                'logo_url' => '/uploads/shops/logos/checkers.jpg',
                'website' => 'https://www.checkers.co.za',
                'status' => 'active'
            ],
            [
                'name' => 'Clicks',
                'slug' => 'clicks',
                'logo_url' => '/uploads/shops/logos/clicks.jpg',
                'website' => 'https://www.clicks.co.za',
                'status' => 'active'
            ],
            [
                'name' => 'Dis-Chem',
                'slug' => 'dischem',
                'logo_url' => '/uploads/shops/logos/dischem.jpg',
                'website' => 'https://www.dis-chem.co.za',
                'status' => 'active'
            ],
            [
                'name' => 'Incredible Connection',
                'slug' => 'incredible',
                'logo_url' => '/uploads/shops/logos/incredible.jpg',
                'website' => 'https://www.incredible.co.za',
                'status' => 'active'
            ],
            [
                'name' => 'Mr Price',
                'slug' => 'mrprice',
                'logo_url' => '/uploads/shops/logos/mrprice.jpg',
                'website' => 'https://www.mrp.com',
                'status' => 'active'
            ],
            [
                'name' => 'Pick n Pay',
                'slug' => 'picknpay',
                'logo_url' => '/uploads/shops/logos/picknpay.jpg',
                'website' => 'https://www.pnp.co.za',
                'status' => 'active'
            ],
            [
                'name' => 'Sportscene',
                'slug' => 'sportscene',
                'logo_url' => '/uploads/shops/logos/sportscene.jpg',
                'website' => 'https://www.sportscene.co.za',
                'status' => 'active'
            ],
            [
                'name' => 'Takealot',
                'slug' => 'takealot',
                'logo_url' => '/uploads/shops/logos/takealot.jpg',
                'website' => 'https://www.takealot.com',
                'status' => 'active'
            ],
            [
                'name' => 'Woolworths',
                'slug' => 'woolworths',
                'logo_url' => '/uploads/shops/logos/woolworths.jpg',
                'website' => 'https://www.woolworths.co.za',
                'status' => 'active'
            ],
            [
                'name' => 'Builders Warehouse',
                'slug' => 'builderswarehouse',
                'logo_url' => '/uploads/shops/logos/builderswarehouse.jpg',
                'website' => 'https://www.builders.co.za',
                'status' => 'active'
            ],
        ];

        foreach ($shops as $shopData) {
            Shop::updateOrCreate(
                ['slug' => $shopData['slug']],
                $shopData
            );
        }

        $this->command->info('Shop seeder completed! ' . count($shops) . ' shops processed.');
    }
}
