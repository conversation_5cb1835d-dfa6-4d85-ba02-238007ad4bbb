<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ExchangeRate;
use Carbon\Carbon;

class ExchangeRatesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing rates
        ExchangeRate::truncate();

        $now = Carbon::now();

        // Seed current exchange rates as per guide.txt structure
        ExchangeRate::create([
            'source' => 'manual',
            'pair' => 'USD/ZAR',
            'rate' => 18.20, // 1 USD = 18.20 ZAR (so 1 ZAR = 0.055 USD)
            'as_of' => $now,
            'from_currency' => 'ZAR',
            'to_currency' => 'USD',
            'is_active' => true,
        ]);

        ExchangeRate::create([
            'source' => 'manual',
            'pair' => 'USD/EUR',
            'rate' => 0.91, // 1 USD = 0.91 EUR
            'as_of' => $now,
            'from_currency' => 'EUR',
            'to_currency' => 'USD',
            'is_active' => true,
        ]);

        ExchangeRate::create([
            'source' => 'manual',
            'pair' => 'USD/GBP',
            'rate' => 0.80, // 1 USD = 0.80 GBP
            'as_of' => $now,
            'from_currency' => 'GBP',
            'to_currency' => 'USD',
            'is_active' => true,
        ]);

        // ZAR to USD rate for pricing engine
        ExchangeRate::create([
            'source' => 'reserve_bank',
            'pair' => 'ZAR/USD',
            'rate' => 0.055, // 1 ZAR = 0.055 USD
            'as_of' => $now,
            'from_currency' => 'ZAR',
            'to_currency' => 'USD',
            'is_active' => true,
        ]);
    }
}