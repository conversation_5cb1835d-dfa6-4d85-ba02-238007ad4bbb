<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pricing_rules', function (Blueprint $table) {
            $table->string('rule_name')->nullable()->after('id');
            $table->string('scope')->nullable()->after('rule_name');
            $table->json('params')->nullable()->after('scope');
            $table->boolean('active')->default(true)->after('params');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pricing_rules', function (Blueprint $table) {
            $table->dropColumn(['rule_name', 'scope', 'params', 'active']);
        });
    }
};
