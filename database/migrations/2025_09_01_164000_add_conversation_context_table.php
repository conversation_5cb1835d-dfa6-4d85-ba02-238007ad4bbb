<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add conversation context table for session data
        Schema::create('conversation_contexts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('conversation_id')->constrained('conversations')->onDelete('cascade');
            $table->string('key'); // e.g., 'current_search', 'awaiting_input_type', 'cart_id'
            $table->json('value'); // Flexible storage for any context data
            $table->timestamps();
            
            $table->unique(['conversation_id', 'key']);
            $table->index('conversation_id');
        });

        // Add conversation_id to carts table
        Schema::table('carts', function (Blueprint $table) {
            $table->foreignId('conversation_id')->nullable()->after('customer_id')->constrained('conversations')->onDelete('set null');
            $table->string('wa_number')->nullable()->after('session_id'); // For guest WhatsApp users
            
            $table->index('conversation_id');
            $table->index('wa_number');
        });

        // Add context fields to conversations table
        Schema::table('conversations', function (Blueprint $table) {
            $table->json('metadata')->nullable()->after('state'); // For storing conversation-specific data
            $table->string('current_flow')->nullable()->after('metadata'); // e.g., 'product_search', 'checkout', 'agent_handover'
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conversation_contexts');
        
        Schema::table('carts', function (Blueprint $table) {
            $table->dropForeign(['conversation_id']);
            $table->dropColumn(['conversation_id', 'wa_number']);
        });
        
        Schema::table('conversations', function (Blueprint $table) {
            $table->dropColumn(['metadata', 'current_flow']);
        });
    }
};