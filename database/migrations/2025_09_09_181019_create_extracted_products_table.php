<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('extracted_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ingestion_id')->constrained('catalogue_ingestions')->onDelete('cascade');
            $table->foreignId('catalogue_id')->constrained()->onDelete('cascade');
            $table->foreignId('shop_id')->constrained()->onDelete('cascade');
            
            // Extracted product information
            $table->string('extracted_name');
            $table->text('extracted_description')->nullable();
            $table->string('extracted_sku')->nullable();
            $table->string('extracted_brand')->nullable();
            $table->string('extracted_model')->nullable();
            $table->decimal('extracted_price', 12, 2)->nullable();
            $table->string('extracted_currency', 3)->nullable();
            $table->string('extracted_unit')->nullable();
            $table->json('extracted_specifications')->nullable();
            $table->json('extracted_features')->nullable();
            $table->string('extracted_category')->nullable();
            $table->json('extracted_images')->nullable()->comment('Regions or URLs of product images');
            
            // AI extraction metadata
            $table->integer('page_number')->nullable()->comment('Page where product was found');
            $table->json('bounding_box')->nullable()->comment('Location in the page');
            $table->decimal('confidence_score', 5, 2)->nullable();
            $table->json('ai_metadata')->nullable()->comment('Additional AI extraction data');
            
            // Review and mapping
            $table->enum('review_status', [
                'pending',      // Waiting for review
                'approved',     // Approved for import
                'rejected',     // Rejected, won't import
                'needs_edit',   // Needs manual editing
                'duplicate'     // Duplicate of existing product
            ])->default('pending');
            
            $table->foreignId('matched_product_id')->nullable()->constrained('products')->comment('Existing product if duplicate');
            $table->boolean('create_new')->default(true)->comment('Whether to create new product');
            $table->boolean('update_existing')->default(false)->comment('Whether to update existing product');
            
            // Edited/corrected values (for manual corrections)
            $table->string('final_name')->nullable();
            $table->text('final_description')->nullable();
            $table->string('final_sku')->nullable();
            $table->string('final_brand')->nullable();
            $table->string('final_model')->nullable();
            $table->decimal('final_price', 12, 2)->nullable();
            $table->string('final_currency', 3)->nullable();
            $table->foreignId('final_category_id')->nullable()->constrained('categories');
            $table->json('final_specifications')->nullable();
            $table->json('final_features')->nullable();
            
            // Import tracking
            $table->foreignId('imported_product_id')->nullable()->constrained('products')->comment('Created product ID');
            $table->timestamp('imported_at')->nullable();
            $table->foreignId('reviewed_by')->nullable()->constrained('users');
            $table->timestamp('reviewed_at')->nullable();
            $table->text('review_notes')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['ingestion_id', 'review_status']);
            $table->index(['catalogue_id', 'page_number']);
            $table->index('review_status');
            $table->index('extracted_sku');
            $table->index('extracted_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('extracted_products');
    }
};