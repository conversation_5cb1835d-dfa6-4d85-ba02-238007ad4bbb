<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_variants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->string('sku')->nullable();
            $table->json('attrs_json')->nullable();
            $table->decimal('sa_price_zar', 12, 2);
            $table->string('currency')->default('ZAR');
            $table->string('barcode')->nullable();
            $table->enum('stock_status', ['in_stock', 'low', 'out'])->default('in_stock');
            $table->timestamp('price_last_seen_at')->nullable();
            $table->timestamps();
            
            $table->index('sku');
            $table->index('barcode');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_variants');
    }
};
