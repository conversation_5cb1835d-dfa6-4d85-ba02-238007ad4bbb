<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('exchange_rates', function (Blueprint $table) {
            $table->string('from_currency', 3)->default('ZAR')->after('id');
            $table->string('to_currency', 3)->default('USD')->after('from_currency');
            $table->decimal('rate', 10, 6)->after('to_currency');
            $table->boolean('is_active')->default(true)->after('rate');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('exchange_rates', function (Blueprint $table) {
            $table->dropColumn(['from_currency', 'to_currency', 'rate', 'is_active']);
        });
    }
};
