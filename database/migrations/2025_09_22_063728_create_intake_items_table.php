<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('intake_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['text', 'link', 'image']);
            $table->text('raw_text')->nullable();
            $table->string('raw_url')->nullable();
            $table->string('media_id')->nullable();
            $table->string('extracted_name')->nullable();
            $table->string('extracted_outlet')->nullable();
            $table->decimal('extracted_price', 10, 2)->nullable();
            $table->string('currency', 3)->default('ZAR');
            $table->integer('confidence')->default(0);
            $table->enum('status', ['parsed', 'needs_price', 'needs_outlet', 'ready'])->default('parsed');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('intake_items');
    }
};
