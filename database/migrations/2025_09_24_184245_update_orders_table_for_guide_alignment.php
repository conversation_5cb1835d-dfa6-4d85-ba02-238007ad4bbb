<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('reference')->nullable()->after('order_ref');
            $table->string('city')->nullable()->after('total_usd');
            $table->json('cart_snapshot')->nullable()->after('breakdown_json');

            $table->enum('status', [
                'NEW', 'IN_PROGRESS', 'FULFILLED', 'CANCELLED',
                'draft', 'quoted', 'pending_approval', 'approved',
                'procured', 'in_transit', 'arrived_zim', 'last_mile',
                'delivered', 'closed'
            ])->default('NEW')->change();

            $table->index('reference');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex(['reference']);
            $table->dropColumn(['reference', 'city', 'cart_snapshot']);

            $table->enum('status', [
                'draft', 'quoted', 'pending_approval', 'approved',
                'procured', 'in_transit', 'arrived_zim', 'last_mile',
                'delivered', 'closed'
            ])->default('pending_approval')->change();
        });
    }
};
