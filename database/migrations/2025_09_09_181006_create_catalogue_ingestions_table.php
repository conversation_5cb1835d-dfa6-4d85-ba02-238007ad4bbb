<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('catalogue_ingestions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('catalogue_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->comment('User who initiated ingestion');
            
            // Ingestion status tracking
            $table->enum('status', [
                'pending',      // Waiting to be processed
                'processing',   // Currently being processed by AI
                'extracting',   // Extracting products from content
                'review',       // Ready for manual review
                'approved',     // Approved and products imported
                'rejected',     // Rejected, won't be imported
                'failed'        // Processing failed
            ])->default('pending');
            
            // Processing details
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->integer('total_pages')->nullable();
            $table->integer('processed_pages')->default(0);
            $table->integer('products_found')->default(0);
            $table->integer('products_approved')->default(0);
            $table->integer('products_rejected')->default(0);
            
            // AI Processing metadata
            $table->json('ai_config')->nullable()->comment('AI model and parameters used');
            $table->json('extraction_metadata')->nullable()->comment('Metadata from extraction process');
            $table->decimal('confidence_score', 5, 2)->nullable()->comment('Overall confidence score');
            $table->text('processing_notes')->nullable();
            $table->text('error_message')->nullable();
            
            // Review details
            $table->foreignId('reviewed_by')->nullable()->constrained('users');
            $table->timestamp('reviewed_at')->nullable();
            $table->text('review_notes')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['catalogue_id', 'status']);
            $table->index('status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('catalogue_ingestions');
    }
};