<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quotes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->nullable()->constrained('customers')->onDelete('set null');
            $table->foreignId('cart_id')->nullable()->constrained('carts')->onDelete('set null');
            $table->foreignId('shop_id')->nullable()->constrained('shops')->onDelete('set null');
            $table->decimal('total_usd', 12, 2);
            $table->json('breakdown_json');
            $table->timestamp('valid_until');
            $table->enum('status', ['draft', 'sent', 'accepted', 'expired'])->default('draft');
            $table->json('fx_snapshot_json')->nullable();
            $table->timestamps();
            
            $table->index(['customer_id', 'status']);
            $table->index('valid_until');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quotes');
    }
};
