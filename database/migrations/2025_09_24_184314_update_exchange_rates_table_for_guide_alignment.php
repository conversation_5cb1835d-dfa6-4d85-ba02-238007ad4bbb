<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('exchange_rates', function (Blueprint $table) {
            $table->timestamp('as_of')->after('rate');

            $table->dropColumn(['usd_per_zar', 'effective_at', 'is_official']);

            $table->index(['pair', 'as_of']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('exchange_rates', function (Blueprint $table) {
            $table->dropIndex(['pair', 'as_of']);
            $table->dropColumn(['pair', 'rate', 'as_of']);

            $table->decimal('usd_per_zar', 10, 6)->after('source');
            $table->timestamp('effective_at')->after('usd_per_zar');
            $table->boolean('is_official')->default(true)->after('effective_at');

            $table->index(['effective_at', 'is_official']);
        });
    }
};
