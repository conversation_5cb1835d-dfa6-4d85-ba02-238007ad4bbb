<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing catalogue paths to remove /storage/ prefix if present
        DB::table('catalogues')->get()->each(function ($catalogue) {
            $updates = [];
            
            // Update cover_url
            if ($catalogue->cover_url && str_starts_with($catalogue->cover_url, '/storage/')) {
                $updates['cover_url'] = str_replace('/storage/', 'uploads/', $catalogue->cover_url);
            }
            
            // Update pdf_url
            if ($catalogue->pdf_url && str_starts_with($catalogue->pdf_url, '/storage/')) {
                $updates['pdf_url'] = str_replace('/storage/', 'uploads/', $catalogue->pdf_url);
            }
            
            // Update images array
            if ($catalogue->images) {
                $images = json_decode($catalogue->images, true);
                if (is_array($images)) {
                    $updatedImages = array_map(function($image) {
                        if (str_starts_with($image, '/storage/')) {
                            return str_replace('/storage/', 'uploads/', $image);
                        }
                        return $image;
                    }, $images);
                    $updates['images'] = json_encode($updatedImages);
                }
            }
            
            if (!empty($updates)) {
                DB::table('catalogues')
                    ->where('id', $catalogue->id)
                    ->update($updates);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert paths back to storage format
        DB::table('catalogues')->get()->each(function ($catalogue) {
            $updates = [];
            
            // Revert cover_url
            if ($catalogue->cover_url && str_starts_with($catalogue->cover_url, 'uploads/')) {
                $updates['cover_url'] = str_replace('uploads/', '/storage/', $catalogue->cover_url);
            }
            
            // Revert pdf_url
            if ($catalogue->pdf_url && str_starts_with($catalogue->pdf_url, 'uploads/')) {
                $updates['pdf_url'] = str_replace('uploads/', '/storage/', $catalogue->pdf_url);
            }
            
            // Revert images array
            if ($catalogue->images) {
                $images = json_decode($catalogue->images, true);
                if (is_array($images)) {
                    $updatedImages = array_map(function($image) {
                        if (str_starts_with($image, 'uploads/')) {
                            return str_replace('uploads/', '/storage/', $image);
                        }
                        return $image;
                    }, $images);
                    $updates['images'] = json_encode($updatedImages);
                }
            }
            
            if (!empty($updates)) {
                DB::table('catalogues')
                    ->where('id', $catalogue->id)
                    ->update($updates);
            }
        });
    }
};