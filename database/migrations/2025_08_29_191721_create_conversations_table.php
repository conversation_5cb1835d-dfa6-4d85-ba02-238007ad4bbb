<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conversations', function (Blueprint $table) {
            $table->id();
            $table->enum('channel', ['whatsapp', 'web']);
            $table->foreignId('customer_id')->nullable()->constrained('customers')->onDelete('set null');
            $table->string('wa_number')->nullable();
            $table->string('state')->default('active');
            $table->timestamp('last_message_at')->nullable();
            $table->timestamps();
            
            $table->index(['channel', 'state']);
            $table->index('wa_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conversations');
    }
};
