<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pricing_rules', function (Blueprint $table) {
            $table->id();
            $table->enum('scope_type', ['global', 'shop', 'category', 'product']);
            $table->unsignedBigInteger('scope_id')->nullable();
            $table->string('key');
            $table->text('value');
            $table->enum('value_type', ['percent', 'fixed', 'json']);
            $table->timestamp('effective_from')->nullable();
            $table->timestamp('effective_to')->nullable();
            $table->timestamps();
            
            $table->index(['scope_type', 'scope_id']);
            $table->index(['key', 'effective_from', 'effective_to']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pricing_rules');
    }
};
