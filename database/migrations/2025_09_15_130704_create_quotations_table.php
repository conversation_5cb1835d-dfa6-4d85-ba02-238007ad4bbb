<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('quotations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('conversation_id')->nullable()->constrained('conversations')->onDelete('set null');
            $table->foreignId('product_id')->nullable()->constrained('products')->onDelete('set null');
            $table->decimal('original_price_zar', 10, 2);
            $table->integer('quantity')->default(1);
            $table->decimal('exchange_rate', 10, 4);
            $table->decimal('product_price_usd', 10, 2);
            $table->decimal('markup_amount', 10, 2);
            $table->decimal('shipping_cost', 10, 2);
            $table->decimal('duties_amount', 10, 2)->default(0);
            $table->decimal('handling_fee', 10, 2)->default(0);
            $table->decimal('payment_processing_fee', 10, 2)->default(0);
            $table->decimal('landing_cost_usd', 10, 2);
            $table->json('breakdown')->nullable();
            $table->timestamp('valid_until');
            $table->string('status')->default('active'); // active, expired, accepted, declined
            $table->timestamps();

            $table->index(['conversation_id', 'status']);
            $table->index('valid_until');
            $table->index('created_at');
        });
    }

    public function down()
    {
        Schema::dropIfExists('quotations');
    }
};