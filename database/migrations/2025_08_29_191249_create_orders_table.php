<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('quote_id')->constrained('quotes');
            $table->foreignId('customer_id')->constrained('customers');
            $table->string('order_ref')->unique();
            $table->enum('status', [
                'draft', 'quoted', 'pending_approval', 'approved', 
                'procured', 'in_transit', 'arrived_zim', 'last_mile', 
                'delivered', 'closed'
            ])->default('pending_approval');
            $table->decimal('total_usd', 12, 2);
            $table->json('breakdown_json');
            $table->string('shipping_pref')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
            
            $table->index(['customer_id', 'status']);
            $table->index('order_ref');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
