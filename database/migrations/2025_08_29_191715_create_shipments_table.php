<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained('orders');
            $table->string('courier')->nullable();
            $table->string('tracking_no')->nullable();
            $table->enum('status', ['preparing', 'dispatched', 'in_transit', 'customs', 'arrived', 'delivered'])->default('preparing');
            $table->json('milestones_json')->nullable();
            $table->timestamps();
            
            $table->index(['order_id', 'status']);
            $table->index('tracking_no');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipments');
    }
};
