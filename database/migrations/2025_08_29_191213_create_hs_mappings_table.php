<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hs_mappings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained('categories')->onDelete('cascade');
            $table->string('hs_code');
            $table->decimal('duty_percent', 5, 2)->nullable();
            $table->decimal('levy_percent', 5, 2)->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
            
            $table->index('hs_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hs_mappings');
    }
};
