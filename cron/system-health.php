<?php
/**
 * cPanel System Health Check Script
 * 
 * Add this to your cPanel cron jobs (every 30 minutes):
 * (every 30 min) /usr/local/bin/php /home/<USER>/public_html/cron/system-health.php >/dev/null 2>&1
 */

// Bootstrap Laravel
require_once __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';

// Boot the application
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$kernel->bootstrap();

try {
    $healthData = [];
    $healthData['timestamp'] = now()->toDateTimeString();
    
    // Check database connectivity
    try {
        \Illuminate\Support\Facades\DB::connection()->getPdo();
        $healthData['database'] = 'connected';
    } catch (Exception $e) {
        $healthData['database'] = 'failed: ' . $e->getMessage();
    }
    
    // Check recent activity
    $healthData['leads_24h'] = \App\Models\Lead::where('created_at', '>=', now()->subDay())->count();
    $healthData['inquiries_24h'] = \App\Models\Inquiry::where('created_at', '>=', now()->subDay())->count();
    $healthData['notifications_sent_24h'] = \App\Models\Notification::where('status', 'sent')
        ->where('sent_at', '>=', now()->subDay())->count();
    $healthData['active_conversations'] = \App\Models\Conversation::where('last_message_at', '>=', now()->subDay())->count();
    
    // Check queue health
    $healthData['pending_jobs'] = \Illuminate\Support\Facades\DB::table('jobs')->count();
    $healthData['failed_jobs'] = \Illuminate\Support\Facades\DB::table('failed_jobs')->count();
    
    // Check pending notifications
    $healthData['pending_notifications'] = \App\Models\Notification::where('status', 'pending')->count();
    
    // Check overdue inquiries
    $healthData['overdue_inquiries'] = \App\Models\Inquiry::where('response_due_at', '<', now())
        ->whereNotIn('status', ['resolved', 'closed'])->count();
    
    // Log health data
    \Illuminate\Support\Facades\Log::info('System Health Check', $healthData);
    
    // Alert on critical issues
    if ($healthData['database'] !== 'connected') {
        error_log("[CRITICAL] Database connection failed");
    }
    
    if ($healthData['failed_jobs'] > 10) {
        error_log("[WARNING] High number of failed jobs: " . $healthData['failed_jobs']);
    }
    
    if ($healthData['overdue_inquiries'] > 5) {
        error_log("[WARNING] High number of overdue inquiries: " . $healthData['overdue_inquiries']);
    }
    
    echo json_encode($healthData, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    error_log("[HEALTH CHECK ERROR] " . $e->getMessage());
    echo json_encode(['error' => $e->getMessage()]);
    exit(1);
}

exit(0);
?>