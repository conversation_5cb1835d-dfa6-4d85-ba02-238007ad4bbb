<?php
/**
 * cPanel Cron Job Script for Laravel Scheduler
 * 
 * Add this to your cPanel cron jobs:
 * * * * * * /usr/local/bin/php /home/<USER>/public_html/cron/scheduler.php >/dev/null 2>&1
 */

// Bootstrap Laravel
require_once __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';

try {
    // Run the Laravel scheduler
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    
    $input = new Symfony\Component\Console\Input\ArrayInput([
        'command' => 'schedule:run'
    ]);
    
    $output = new Symfony\Component\Console\Output\BufferedOutput();
    
    $exitCode = $kernel->handle($input, $output);
    
    // Log the output
    $outputContent = $output->fetch();
    
    if ($exitCode === 0) {
        error_log("[CRON] Scheduler ran successfully: " . trim($outputContent));
    } else {
        error_log("[CRON ERROR] Scheduler failed with exit code $exitCode: " . trim($outputContent));
    }
    
} catch (Exception $e) {
    error_log("[CRON EXCEPTION] " . $e->getMessage());
    exit(1);
}

exit($exitCode ?? 0);
?>