<?php
/**
 * cPanel Queue Worker Script
 * 
 * Add this to your cPanel cron jobs (every 5 minutes):
 * (every 5 min) /usr/local/bin/php /home/<USER>/public_html/cron/queue-worker.php >/dev/null 2>&1
 */

// Bootstrap Laravel
require_once __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';

try {
    // Check if queue worker should run
    $lockFile = __DIR__ . '/queue-worker.lock';
    $maxRunTime = 300; // 5 minutes
    
    // Check if worker is already running
    if (file_exists($lockFile)) {
        $lockTime = filemtime($lockFile);
        if (time() - $lockTime < $maxRunTime) {
            // Worker is still running, exit
            exit(0);
        } else {
            // Lock file is stale, remove it
            unlink($lockFile);
        }
    }
    
    // Create lock file
    touch($lockFile);
    
    // Run queue worker for limited time
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    
    $input = new Symfony\Component\Console\Input\ArrayInput([
        'command' => 'queue:work',
        '--max-jobs' => 10,
        '--max-time' => $maxRunTime - 30, // Leave buffer for cleanup
        '--timeout' => 60,
        '--tries' => 3,
        '--queue' => 'notifications,default',
    ]);
    
    $output = new Symfony\Component\Console\Output\BufferedOutput();
    
    $exitCode = $kernel->handle($input, $output);
    
    // Log the output
    $outputContent = $output->fetch();
    error_log("[QUEUE WORKER] " . trim($outputContent));
    
} catch (Exception $e) {
    error_log("[QUEUE WORKER ERROR] " . $e->getMessage());
} finally {
    // Always remove lock file
    if (file_exists($lockFile)) {
        unlink($lockFile);
    }
}

exit(0);
?>