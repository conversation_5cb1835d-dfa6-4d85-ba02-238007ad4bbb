<?php

namespace App\Enums;

enum ConversationState: string
{
    case INITIAL = 'initial';
    case PRODUCT_CAPTURE = 'product_capture';
    case QUOTATION_PROVIDED = 'quotation_provided';
    case COLLECTING_DETAILS = 'collecting_details';
    case ORDER_CONFIRMATION = 'order_confirmation';
    case PAYMENT_PENDING = 'payment_pending';
    case ESCALATED = 'escalated';
    case COMPLETED = 'completed';
    case ABANDONED = 'abandoned';

    public function getLabel(): string
    {
        return match($this) {
            self::INITIAL => 'Initial Contact',
            self::PRODUCT_CAPTURE => 'Capturing Product Details',
            self::QUOTATION_PROVIDED => 'Quotation Provided',
            self::COLLECTING_DETAILS => 'Collecting Customer Details',
            self::ORDER_CONFIRMATION => 'Order Confirmation',
            self::PAYMENT_PENDING => 'Payment Pending',
            self::ESCALATED => 'Escalated to Agent',
            self::COMPLETED => 'Completed',
            self::ABANDONED => 'Abandoned',
        };
    }

    public function canTransitionTo(self $newState): bool
    {
        $allowedTransitions = match($this) {
            self::INITIAL => [
                self::PRODUCT_CAPTURE,
                self::ESCALATED,
                self::ABANDONED
            ],
            self::PRODUCT_CAPTURE => [
                self::QUOTATION_PROVIDED,
                self::ESCALATED,
                self::ABANDONED
            ],
            self::QUOTATION_PROVIDED => [
                self::COLLECTING_DETAILS,
                self::PRODUCT_CAPTURE,
                self::ESCALATED,
                self::ABANDONED
            ],
            self::COLLECTING_DETAILS => [
                self::ORDER_CONFIRMATION,
                self::ESCALATED,
                self::ABANDONED
            ],
            self::ORDER_CONFIRMATION => [
                self::PAYMENT_PENDING,
                self::COMPLETED,
                self::ESCALATED
            ],
            self::PAYMENT_PENDING => [
                self::COMPLETED,
                self::ESCALATED
            ],
            self::ESCALATED => [
                self::COMPLETED,
                self::ABANDONED
            ],
            self::COMPLETED => [],
            self::ABANDONED => [self::INITIAL],
        };

        return in_array($newState, $allowedTransitions);
    }
}