<?php

namespace App\Enums;

enum MessageIntent: string
{
    case GREETING = 'greeting';
    case PRODUCT_INQUIRY = 'product_inquiry';
    case PRICE_REQUEST = 'price_request';
    case ACCEPT_QUOTE = 'accept_quote';
    case DECLINE_QUOTE = 'decline_quote';
    case PROVIDE_DETAILS = 'provide_details';
    case ASK_QUESTION = 'ask_question';
    case REQUEST_AGENT = 'request_agent';
    case CONFIRM_ORDER = 'confirm_order';
    case CANCEL_ORDER = 'cancel_order';
    case TRACK_ORDER = 'track_order';
    case COMPLAINT = 'complaint';
    case FEEDBACK = 'feedback';
    case UNKNOWN = 'unknown';

    public function getLabel(): string
    {
        return match($this) {
            self::GREETING => 'Greeting',
            self::PRODUCT_INQUIRY => 'Product Inquiry',
            self::PRICE_REQUEST => 'Price Request',
            self::ACCEPT_QUOTE => 'Accept Quote',
            self::DECLINE_QUOTE => 'Decline Quote',
            self::PROVIDE_DETAILS => 'Provide Details',
            self::ASK_QUESTION => 'Ask Question',
            self::REQUEST_AGENT => 'Request Agent',
            self::CONFIRM_ORDER => 'Confirm Order',
            self::CANCEL_ORDER => 'Cancel Order',
            self::TRACK_ORDER => 'Track Order',
            self::COMPLAINT => 'Complaint',
            self::FEEDBACK => 'Feedback',
            self::UNKNOWN => 'Unknown',
        };
    }

    public function requiresHumanIntervention(): bool
    {
        return match($this) {
            self::REQUEST_AGENT, self::COMPLAINT => true,
            default => false,
        };
    }

    public function getPriority(): int
    {
        return match($this) {
            self::CONFIRM_ORDER, self::ACCEPT_QUOTE => 5,
            self::PRODUCT_INQUIRY, self::PRICE_REQUEST => 4,
            self::ASK_QUESTION, self::PROVIDE_DETAILS => 3,
            self::GREETING, self::FEEDBACK => 2,
            self::COMPLAINT, self::REQUEST_AGENT => 5,
            default => 1,
        };
    }
}