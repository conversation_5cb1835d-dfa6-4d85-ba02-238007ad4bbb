<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Models\ExtractedProduct;
use App\Models\CatalogueIngestion;
use App\Services\CatalogueIngestionService;
use App\Jobs\ImportApprovedProducts;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ProductReview extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-eye';
    
    protected static string $view = 'filament.pages.product-review';
    
    protected static ?string $navigationGroup = 'AI & Processing';
    
    protected static ?string $title = 'Product Review Center';
    
    protected static ?int $navigationSort = 2;

    public function mount(): void
    {
        // You can add any initialization logic here
    }

    /**
     * Get review statistics
     */
    public function getStats(): array
    {
        $totalProducts = ExtractedProduct::count();
        $pendingProducts = ExtractedProduct::where('review_status', 'pending')->count();
        $approvedProducts = ExtractedProduct::where('review_status', 'approved')->count();
        $rejectedProducts = ExtractedProduct::where('review_status', 'rejected')->count();
        $importedProducts = ExtractedProduct::whereNotNull('imported_product_id')->count();
        $avgConfidence = ExtractedProduct::where('review_status', 'pending')->avg('confidence_score') ?? 0;

        return [
            Stat::make('Total Extracted', number_format($totalProducts))
                ->description('Products found by AI')
                ->color('primary'),
                
            Stat::make('Pending Review', $pendingProducts)
                ->description('Awaiting manual review')
                ->color($pendingProducts > 0 ? 'warning' : 'success'),
                
            Stat::make('Approved', number_format($approvedProducts))
                ->description('Ready for import')
                ->color('success'),
                
            Stat::make('Imported', number_format($importedProducts))
                ->description('Added to catalog')
                ->color('info'),
                
            Stat::make('Avg Confidence', number_format($avgConfidence, 1) . '%')
                ->description('For pending items')
                ->color($avgConfidence >= 80 ? 'success' : ($avgConfidence >= 60 ? 'warning' : 'danger')),
        ];
    }

    /**
     * Get products by ingestion for review
     */
    public function getProductsByIngestion(): array
    {
        return CatalogueIngestion::with(['catalogue.shop', 'extractedProducts' => function ($query) {
            $query->where('review_status', 'pending');
        }])
        ->whereHas('extractedProducts', function ($query) {
            $query->where('review_status', 'pending');
        })
        ->latest()
        ->get()
        ->map(function ($ingestion) {
            $approvedForImport = $ingestion->extractedProducts()
                ->where('review_status', 'approved')
                ->whereNull('imported_product_id')
                ->count();

            return [
                'ingestion_id' => $ingestion->id,
                'catalogue_title' => $ingestion->catalogue->title,
                'shop_name' => $ingestion->catalogue->shop->name,
                'pending_count' => $ingestion->extractedProducts->count(),
                'approved_for_import' => $approvedForImport,
                'avg_confidence' => $ingestion->extractedProducts->avg('confidence_score'),
                'created_at' => $ingestion->created_at->diffForHumans(),
                'products' => $ingestion->extractedProducts->map(function ($product) {
                    return [
                        'id' => $product->id,
                        'name' => $product->extracted_name,
                        'price' => $product->extracted_price,
                        'currency' => $product->extracted_currency ?? 'USD',
                        'brand' => $product->extracted_brand,
                        'category' => $product->extracted_category,
                        'confidence_score' => $product->confidence_score,
                        'page_number' => $product->page_number,
                    ];
                })->toArray(),
            ];
        })
        ->toArray();
    }

    /**
     * Get high priority products needing immediate review
     */
    public function getHighPriorityProducts(): array
    {
        return ExtractedProduct::with(['catalogue.shop', 'ingestion'])
            ->where('review_status', 'pending')
            ->where(function ($query) {
                $query->where('confidence_score', '>=', 90)
                      ->orWhere('extracted_price', '>', 100)
                      ->orWhereNotNull('extracted_sku');
            })
            ->latest()
            ->limit(20)
            ->get()
            ->map(function ($product) {
                $priority = 'medium';
                $reason = [];
                
                if ($product->confidence_score >= 90) {
                    $priority = 'high';
                    $reason[] = 'High AI confidence';
                }
                
                if ($product->extracted_price > 100) {
                    $priority = 'high';
                    $reason[] = 'High value item';
                }
                
                if ($product->extracted_sku) {
                    $priority = 'high';
                    $reason[] = 'Has SKU';
                }

                return [
                    'id' => $product->id,
                    'name' => $product->extracted_name,
                    'shop_name' => $product->shop->name,
                    'catalogue_title' => $product->catalogue->title,
                    'price' => $product->extracted_price,
                    'currency' => $product->extracted_currency ?? 'USD',
                    'brand' => $product->extracted_brand,
                    'sku' => $product->extracted_sku,
                    'category' => $product->extracted_category,
                    'confidence_score' => $product->confidence_score,
                    'page_number' => $product->page_number,
                    'priority' => $priority,
                    'priority_reasons' => $reason,
                    'created_at' => $product->created_at->diffForHumans(),
                ];
            })
            ->toArray();
    }

    /**
     * Approve a product
     */
    public function approveProduct($productId)
    {
        $product = ExtractedProduct::find($productId);
        
        if (!$product || $product->review_status !== 'pending') {
            Notification::make()
                ->title('Cannot approve product')
                ->body('Product not found or already reviewed.')
                ->danger()
                ->send();
            return;
        }

        $product->approve(auth()->id(), 'Approved via Product Review Center');
        
        Notification::make()
            ->title('Product approved')
            ->body('Product has been approved for import.')
            ->success()
            ->send();
            
        $this->redirect(static::getUrl());
    }

    /**
     * Reject a product
     */
    public function rejectProduct($productId, $reason = 'Rejected via review interface')
    {
        $product = ExtractedProduct::find($productId);
        
        if (!$product || $product->review_status !== 'pending') {
            Notification::make()
                ->title('Cannot reject product')
                ->body('Product not found or already reviewed.')
                ->danger()
                ->send();
            return;
        }

        $product->reject(auth()->id(), $reason);
        
        Notification::make()
            ->title('Product rejected')
            ->body('Product has been rejected.')
            ->success()
            ->send();
            
        $this->redirect(static::getUrl());
    }

    /**
     * Bulk approve products from an ingestion
     */
    public function bulkApproveIngestion($ingestionId)
    {
        $service = app(CatalogueIngestionService::class);
        
        $products = ExtractedProduct::where('ingestion_id', $ingestionId)
            ->where('review_status', 'pending')
            ->pluck('id')
            ->toArray();
            
        if (empty($products)) {
            Notification::make()
                ->title('No products to approve')
                ->body('All products in this ingestion have already been reviewed.')
                ->warning()
                ->send();
            return;
        }

        $count = $service->bulkApprove($products, auth()->id());
        
        Notification::make()
            ->title("Approved {$count} products")
            ->body('Products are ready for import.')
            ->success()
            ->send();
            
        $this->redirect(static::getUrl());
    }

    /**
     * Bulk reject products from an ingestion
     */
    public function bulkRejectIngestion($ingestionId, $reason = 'Bulk rejected via review interface')
    {
        $service = app(CatalogueIngestionService::class);
        
        $products = ExtractedProduct::where('ingestion_id', $ingestionId)
            ->where('review_status', 'pending')
            ->pluck('id')
            ->toArray();
            
        if (empty($products)) {
            Notification::make()
                ->title('No products to reject')
                ->body('All products in this ingestion have already been reviewed.')
                ->warning()
                ->send();
            return;
        }

        $count = $service->bulkReject($products, auth()->id(), $reason);
        
        Notification::make()
            ->title("Rejected {$count} products")
            ->body('Products have been rejected.')
            ->success()
            ->send();
            
        $this->redirect(static::getUrl());
    }

    /**
     * Import approved products from an ingestion
     */
    public function importIngestionProducts($ingestionId)
    {
        $ingestion = CatalogueIngestion::find($ingestionId);
        
        if (!$ingestion) {
            Notification::make()
                ->title('Ingestion not found')
                ->danger()
                ->send();
            return;
        }

        $approvedCount = $ingestion->extractedProducts()
            ->where('review_status', 'approved')
            ->whereNull('imported_product_id')
            ->count();

        if ($approvedCount === 0) {
            Notification::make()
                ->title('No products to import')
                ->body('All approved products have already been imported.')
                ->warning()
                ->send();
            return;
        }

        ImportApprovedProducts::dispatch($ingestion);
        
        Notification::make()
            ->title("Importing {$approvedCount} approved products")
            ->body('Products are being added to the main catalog.')
            ->success()
            ->send();
            
        $this->redirect(static::getUrl());
    }

    /**
     * Get review status breakdown
     */
    public function getStatusBreakdown(): array
    {
        return ExtractedProduct::select('review_status', DB::raw('count(*) as count'))
            ->groupBy('review_status')
            ->get()
            ->pluck('count', 'review_status')
            ->toArray();
    }
}