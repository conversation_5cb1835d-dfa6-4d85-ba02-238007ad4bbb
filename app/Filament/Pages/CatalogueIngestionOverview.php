<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Models\CatalogueIngestion;
use App\Models\ExtractedProduct;
use App\Models\Catalogue;
use App\Services\CatalogueIngestionService;
use App\Jobs\ProcessCatalogueIngestion;
use App\Jobs\ImportApprovedProducts;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CatalogueIngestionOverview extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-cpu-chip';
    
    protected static string $view = 'filament.pages.catalogue-ingestion-overview';
    
    protected static ?string $navigationGroup = 'AI & Processing';
    
    protected static ?string $title = 'AI Catalog Processing';
    
    protected static ?int $navigationSort = 1;

    public function mount(): void
    {
        // You can add any initialization logic here
    }

    /**
     * Get ingestion statistics for the dashboard
     */
    public function getStats(): array
    {
        $totalIngestions = CatalogueIngestion::count();
        $pendingIngestions = CatalogueIngestion::where('status', 'pending')->count();
        $processingIngestions = CatalogueIngestion::whereIn('status', ['processing', 'extracting'])->count();
        $reviewIngestions = CatalogueIngestion::where('status', 'review')->count();
        $totalProductsExtracted = ExtractedProduct::count();
        $pendingProducts = ExtractedProduct::where('review_status', 'pending')->count();
        $approvedProducts = ExtractedProduct::where('review_status', 'approved')->count();
        $avgConfidence = ExtractedProduct::avg('confidence_score') ?? 0;

        return [
            Stat::make('Total Ingestions', number_format($totalIngestions))
                ->description($pendingIngestions . ' pending')
                ->color('primary'),
                
            Stat::make('Ready for Review', $reviewIngestions)
                ->description($processingIngestions . ' processing')
                ->color($reviewIngestions > 0 ? 'warning' : 'success'),
                
            Stat::make('Products Extracted', number_format($totalProductsExtracted))
                ->description($pendingProducts . ' awaiting review')
                ->color('info'),
                
            Stat::make('Products Approved', number_format($approvedProducts))
                ->description('Ready for import')
                ->color('success'),
                
            Stat::make('Avg AI Confidence', number_format($avgConfidence, 1) . '%')
                ->description('AI extraction accuracy')
                ->color($avgConfidence >= 80 ? 'success' : ($avgConfidence >= 60 ? 'warning' : 'danger')),
        ];
    }

    /**
     * Get recent ingestions
     */
    public function getRecentIngestions(): array
    {
        return CatalogueIngestion::with(['catalogue.shop', 'user'])
            ->latest()
            ->limit(10)
            ->get()
            ->map(function ($ingestion) {
                $readyForImport = $ingestion->extractedProducts()
                    ->where('review_status', 'approved')
                    ->whereNull('imported_product_id')
                    ->count();

                return [
                    'id' => $ingestion->id,
                    'catalogue_title' => $ingestion->catalogue->title,
                    'shop_name' => $ingestion->catalogue->shop->name,
                    'status' => $ingestion->status,
                    'progress' => $ingestion->getProgressPercentage(),
                    'products_found' => $ingestion->products_found,
                    'products_approved' => $ingestion->products_approved,
                    'ready_for_import' => $readyForImport,
                    'confidence_score' => $ingestion->confidence_score,
                    'created_at' => $ingestion->created_at->diffForHumans(),
                    'user_name' => $ingestion->user->name,
                ];
            })
            ->toArray();
    }

    /**
     * Get pending products for review
     */
    public function getPendingProducts(): array
    {
        return ExtractedProduct::with(['catalogue.shop', 'ingestion'])
            ->where('review_status', 'pending')
            ->latest()
            ->limit(20)
            ->get()
            ->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->extracted_name,
                    'shop_name' => $product->shop->name,
                    'catalogue_title' => $product->catalogue->title,
                    'price' => $product->extracted_price,
                    'currency' => $product->extracted_currency ?? 'USD',
                    'brand' => $product->extracted_brand,
                    'category' => $product->extracted_category,
                    'confidence_score' => $product->confidence_score,
                    'page_number' => $product->page_number,
                    'created_at' => $product->created_at->diffForHumans(),
                ];
            })
            ->toArray();
    }

    /**
     * Get status breakdown
     */
    public function getStatusBreakdown(): array
    {
        $statuses = CatalogueIngestion::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status')
            ->toArray();

        return [
            'pending' => $statuses['pending'] ?? 0,
            'processing' => $statuses['processing'] ?? 0,
            'extracting' => $statuses['extracting'] ?? 0,
            'review' => $statuses['review'] ?? 0,
            'approved' => $statuses['approved'] ?? 0,
            'rejected' => $statuses['rejected'] ?? 0,
            'failed' => $statuses['failed'] ?? 0,
        ];
    }

    /**
     * Get confidence distribution
     */
    public function getConfidenceDistribution(): array
    {
        $products = ExtractedProduct::select('confidence_score')->get();
        
        return [
            'high' => $products->where('confidence_score', '>=', 80)->count(),
            'medium' => $products->where('confidence_score', '>=', 60)->where('confidence_score', '<', 80)->count(),
            'low' => $products->where('confidence_score', '<', 60)->count(),
        ];
    }

    /**
     * Start processing an ingestion
     */
    public function startProcessing($ingestionId)
    {
        $ingestion = CatalogueIngestion::find($ingestionId);
        
        if (!$ingestion || $ingestion->status !== 'pending') {
            Notification::make()
                ->title('Cannot start processing')
                ->body('Ingestion not found or not in pending status.')
                ->danger()
                ->send();
            return;
        }

        ProcessCatalogueIngestion::dispatch($ingestion);
        
        Notification::make()
            ->title('Processing started')
            ->body('The AI is now extracting products from the catalogue.')
            ->success()
            ->send();
            
        $this->redirect(static::getUrl());
    }

    /**
     * Bulk approve products
     */
    public function bulkApproveProducts($ingestionId)
    {
        $ingestion = CatalogueIngestion::find($ingestionId);
        
        if (!$ingestion) {
            Notification::make()
                ->title('Ingestion not found')
                ->danger()
                ->send();
            return;
        }

        $count = $ingestion->extractedProducts()
            ->where('review_status', 'pending')
            ->update([
                'review_status' => 'approved',
                'reviewed_by' => auth()->id(),
                'reviewed_at' => now(),
            ]);

        ImportApprovedProducts::dispatch($ingestion);
        
        Notification::make()
            ->title("Approved {$count} products")
            ->body('Products are being imported to the catalog.')
            ->success()
            ->send();
            
        $this->redirect(static::getUrl());
    }

    /**
     * Import approved products to main catalog
     */
    public function importApprovedProducts($ingestionId)
    {
        $ingestion = CatalogueIngestion::find($ingestionId);
        
        if (!$ingestion) {
            Notification::make()
                ->title('Ingestion not found')
                ->danger()
                ->send();
            return;
        }

        $approvedCount = $ingestion->extractedProducts()
            ->where('review_status', 'approved')
            ->whereNull('imported_product_id')
            ->count();

        if ($approvedCount === 0) {
            Notification::make()
                ->title('No products to import')
                ->body('All approved products have already been imported.')
                ->warning()
                ->send();
            return;
        }

        ImportApprovedProducts::dispatch($ingestion);
        
        Notification::make()
            ->title("Importing {$approvedCount} approved products")
            ->body('Products are being added to the main catalog.')
            ->success()
            ->send();
            
        $this->redirect(static::getUrl());
    }

    /**
     * Get available catalogues for processing
     */
    public function getAvailableCatalogues(): array
    {
        return Catalogue::with('shop')
            ->whereDoesntHave('ingestions', function ($query) {
                $query->whereIn('status', ['pending', 'processing', 'extracting']);
            })
            ->latest()
            ->limit(10)
            ->get()
            ->map(function ($catalogue) {
                return [
                    'id' => $catalogue->id,
                    'title' => $catalogue->title,
                    'shop_name' => $catalogue->shop->name,
                    'type' => $catalogue->type,
                    'page_count' => $catalogue->page_count,
                    'created_at' => $catalogue->created_at->diffForHumans(),
                ];
            })
            ->toArray();
    }

    /**
     * Start new ingestion for a catalogue
     */
    public function startNewIngestion($catalogueId)
    {
        $catalogue = Catalogue::find($catalogueId);
        
        if (!$catalogue) {
            Notification::make()
                ->title('Catalogue not found')
                ->danger()
                ->send();
            return;
        }

        // Check if there's already a pending/processing ingestion
        $existingIngestion = CatalogueIngestion::where('catalogue_id', $catalogue->id)
            ->whereIn('status', ['pending', 'processing', 'extracting'])
            ->first();
            
        if ($existingIngestion) {
            Notification::make()
                ->title('Extraction already in progress')
                ->body('This catalogue is already being processed.')
                ->warning()
                ->send();
            return;
        }

        // Create new ingestion
        $ingestionService = app(CatalogueIngestionService::class);
        $ingestion = $ingestionService->startIngestion($catalogue, auth()->id());
        
        // Queue the processing job
        ProcessCatalogueIngestion::dispatch($ingestion);
        
        Notification::make()
            ->title('Product extraction started')
            ->body('The AI will process this catalogue and extract products.')
            ->success()
            ->send();
            
        $this->redirect(static::getUrl());
    }
}