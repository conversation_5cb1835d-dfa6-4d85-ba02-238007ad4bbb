<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Models\Conversation;
use App\Models\ConversationIntent;
use App\Models\ConversationAnalytics;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class ConversationInsights extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-bottom-center-text';
    
    protected static string $view = 'filament.pages.conversation-insights';
    
    protected static ?string $navigationGroup = 'AI Analytics';
    
    protected static ?string $title = 'AI Conversation Insights';
    
    protected static ?int $navigationSort = 1;

    public function mount(): void
    {
        // You can add any initialization logic here
    }

    /**
     * Get conversation statistics for the dashboard
     */
    public function getStats(): array
    {
        $today = now()->startOfDay();
        $thisWeek = now()->startOfWeek();
        $thisMonth = now()->startOfMonth();

        // Total conversations
        $totalConversations = Conversation::count();
        $todayConversations = Conversation::where('created_at', '>=', $today)->count();
        
        // Intent detection stats
        $withIntent = Conversation::whereNotNull('conversation_intent_id')->count();
        $intentAccuracy = $totalConversations > 0 ? round(($withIntent / $totalConversations) * 100, 1) : 0;
        
        // Lead scoring stats
        $highValueLeads = ConversationAnalytics::where('satisfaction_score', '>=', 4)->count();
        $avgSatisfaction = ConversationAnalytics::avg('satisfaction_score') ?? 0;
        
        // Completion stats
        $completed = Conversation::where('completion_status', 'successful')->count();
        $completionRate = $totalConversations > 0 ? round(($completed / $totalConversations) * 100, 1) : 0;

        return [
            Stat::make('Total Conversations', number_format($totalConversations))
                ->description($todayConversations . ' today')
                ->color('primary'),
                
            Stat::make('Intent Detection Rate', $intentAccuracy . '%')
                ->description($withIntent . ' / ' . $totalConversations . ' detected')
                ->color($intentAccuracy > 80 ? 'success' : ($intentAccuracy > 60 ? 'warning' : 'danger')),
                
            Stat::make('High-Value Leads', $highValueLeads)
                ->description('Satisfaction ≥ 4/5')
                ->color('success'),
                
            Stat::make('Completion Rate', $completionRate . '%')
                ->description($completed . ' successfully completed')
                ->color($completionRate > 70 ? 'success' : ($completionRate > 50 ? 'warning' : 'danger')),
                
            Stat::make('Avg Satisfaction', number_format($avgSatisfaction, 1) . '/5')
                ->description('Customer satisfaction score')
                ->color($avgSatisfaction >= 4 ? 'success' : ($avgSatisfaction >= 3 ? 'warning' : 'danger')),
        ];
    }

    /**
     * Get intent breakdown data
     */
    public function getIntentBreakdown(): array
    {
        return Conversation::whereNotNull('conversation_intent_id')
            ->with('intent')
            ->get()
            ->groupBy('intent.name')
            ->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'avg_confidence' => $group->avg('confidence_score') ?? 0,
                    'completion_rate' => $group->where('completion_status', 'successful')->count() / $group->count() * 100,
                ];
            })
            ->toArray();
    }

    /**
     * Get recent high-priority conversations
     */
    public function getHighPriorityConversations(): array
    {
        return Conversation::with(['intent', 'analytics'])
            ->where('created_at', '>=', now()->subDays(7))
            ->whereHas('analytics', function ($query) {
                $query->where('satisfaction_score', '>=', 4);
            })
            ->orWhere('completion_status', 'pending')
            ->whereHas('intent', function ($query) {
                $query->whereIn('slug', ['place-order', 'track-order', 'customer-support']);
            })
            ->latest()
            ->limit(10)
            ->get()
            ->map(function ($conversation) {
                return [
                    'id' => $conversation->id,
                    'phone' => substr($conversation->wa_number, -4),
                    'intent' => $conversation->intent?->name ?? 'Unknown',
                    'confidence' => round(($conversation->confidence_score ?? 0) * 100),
                    'status' => $conversation->completion_status,
                    'created_at' => $conversation->created_at->diffForHumans(),
                    'satisfaction' => $conversation->analytics?->satisfaction_score ?? 0,
                    'message_count' => $conversation->messages()->count(),
                ];
            })
            ->toArray();
    }

    /**
     * Get AI performance metrics
     */
    public function getAIPerformance(): array
    {
        $conversations = Conversation::with(['intent', 'analytics'])->get();
        
        $totalWithAI = $conversations->where('confidence_score', '>', 0)->count();
        $highConfidence = $conversations->where('confidence_score', '>=', 0.8)->count();
        $lowConfidence = $conversations->where('confidence_score', '>', 0)->where('confidence_score', '<', 0.5)->count();
        
        $avgConfidence = $conversations->where('confidence_score', '>', 0)->avg('confidence_score') ?? 0;
        $avgSatisfaction = $conversations->flatMap->analytics->whereNotNull('satisfaction_score')->avg('satisfaction_score') ?? 0;
        
        return [
            'total_ai_processed' => $totalWithAI,
            'high_confidence_rate' => $totalWithAI > 0 ? round(($highConfidence / $totalWithAI) * 100, 1) : 0,
            'low_confidence_rate' => $totalWithAI > 0 ? round(($lowConfidence / $totalWithAI) * 100, 1) : 0,
            'avg_confidence' => round($avgConfidence * 100, 1),
            'avg_satisfaction' => round($avgSatisfaction, 2),
            'confidence_distribution' => [
                'high' => $highConfidence,
                'medium' => $conversations->where('confidence_score', '>=', 0.5)->where('confidence_score', '<', 0.8)->count(),
                'low' => $lowConfidence,
            ],
        ];
    }

    /**
     * Get trending insights
     */
    public function getTrends(): array
    {
        $last7Days = collect();
        
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $dayStart = $date->startOfDay();
            $dayEnd = $date->copy()->endOfDay();
            
            $dayConversations = Conversation::whereBetween('created_at', [$dayStart, $dayEnd])->get();
            
            $last7Days->push([
                'date' => $date->format('M j'),
                'conversations' => $dayConversations->count(),
                'intents_detected' => $dayConversations->whereNotNull('conversation_intent_id')->count(),
                'completed' => $dayConversations->where('completion_status', 'successful')->count(),
                'high_confidence' => $dayConversations->where('confidence_score', '>=', 0.8)->count(),
            ]);
        }
        
        return $last7Days->toArray();
    }

    /**
     * Get follow-up recommendations
     */
    public function getFollowUpRecommendations(): array
    {
        $pendingConversations = Conversation::with(['intent', 'analytics'])
            ->where('completion_status', 'pending')
            ->where('created_at', '>=', now()->subDays(3))
            ->get();

        $recommendations = [];

        foreach ($pendingConversations as $conversation) {
            $priority = 'medium';
            $action = 'follow_up';
            $reason = 'Pending conversation';

            // High-value intents get priority
            if ($conversation->intent && in_array($conversation->intent->slug, ['place-order', 'track-order'])) {
                $priority = 'high';
                $action = 'immediate_contact';
                $reason = 'High-value intent: ' . $conversation->intent->name;
            }

            // High confidence gets priority
            if (($conversation->confidence_score ?? 0) >= 0.8) {
                $priority = 'high';
                $reason = 'High AI confidence: ' . round($conversation->confidence_score * 100) . '%';
            }

            // Old conversations need attention
            if ($conversation->created_at->diffInHours(now()) > 24) {
                $priority = 'high';
                $action = 'urgent_follow_up';
                $reason = 'Conversation aging: ' . $conversation->created_at->diffForHumans();
            }

            $recommendations[] = [
                'conversation_id' => $conversation->id,
                'phone' => '***' . substr($conversation->wa_number, -4),
                'intent' => $conversation->intent?->name ?? 'Unknown',
                'priority' => $priority,
                'action' => $action,
                'reason' => $reason,
                'age' => $conversation->created_at->diffForHumans(),
                'message_count' => $conversation->messages()->count(),
            ];
        }

        // Sort by priority and age
        usort($recommendations, function ($a, $b) {
            $priorityOrder = ['high' => 3, 'medium' => 2, 'low' => 1];
            if ($priorityOrder[$a['priority']] !== $priorityOrder[$b['priority']]) {
                return $priorityOrder[$b['priority']] - $priorityOrder[$a['priority']];
            }
            return 0; // Keep original order for same priority
        });

        return array_slice($recommendations, 0, 20); // Limit to top 20
    }
}