<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CatalogueIngestionResource\Pages;
use App\Models\CatalogueIngestion;
use App\Services\CatalogueIngestionService;
use App\Jobs\ImportApprovedProducts;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;

class CatalogueIngestionResource extends Resource
{
    protected static ?string $model = CatalogueIngestion::class;

    protected static ?string $navigationIcon = 'heroicon-o-cpu-chip';
    
    protected static ?string $navigationGroup = 'System';
    
    protected static ?string $navigationLabel = 'Catalog Ingestion (Legacy)';
    
    protected static ?int $navigationSort = 99;
    
    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Ingestion Details')
                    ->schema([
                        Forms\Components\Select::make('catalogue_id')
                            ->relationship('catalogue', 'title')
                            ->required()
                            ->searchable()
                            ->preload(),
                            
                        Forms\Components\Select::make('status')
                            ->options([
                                'pending' => 'Pending',
                                'processing' => 'Processing',
                                'extracting' => 'Extracting',
                                'review' => 'Ready for Review',
                                'approved' => 'Approved',
                                'rejected' => 'Rejected',
                                'failed' => 'Failed',
                            ])
                            ->required(),
                            
                        Forms\Components\Textarea::make('processing_notes')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                    
                Forms\Components\Section::make('Progress')
                    ->schema([
                        Forms\Components\TextInput::make('total_pages')
                            ->numeric(),
                            
                        Forms\Components\TextInput::make('processed_pages')
                            ->numeric(),
                            
                        Forms\Components\TextInput::make('products_found')
                            ->numeric()
                            ->disabled(),
                            
                        Forms\Components\TextInput::make('products_approved')
                            ->numeric()
                            ->disabled(),
                            
                        Forms\Components\TextInput::make('products_rejected')
                            ->numeric()
                            ->disabled(),
                            
                        Forms\Components\TextInput::make('confidence_score')
                            ->numeric()
                            ->suffix('%'),
                    ])
                    ->columns(3),
                    
                Forms\Components\Section::make('Review')
                    ->schema([
                        Forms\Components\Select::make('reviewed_by')
                            ->relationship('reviewer', 'name')
                            ->searchable(),
                            
                        Forms\Components\DateTimePicker::make('reviewed_at'),
                        
                        Forms\Components\Textarea::make('review_notes')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('catalogue.title')
                    ->searchable()
                    ->sortable()
                    ->limit(30),
                    
                Tables\Columns\TextColumn::make('catalogue.shop.name')
                    ->label('Shop')
                    ->searchable()
                    ->sortable(),
                    
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'gray',
                        'processing', 'extracting' => 'info',
                        'review' => 'warning',
                        'approved' => 'success',
                        'rejected', 'failed' => 'danger',
                        default => 'gray',
                    })
                    ->icon(fn (string $state): string => match ($state) {
                        'pending' => 'heroicon-o-clock',
                        'processing', 'extracting' => 'heroicon-o-cpu-chip',
                        'review' => 'heroicon-o-eye',
                        'approved' => 'heroicon-o-check-circle',
                        'rejected', 'failed' => 'heroicon-o-x-circle',
                        default => 'heroicon-o-question-mark-circle',
                    }),
                    
                Tables\Columns\TextColumn::make('progress')
                    ->label('Progress')
                    ->getStateUsing(fn (CatalogueIngestion $record): string => 
                        $record->getProgressPercentage() . '%'
                    )
                    ->badge()
                    ->color(fn (CatalogueIngestion $record): string => 
                        $record->getProgressPercentage() >= 100 ? 'success' : 'info'
                    ),
                    
                Tables\Columns\TextColumn::make('products_found')
                    ->label('Products')
                    ->alignCenter()
                    ->badge()
                    ->color('info'),
                    
                Tables\Columns\TextColumn::make('products_approved')
                    ->label('Approved')
                    ->alignCenter()
                    ->badge()
                    ->color('success'),
                    
                Tables\Columns\TextColumn::make('products_rejected')
                    ->label('Rejected')
                    ->alignCenter()
                    ->badge()
                    ->color('danger'),
                    
                Tables\Columns\TextColumn::make('confidence_score')
                    ->label('Confidence')
                    ->suffix('%')
                    ->alignCenter()
                    ->color(fn (?float $state): string => match (true) {
                        $state >= 90 => 'success',
                        $state >= 70 => 'info',
                        $state >= 50 => 'warning',
                        default => 'danger',
                    }),
                    
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                    
                Tables\Columns\TextColumn::make('started_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                    
                Tables\Columns\TextColumn::make('completed_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'processing' => 'Processing',
                        'extracting' => 'Extracting',
                        'review' => 'Ready for Review',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                        'failed' => 'Failed',
                    ])
                    ->multiple(),
                    
                Tables\Filters\Filter::make('ready_for_review')
                    ->query(fn (Builder $query): Builder => $query->where('status', 'review'))
                    ->label('Ready for Review'),
                    
                Tables\Filters\Filter::make('high_confidence')
                    ->query(fn (Builder $query): Builder => $query->where('confidence_score', '>=', 90))
                    ->label('High Confidence (≥90%)'),
            ])
            ->actions([
                Tables\Actions\Action::make('review')
                    ->label('Review Products')
                    ->icon('heroicon-o-eye')
                    ->url(fn (CatalogueIngestion $record): string => 
                        route('filament.admin.resources.extracted-products.index', [
                            'tableFilters' => ['ingestion_id' => ['value' => $record->id]]
                        ])
                    )
                    ->visible(fn (CatalogueIngestion $record): bool => 
                        $record->status === 'review' && $record->products_found > 0
                    )
                    ->color('primary'),
                    
                Tables\Actions\Action::make('start_processing')
                    ->label('Start Processing')
                    ->icon('heroicon-o-play')
                    ->action(function (CatalogueIngestion $record) {
                        $service = app(CatalogueIngestionService::class);
                        $service->processCatalogue($record);
                        
                        Notification::make()
                            ->title('Processing started')
                            ->body('The AI is now extracting products from the catalogue.')
                            ->success()
                            ->send();
                    })
                    ->requiresConfirmation()
                    ->visible(fn (CatalogueIngestion $record): bool => 
                        $record->status === 'pending'
                    )
                    ->color('success'),
                    
                Tables\Actions\Action::make('approve_all')
                    ->label('Approve & Import All')
                    ->icon('heroicon-o-check-circle')
                    ->action(function (CatalogueIngestion $record) {
                        // Approve all pending products
                        $record->extractedProducts()
                            ->where('review_status', 'pending')
                            ->update([
                                'review_status' => 'approved',
                                'reviewed_by' => auth()->id(),
                                'reviewed_at' => now(),
                            ]);
                        
                        // Queue import job
                        ImportApprovedProducts::dispatch($record);
                        
                        Notification::make()
                            ->title('Import started')
                            ->body('All products have been approved and import is in progress.')
                            ->success()
                            ->send();
                    })
                    ->requiresConfirmation()
                    ->visible(fn (CatalogueIngestion $record): bool => 
                        $record->status === 'review' && $record->pendingProducts()->count() > 0
                    )
                    ->color('success'),
                    
                Tables\Actions\Action::make('import_approved')
                    ->label('Import Approved')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->action(function (CatalogueIngestion $record) {
                        ImportApprovedProducts::dispatch($record);
                        
                        Notification::make()
                            ->title('Import started')
                            ->body('Approved products are being imported to the catalog.')
                            ->success()
                            ->send();
                    })
                    ->requiresConfirmation()
                    ->visible(fn (CatalogueIngestion $record): bool => 
                        $record->status === 'review' && $record->approvedProducts()->count() > 0
                    )
                    ->color('primary'),
                    
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    
                    Tables\Actions\BulkAction::make('start_processing')
                        ->label('Start Processing')
                        ->icon('heroicon-o-play')
                        ->action(function ($records) {
                            $service = app(CatalogueIngestionService::class);
                            foreach ($records as $record) {
                                if ($record->status === 'pending') {
                                    $service->processCatalogue($record);
                                }
                            }
                        })
                        ->requiresConfirmation()
                        ->color('success'),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCatalogueIngestions::route('/'),
            'create' => Pages\CreateCatalogueIngestion::route('/create'),
            'edit' => Pages\EditCatalogueIngestion::route('/{record}/edit'),
        ];
    }
    
    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('status', 'review')->count() ?: null;
    }
    
    public static function getNavigationBadgeColor(): ?string
    {
        return 'warning';
    }
}