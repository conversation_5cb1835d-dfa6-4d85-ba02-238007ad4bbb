<?php

namespace App\Filament\Resources\ExtractedProductResource\Pages;

use App\Filament\Resources\ExtractedProductResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListExtractedProducts extends ListRecords
{
    protected static string $resource = ExtractedProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}