<?php

namespace App\Filament\Resources\CatalogueResource\Pages;

use App\Filament\Resources\CatalogueResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Storage;

class EditCatalogue extends EditRecord
{
    protected static string $resource = CatalogueResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
    
    protected function mutateFormDataBeforeSave(array $data): array
    {
        $record = $this->getRecord();
        
        // Preserve existing images if no new images were uploaded
        if ($data['type'] === 'images') {
            if (empty($data['images']) && $record && $record->images) {
                // No new images uploaded, preserve existing ones
                $data['images'] = $record->getRawOriginal('images');
                $data['page_count'] = is_array($record->images) ? count($record->images) : $record->page_count;
            } elseif (!empty($data['images'])) {
                // New images uploaded, calculate page count
                $data['page_count'] = is_array($data['images']) ? count($data['images']) : 1;
                // Ensure images are stored as JSON if they're an array
                if (is_array($data['images'])) {
                    $data['images'] = json_encode($data['images']);
                }
            }
        } elseif ($data['type'] === 'pdf') {
            // Preserve existing PDF if no new PDF was uploaded
            if (empty($data['pdf_url']) && $record && $record->pdf_url) {
                $data['pdf_url'] = $record->getRawOriginal('pdf_url');
                $data['page_count'] = $record->page_count;
                $data['file_size'] = $record->file_size;
            } elseif (!empty($data['pdf_url'])) {
                // New PDF uploaded
                $data['page_count'] = $data['page_count'] ?? 1;
                
                // Calculate file size if PDF exists
                if (Storage::disk('public')->exists($data['pdf_url'])) {
                    $data['file_size'] = Storage::disk('public')->size($data['pdf_url']);
                }
            }
        }
        
        // Preserve existing cover image if no new cover was uploaded
        if (empty($data['cover_url']) && $record && $record->cover_url) {
            $data['cover_url'] = $record->getRawOriginal('cover_url');
        }
        
        return $data;
    }
}
