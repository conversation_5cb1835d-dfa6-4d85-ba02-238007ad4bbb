<?php

namespace App\Filament\Resources\CatalogueResource\Pages;

use App\Filament\Resources\CatalogueResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Storage;

class CreateCatalogue extends CreateRecord
{
    protected static string $resource = CatalogueResource::class;
    
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Calculate page count based on type
        if ($data['type'] === 'images' && !empty($data['images'])) {
            $data['page_count'] = is_array($data['images']) ? count($data['images']) : 1;
        } elseif ($data['type'] === 'pdf' && !empty($data['pdf_url'])) {
            // For PDF, we'll set a default or you could use a PDF parser library
            $data['page_count'] = $data['page_count'] ?? 1;
            
            // Calculate file size if PDF exists
            if (Storage::disk('public')->exists($data['pdf_url'])) {
                $data['file_size'] = Storage::disk('public')->size($data['pdf_url']);
            }
        }
        
        // Ensure images are stored as JSON if they're an array
        if (isset($data['images']) && is_array($data['images'])) {
            $data['images'] = json_encode($data['images']);
        }
        
        return $data;
    }
}
