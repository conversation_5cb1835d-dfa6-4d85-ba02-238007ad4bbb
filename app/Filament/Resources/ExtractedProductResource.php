<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ExtractedProductResource\Pages;
use App\Models\ExtractedProduct;
use App\Services\CatalogueIngestionService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ExtractedProductResource extends Resource
{
    protected static ?string $model = ExtractedProduct::class;

    protected static ?string $navigationIcon = 'heroicon-o-eye';
    
    protected static ?string $navigationGroup = 'System';
    
    protected static ?string $navigationLabel = 'Product Review (Legacy)';
    
    protected static ?int $navigationSort = 99;
    
    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Product Information')
                    ->schema([
                        Forms\Components\Group::make([
                            Forms\Components\TextInput::make('extracted_name')
                                ->label('AI Extracted Name')
                                ->disabled(),
                                
                            Forms\Components\TextInput::make('final_name')
                                ->label('Final Name (Edit if needed)')
                                ->placeholder('Leave empty to use extracted name'),
                        ])->columns(2),
                        
                        Forms\Components\Group::make([
                            Forms\Components\Textarea::make('extracted_description')
                                ->label('AI Extracted Description')
                                ->disabled()
                                ->rows(3),
                                
                            Forms\Components\Textarea::make('final_description')
                                ->label('Final Description (Edit if needed)')
                                ->rows(3),
                        ])->columns(2),
                        
                        Forms\Components\Group::make([
                            Forms\Components\TextInput::make('extracted_sku')
                                ->label('AI Extracted SKU')
                                ->disabled(),
                                
                            Forms\Components\TextInput::make('final_sku')
                                ->label('Final SKU'),
                        ])->columns(2),
                        
                        Forms\Components\Group::make([
                            Forms\Components\TextInput::make('extracted_brand')
                                ->label('AI Extracted Brand')
                                ->disabled(),
                                
                            Forms\Components\TextInput::make('final_brand')
                                ->label('Final Brand'),
                        ])->columns(2),
                    ]),
                    
                Forms\Components\Section::make('Pricing & Classification')
                    ->schema([
                        Forms\Components\Group::make([
                            Forms\Components\TextInput::make('extracted_price')
                                ->label('AI Extracted Price')
                                ->numeric()
                                ->disabled(),
                                
                            Forms\Components\TextInput::make('final_price')
                                ->label('Final Price')
                                ->numeric(),
                        ])->columns(2),
                        
                        Forms\Components\Group::make([
                            Forms\Components\TextInput::make('extracted_currency')
                                ->label('AI Extracted Currency')
                                ->disabled(),
                                
                            Forms\Components\TextInput::make('final_currency')
                                ->label('Final Currency')
                                ->placeholder('USD'),
                        ])->columns(2),
                        
                        Forms\Components\Group::make([
                            Forms\Components\TextInput::make('extracted_category')
                                ->label('AI Suggested Category')
                                ->disabled(),
                                
                            Forms\Components\Select::make('final_category_id')
                                ->label('Final Category')
                                ->relationship('finalCategory', 'name')
                                ->searchable()
                                ->preload(),
                        ])->columns(2),
                    ]),
                    
                Forms\Components\Section::make('Review & Status')
                    ->schema([
                        Forms\Components\Select::make('review_status')
                            ->options([
                                'pending' => 'Pending Review',
                                'approved' => 'Approved',
                                'rejected' => 'Rejected',
                                'needs_edit' => 'Needs Editing',
                                'duplicate' => 'Duplicate',
                            ])
                            ->required(),
                            
                        Forms\Components\Toggle::make('create_new')
                            ->label('Create New Product'),
                            
                        Forms\Components\Toggle::make('update_existing')
                            ->label('Update Existing Product'),
                            
                        Forms\Components\Select::make('matched_product_id')
                            ->label('Matched Existing Product')
                            ->relationship('matchedProduct', 'title')
                            ->searchable()
                            ->preload(),
                            
                        Forms\Components\Textarea::make('review_notes')
                            ->label('Review Notes')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                    
                Forms\Components\Section::make('AI Metadata')
                    ->schema([
                        Forms\Components\TextInput::make('page_number')
                            ->label('Found on Page')
                            ->numeric()
                            ->disabled(),
                            
                        Forms\Components\TextInput::make('confidence_score')
                            ->label('AI Confidence Score')
                            ->suffix('%')
                            ->disabled(),
                            
                        Forms\Components\Textarea::make('extracted_specifications')
                            ->label('AI Extracted Specifications')
                            ->disabled()
                            ->formatStateUsing(fn ($state) => 
                                is_array($state) ? json_encode($state, JSON_PRETTY_PRINT) : $state
                            ),
                            
                        Forms\Components\Textarea::make('extracted_features')
                            ->label('AI Extracted Features')
                            ->disabled()
                            ->formatStateUsing(fn ($state) => 
                                is_array($state) ? implode(', ', $state) : $state
                            ),
                    ])
                    ->columns(2)
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('catalogue.cover_url')
                    ->label('Catalog')
                    ->disk('public')
                    ->size(40),
                    
                Tables\Columns\TextColumn::make('extracted_name')
                    ->label('Product Name')
                    ->searchable()
                    ->limit(30)
                    ->tooltip(fn (ExtractedProduct $record): string => $record->extracted_name),
                    
                Tables\Columns\TextColumn::make('extracted_brand')
                    ->label('Brand')
                    ->searchable()
                    ->toggleable(),
                    
                Tables\Columns\TextColumn::make('extracted_sku')
                    ->label('SKU')
                    ->searchable()
                    ->toggleable(),
                    
                Tables\Columns\TextColumn::make('extracted_price')
                    ->label('Price')
                    ->money(fn (ExtractedProduct $record): string => $record->extracted_currency ?? 'USD')
                    ->sortable(),
                    
                Tables\Columns\TextColumn::make('extracted_category')
                    ->label('Category')
                    ->badge()
                    ->color('info')
                    ->toggleable(),
                    
                Tables\Columns\TextColumn::make('page_number')
                    ->label('Page')
                    ->alignCenter()
                    ->sortable(),
                    
                Tables\Columns\TextColumn::make('confidence_score')
                    ->label('Confidence')
                    ->suffix('%')
                    ->alignCenter()
                    ->color(fn (?float $state): string => match (true) {
                        $state >= 90 => 'success',
                        $state >= 70 => 'info', 
                        $state >= 50 => 'warning',
                        default => 'danger',
                    })
                    ->badge(),
                    
                Tables\Columns\TextColumn::make('review_status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'gray',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        'needs_edit' => 'warning',
                        'duplicate' => 'info',
                        default => 'gray',
                    })
                    ->icon(fn (string $state): string => match ($state) {
                        'pending' => 'heroicon-o-clock',
                        'approved' => 'heroicon-o-check-circle',
                        'rejected' => 'heroicon-o-x-circle',
                        'needs_edit' => 'heroicon-o-pencil-square',
                        'duplicate' => 'heroicon-o-document-duplicate',
                        default => 'heroicon-o-question-mark-circle',
                    }),
                    
                Tables\Columns\IconColumn::make('imported_product_id')
                    ->label('Imported')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('gray'),
                    
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('ingestion_id')
                    ->label('Ingestion')
                    ->relationship('ingestion', 'id')
                    ->getOptionLabelFromRecordUsing(fn ($record) => 
                        "#{$record->id} - {$record->catalogue->title}"
                    )
                    ->searchable()
                    ->preload(),
                    
                Tables\Filters\SelectFilter::make('review_status')
                    ->options([
                        'pending' => 'Pending',
                        'approved' => 'Approved', 
                        'rejected' => 'Rejected',
                        'needs_edit' => 'Needs Edit',
                        'duplicate' => 'Duplicate',
                    ])
                    ->multiple(),
                    
                Tables\Filters\SelectFilter::make('shop_id')
                    ->label('Shop')
                    ->relationship('shop', 'name')
                    ->searchable()
                    ->preload(),
                    
                Tables\Filters\Filter::make('high_confidence')
                    ->query(fn (Builder $query): Builder => $query->where('confidence_score', '>=', 90))
                    ->label('High Confidence (≥90%)'),
                    
                Tables\Filters\Filter::make('not_imported')
                    ->query(fn (Builder $query): Builder => $query->whereNull('imported_product_id'))
                    ->label('Not Yet Imported'),
            ])
            ->actions([
                Tables\Actions\Action::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check-circle')
                    ->action(function (ExtractedProduct $record) {
                        $record->approve(auth()->id());
                        
                        Notification::make()
                            ->title('Product approved successfully')
                            ->success()
                            ->send();
                    })
                    ->visible(fn (ExtractedProduct $record): bool => 
                        $record->review_status === 'pending'
                    )
                    ->color('success'),
                    
                Tables\Actions\Action::make('reject')
                    ->label('Reject')
                    ->icon('heroicon-o-x-circle')
                    ->form([
                        Forms\Components\Textarea::make('reason')
                            ->label('Rejection Reason')
                            ->required(),
                    ])
                    ->action(function (ExtractedProduct $record, array $data) {
                        $record->reject(auth()->id(), $data['reason']);
                        
                        Notification::make()
                            ->title('Product rejected')
                            ->success()
                            ->send();
                    })
                    ->visible(fn (ExtractedProduct $record): bool => 
                        $record->review_status === 'pending'
                    )
                    ->color('danger'),
                    
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('bulk_approve')
                        ->label('Approve Selected')
                        ->icon('heroicon-o-check-circle')
                        ->action(function ($records) {
                            $service = app(CatalogueIngestionService::class);
                            $count = $service->bulkApprove(
                                $records->pluck('id')->toArray(),
                                auth()->id()
                            );
                            
                            Notification::make()
                                ->title("Approved {$count} products")
                                ->success()
                                ->send();
                        })
                        ->requiresConfirmation()
                        ->color('success'),
                        
                    Tables\Actions\BulkAction::make('bulk_reject')
                        ->label('Reject Selected')
                        ->icon('heroicon-o-x-circle')
                        ->form([
                            Forms\Components\Textarea::make('reason')
                                ->label('Rejection Reason')
                                ->required(),
                        ])
                        ->action(function ($records, array $data) {
                            $service = app(CatalogueIngestionService::class);
                            $count = $service->bulkReject(
                                $records->pluck('id')->toArray(),
                                auth()->id(),
                                $data['reason']
                            );
                            
                            Notification::make()
                                ->title("Rejected {$count} products")
                                ->success()
                                ->send();
                        })
                        ->requiresConfirmation()
                        ->color('danger'),
                        
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListExtractedProducts::route('/'),
            'create' => Pages\CreateExtractedProduct::route('/create'),
            'edit' => Pages\EditExtractedProduct::route('/{record}/edit'),
        ];
    }
    
    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('review_status', 'pending')->count() ?: null;
    }
    
    public static function getNavigationBadgeColor(): ?string
    {
        return 'warning';
    }
}