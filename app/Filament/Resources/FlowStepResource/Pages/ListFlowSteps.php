<?php

namespace App\Filament\Resources\FlowStepResource\Pages;

use App\Filament\Resources\FlowStepResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFlowSteps extends ListRecords
{
    protected static string $resource = FlowStepResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
