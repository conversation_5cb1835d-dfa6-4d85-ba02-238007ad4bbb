<?php

namespace App\Filament\Resources\ConversationAnalyticsResource\Pages;

use App\Filament\Resources\ConversationAnalyticsResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditConversationAnalytics extends EditRecord
{
    protected static string $resource = ConversationAnalyticsResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
