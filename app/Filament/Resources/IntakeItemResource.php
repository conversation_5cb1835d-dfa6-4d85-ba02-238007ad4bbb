<?php

namespace App\Filament\Resources;

use App\Filament\Resources\IntakeItemResource\Pages;
use App\Models\IntakeItem;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class IntakeItemResource extends Resource
{
    protected static ?string $model = IntakeItem::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';

    protected static ?string $navigationGroup = 'Bot Management';

    protected static ?string $navigationLabel = 'Image Recognition';

    protected static ?string $pluralModelLabel = 'Image Recognition Results';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('customer_id')
                    ->relationship('customer', 'first_name')
                    ->required(),
                Forms\Components\Select::make('type')
                    ->options([
                        'text' => 'Text',
                        'link' => 'Link',
                        'image' => 'Image',
                    ])
                    ->required(),
                Forms\Components\Textarea::make('raw_text')
                    ->maxLength(65535)
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('raw_url')
                    ->maxLength(255),
                Forms\Components\TextInput::make('media_id')
                    ->maxLength(255),
                Forms\Components\TextInput::make('extracted_name')
                    ->maxLength(255),
                Forms\Components\TextInput::make('extracted_outlet')
                    ->maxLength(255),
                Forms\Components\TextInput::make('extracted_price')
                    ->numeric(),
                Forms\Components\TextInput::make('currency')
                    ->maxLength(3)
                    ->default('ZAR'),
                Forms\Components\TextInput::make('confidence')
                    ->numeric()
                    ->minValue(0)
                    ->maxValue(100),
                Forms\Components\Select::make('status')
                    ->options([
                        'parsed' => 'Parsed',
                        'needs_price' => 'Needs Price',
                        'needs_outlet' => 'Needs Outlet',
                        'ready' => 'Ready',
                    ])
                    ->default('parsed'),
                Forms\Components\Textarea::make('ai_analysis')
                    ->label('AI Analysis (JSON)')
                    ->columnSpanFull()
                    ->formatStateUsing(fn ($state) =>
                        is_string($state) ? $state : json_encode($state, JSON_PRETTY_PRINT)
                    ),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('customer.first_name')
                    ->label('Customer')
                    ->searchable()
                    ->formatStateUsing(fn ($record) =>
                        ($record->customer->first_name ?? 'Unknown') .
                        ' (' . ($record->customer->phone_e164 ?? 'No phone') . ')'
                    ),
                Tables\Columns\BadgeColumn::make('type')
                    ->colors([
                        'success' => 'image',
                        'warning' => 'link',
                        'primary' => 'text',
                    ]),
                Tables\Columns\TextColumn::make('extracted_name')
                    ->label('Product')
                    ->searchable()
                    ->limit(30)
                    ->tooltip(fn ($record) => $record->extracted_name),
                Tables\Columns\TextColumn::make('extracted_outlet')
                    ->label('Outlet')
                    ->searchable(),
                Tables\Columns\TextColumn::make('extracted_price')
                    ->label('Price')
                    ->money('ZAR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('confidence')
                    ->suffix('%')
                    ->sortable()
                    ->color(fn ($state) =>
                        $state >= 80 ? 'success' :
                        ($state >= 60 ? 'warning' : 'danger')
                    ),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'success' => 'ready',
                        'warning' => ['needs_price', 'needs_outlet'],
                        'primary' => 'parsed',
                    ]),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('M j, H:i')
                    ->sortable()
                    ->description(fn ($record) => $record->created_at->diffForHumans()),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'text' => 'Text',
                        'link' => 'Link',
                        'image' => 'Image',
                    ]),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'parsed' => 'Parsed',
                        'needs_price' => 'Needs Price',
                        'needs_outlet' => 'Needs Outlet',
                        'ready' => 'Ready',
                    ]),
                Tables\Filters\Filter::make('high_confidence')
                    ->label('High Confidence (80%+)')
                    ->query(fn (Builder $query): Builder => $query->where('confidence', '>=', 80)),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListIntakeItems::route('/'),
            'create' => Pages\CreateIntakeItem::route('/create'),
            'edit' => Pages\EditIntakeItem::route('/{record}/edit'),
        ];
    }
}
