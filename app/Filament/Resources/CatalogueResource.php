<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CatalogueResource\Pages;
use App\Models\Catalogue;
use App\Models\CatalogueIngestion;
use App\Jobs\ProcessCatalogueIngestion;
use App\Services\CatalogueIngestionService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Closure;

class CatalogueResource extends Resource
{
    protected static ?string $model = Catalogue::class;

    protected static ?string $navigationIcon = 'heroicon-o-book-open';
    
    protected static ?string $navigationGroup = 'Content Management';
    
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\Select::make('shop_id')
                            ->relationship('shop', 'name')
                            ->required()
                            ->searchable()
                            ->preload(),
                            
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(fn (Set $set, ?string $state) => 
                                $set('slug', Str::slug($state))
                            ),
                            
                        Forms\Components\TextInput::make('slug')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->disabled()
                            ->dehydrated(),
                            
                        Forms\Components\Textarea::make('description')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                    
                Forms\Components\Section::make('Catalogue Type & Content')
                    ->schema([
                        Forms\Components\Radio::make('type')
                            ->label('Catalogue Type')
                            ->options([
                                'pdf' => 'PDF Document',
                                'images' => 'Image Gallery',
                            ])
                            ->required()
                            ->live()
                            ->afterStateUpdated(fn (Set $set) => [
                                $set('pdf_url', null),
                                $set('images', null),
                            ]),
                            
                        Forms\Components\FileUpload::make('pdf_url')
                            ->label('PDF File')
                            ->acceptedFileTypes(['application/pdf'])
                            ->maxSize(51200) // 50MB
                            ->disk('public')
                            ->directory('catalogues/pdfs')
                            ->visibility('public')
                            ->downloadable()
                            ->openable()
                            ->required(fn (Get $get, $record): bool => 
                                $get('type') === 'pdf' && (!$record || !$record->pdf_url)
                            )
                            ->visible(fn (Get $get): bool => $get('type') === 'pdf')
                            ->helperText(function ($record): string {
                                return $record && $record->pdf_url && $record->type === 'pdf'
                                    ? 'Current PDF: ' . basename($record->pdf_url) . ' (Upload a new file to replace, or leave empty to keep current file)'
                                    : 'Upload PDF catalogue (max 50MB)';
                            }),
                            
                        Forms\Components\FileUpload::make('images')
                            ->label('Catalogue Pages')
                            ->multiple()
                            ->image()
                            ->reorderable()
                            ->maxSize(10240) // 10MB per image
                            ->maxFiles(50)
                            ->disk('public')
                            ->directory('catalogues/images')
                            ->visibility('public')
                            ->required(fn (Get $get, $record): bool => 
                                $get('type') === 'images' && (!$record || !$record->images || count($record->images) === 0)
                            )
                            ->visible(fn (Get $get): bool => $get('type') === 'images')
                            ->helperText(function ($record): string {
                                if ($record && $record->images && $record->type === 'images' && count($record->images) > 0) {
                                    $count = count($record->images);
                                    return "Current catalogue has {$count} page(s). You can add more images, remove existing ones, or reorder them. Existing images are preserved when editing other fields.";
                                }
                                return 'Upload catalogue pages as images (max 10MB per image, up to 50 pages)';
                            }),
                            
                        Forms\Components\FileUpload::make('cover_url')
                            ->label('Cover Image')
                            ->image()
                            ->maxSize(5120) // 5MB
                            ->disk('public')
                            ->directory('catalogues/covers')
                            ->visibility('public')
                            ->helperText(function ($record): string {
                                if ($record && $record->cover_url) {
                                    return 'Current cover: ' . basename($record->cover_url) . ' (Upload a new image to replace, leave empty to keep current, or remove to delete)';
                                }
                                return 'Upload a cover image for the catalogue (optional for image galleries)';
                            }),
                    ])
                    ->columns(1),
                    
                Forms\Components\Section::make('Validity & Status')
                    ->schema([
                        Forms\Components\DatePicker::make('valid_from')
                            ->label('Valid From')
                            ->native(false),
                            
                        Forms\Components\DatePicker::make('valid_to')
                            ->label('Valid Until')
                            ->native(false)
                            ->after('valid_from'),
                            
                        Forms\Components\Select::make('status')
                            ->options([
                                'active' => 'Active',
                                'inactive' => 'Inactive',
                                'expired' => 'Expired',
                            ])
                            ->default('active')
                            ->required(),
                            
                        Forms\Components\TextInput::make('page_count')
                            ->numeric()
                            ->disabled()
                            ->dehydrated()
                            ->helperText('Automatically calculated'),
                    ])
                    ->columns(2),
                    
                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Catalogue $record): ?string => $record->created_at?->diffForHumans()),
                            
                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Catalogue $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columns(2)
                    ->collapsible()
                    ->collapsed()
                    ->visible(fn ($record) => $record !== null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('cover_url')
                    ->label('Cover')
                    ->square()
                    ->disk('public'),
                    
                Tables\Columns\TextColumn::make('shop.name')
                    ->label('Shop')
                    ->searchable()
                    ->sortable()
                    ->url(function ($record) {
                        return $record->shop?->website;
                    }, shouldOpenInNewTab: true)
                    ->icon('heroicon-o-arrow-top-right-on-square')
                    ->iconPosition('after')
                    ->tooltip(function ($record) {
                        return $record->shop?->website ? 'Visit ' . $record->shop->name . ' website' : null;
                    }),
                    
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->limit(30),
                    
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pdf' => 'primary',
                        'images' => 'success',
                        default => 'gray',
                    })
                    ->icon(fn (string $state): string => match ($state) {
                        'pdf' => 'heroicon-o-document',
                        'images' => 'heroicon-o-photo',
                        default => 'heroicon-o-document',
                    }),
                    
                Tables\Columns\TextColumn::make('page_count')
                    ->label('Pages')
                    ->numeric()
                    ->sortable(),
                    
                Tables\Columns\TextColumn::make('valid_from')
                    ->date()
                    ->sortable()
                    ->toggleable(),
                    
                Tables\Columns\TextColumn::make('valid_to')
                    ->date()
                    ->sortable()
                    ->toggleable(),
                    
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'warning',
                        'expired' => 'danger',
                        default => 'gray',
                    }),
                    
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('shop')
                    ->relationship('shop', 'name')
                    ->searchable()
                    ->preload(),
                    
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'pdf' => 'PDF',
                        'images' => 'Images',
                    ]),
                    
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'expired' => 'Expired',
                    ]),
                    
                Tables\Filters\Filter::make('valid')
                    ->query(fn (Builder $query): Builder => $query
                        ->where('status', 'active')
                        ->where(function ($query) {
                            $query->whereNull('valid_to')
                                  ->orWhere('valid_to', '>=', now());
                        })
                    )
                    ->label('Currently Valid'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('preview')
                    ->label('Preview')
                    ->icon('heroicon-o-eye')
                    ->url(fn (Catalogue $record): string => 
                        $record->type === 'pdf' 
                            ? $record->pdf_url
                            : ($record->cover_url ?: ($record->images[0] ?? '#'))
                    )
                    ->openUrlInNewTab(),
                Tables\Actions\Action::make('view_gallery')
                    ->label('View Gallery')
                    ->icon('heroicon-o-photo')
                    ->modalHeading(fn (Catalogue $record): string => $record->title)
                    ->modalContent(fn (Catalogue $record): \Illuminate\View\View => view('filament.catalogue-preview', ['catalogue' => $record]))
                    ->modalWidth('7xl')
                    ->visible(fn (Catalogue $record): bool => 
                        $record->type === 'images' && !empty($record->images)
                    ),
                Tables\Actions\Action::make('extract_products')
                    ->label('Extract Products')
                    ->icon('heroicon-o-cpu-chip')
                    ->form([
                        Forms\Components\Textarea::make('notes')
                            ->label('Processing Notes (Optional)')
                            ->placeholder('Add any specific instructions for the AI extraction process...'),
                        Forms\Components\Toggle::make('high_accuracy')
                            ->label('Use High Accuracy Mode')
                            ->helperText('More thorough but slower processing')
                            ->default(false),
                    ])
                    ->action(function (Catalogue $record, array $data) {
                        // Check if there's already a pending/processing ingestion
                        $existingIngestion = CatalogueIngestion::where('catalogue_id', $record->id)
                            ->whereIn('status', ['pending', 'processing', 'extracting'])
                            ->first();
                            
                        if ($existingIngestion) {
                            Notification::make()
                                ->title('Extraction already in progress')
                                ->body('This catalogue is already being processed.')
                                ->warning()
                                ->send();
                            return;
                        }
                        
                        // Create new ingestion
                        $ingestionService = app(CatalogueIngestionService::class);
                        $ingestion = $ingestionService->startIngestion($record, auth()->id());
                        
                        // Add processing notes if provided
                        if (!empty($data['notes'])) {
                            $ingestion->update(['processing_notes' => $data['notes']]);
                        }
                        
                        // Queue the processing job
                        ProcessCatalogueIngestion::dispatch($ingestion);
                        
                        Notification::make()
                            ->title('Product extraction started')
                            ->body('The AI will process this catalogue and extract products. You will be notified when ready for review.')
                            ->success()
                            ->send();
                    })
                    ->color('primary')
                    ->requiresConfirmation()
                    ->modalHeading('Extract Products with AI')
                    ->modalDescription('This will use AI to automatically identify and extract products from this catalogue. The process may take several minutes depending on the catalogue size.'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate Selected')
                        ->icon('heroicon-o-check-circle')
                        ->action(fn ($records) => $records->each->update(['status' => 'active']))
                        ->requiresConfirmation()
                        ->color('success'),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate Selected')
                        ->icon('heroicon-o-x-circle')
                        ->action(fn ($records) => $records->each->update(['status' => 'inactive']))
                        ->requiresConfirmation()
                        ->color('warning'),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCatalogues::route('/'),
            'create' => Pages\CreateCatalogue::route('/create'),
            'edit' => Pages\EditCatalogue::route('/{record}/edit'),
        ];
    }
    
    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('status', 'active')->count();
    }
    
    public static function getNavigationBadgeColor(): ?string
    {
        return 'success';
    }
}