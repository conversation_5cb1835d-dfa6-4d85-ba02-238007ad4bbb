<?php

namespace App\Filament\Resources\ConversationFlowResource\Pages;

use App\Filament\Resources\ConversationFlowResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListConversationFlows extends ListRecords
{
    protected static string $resource = ConversationFlowResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
