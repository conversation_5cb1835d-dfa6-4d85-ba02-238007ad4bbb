<?php

namespace App\Filament\Resources\ConversationIntentResource\Pages;

use App\Filament\Resources\ConversationIntentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListConversationIntents extends ListRecords
{
    protected static string $resource = ConversationIntentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
