<?php

namespace App\Filament\Resources;

use App\Filament\Resources\OrderResource\Pages;
use App\Filament\Resources\OrderResource\RelationManagers;
use App\Models\Order;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class OrderResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-bag';
    
    protected static ?string $navigationGroup = 'Order Management';
    
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('quote_id')
                    ->relationship('quote', 'id')
                    ->required(),
                Forms\Components\Select::make('customer_id')
                    ->relationship('customer', 'id')
                    ->required(),
                Forms\Components\TextInput::make('order_ref')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('reference')
                    ->label('YA Reference')
                    ->maxLength(255),
                Forms\Components\Select::make('status')
                    ->required()
                    ->options([
                        'NEW' => 'New',
                        'IN_PROGRESS' => 'In Progress',
                        'FULFILLED' => 'Fulfilled',
                        'CANCELLED' => 'Cancelled',
                        'draft' => 'Draft',
                        'quoted' => 'Quoted',
                        'pending_approval' => 'Pending Approval',
                        'approved' => 'Approved',
                        'procured' => 'Procured',
                        'in_transit' => 'In Transit',
                        'arrived_zim' => 'Arrived Zimbabwe',
                        'last_mile' => 'Last Mile',
                        'delivered' => 'Delivered',
                        'closed' => 'Closed',
                    ])
                    ->default('NEW'),
                Forms\Components\TextInput::make('total_usd')
                    ->required()
                    ->numeric()
                    ->prefix('$'),
                Forms\Components\TextInput::make('city')
                    ->maxLength(255)
                    ->default('Harare'),
                Forms\Components\Textarea::make('breakdown_json')
                    ->label('Pricing Breakdown')
                    ->columnSpanFull()
                    ->formatStateUsing(fn ($state) => json_encode($state, JSON_PRETTY_PRINT)),
                Forms\Components\Textarea::make('cart_snapshot')
                    ->label('Cart Snapshot')
                    ->columnSpanFull()
                    ->formatStateUsing(fn ($state) => json_encode($state, JSON_PRETTY_PRINT)),
                Forms\Components\TextInput::make('shipping_pref')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\Textarea::make('notes')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('reference')
                    ->label('YA Reference')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->weight('bold'),
                Tables\Columns\TextColumn::make('customer.first_name')
                    ->label('Customer')
                    ->searchable()
                    ->formatStateUsing(fn ($record) =>
                        ($record->customer->first_name ?? 'Unknown') . ' ' .
                        ($record->customer->last_name ?? '') .
                        ' (' . ($record->customer->phone_e164 ?? $record->customer->phone ?? 'No phone') . ')'
                    ),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'danger' => ['CANCELLED'],
                        'warning' => ['NEW', 'draft', 'pending_approval'],
                        'primary' => ['IN_PROGRESS', 'approved', 'procured', 'in_transit'],
                        'success' => ['FULFILLED', 'delivered', 'closed'],
                    ]),
                Tables\Columns\TextColumn::make('total_usd')
                    ->label('Total')
                    ->money('USD', true)
                    ->sortable()
                    ->weight('bold'),
                Tables\Columns\TextColumn::make('city')
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('order_ref')
                    ->label('Internal Ref')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y H:i')
                    ->sortable()
                    ->description(fn ($record) => $record->created_at->diffForHumans()),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'NEW' => 'New',
                        'IN_PROGRESS' => 'In Progress',
                        'FULFILLED' => 'Fulfilled',
                        'CANCELLED' => 'Cancelled',
                    ])
                    ->multiple(),
                Tables\Filters\SelectFilter::make('city')
                    ->relationship('customer', 'id')
                    ->multiple(),
                Tables\Filters\Filter::make('created_from')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Created from'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrders::route('/'),
            'create' => Pages\CreateOrder::route('/create'),
            'edit' => Pages\EditOrder::route('/{record}/edit'),
        ];
    }
}
