<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FlowStepResource\Pages;
use App\Filament\Resources\FlowStepResource\RelationManagers;
use App\Models\FlowStep;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FlowStepResource extends Resource
{
    protected static ?string $model = FlowStep::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFlowSteps::route('/'),
            'create' => Pages\CreateFlowStep::route('/create'),
            'edit' => Pages\EditFlowStep::route('/{record}/edit'),
        ];
    }
}
