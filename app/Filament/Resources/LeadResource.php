<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LeadResource\Pages;
use App\Filament\Resources\LeadResource\RelationManagers;
use App\Models\Lead;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class LeadResource extends Resource
{
    protected static ?string $model = Lead::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-plus';
    
    protected static ?string $navigationGroup = 'CRM';
    
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Lead Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('phone')
                            ->tel()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->maxLength(255),
                        Forms\Components\Select::make('country')
                            ->options([
                                'ZW' => 'Zimbabwe',
                                'ZA' => 'South Africa',
                                'KE' => 'Kenya',
                                'UG' => 'Uganda',
                            ])
                            ->default('ZW'),
                    ])->columns(2),

                Forms\Components\Section::make('Lead Status')
                    ->schema([
                        Forms\Components\Select::make('source')
                            ->options([
                                'whatsapp' => 'WhatsApp',
                                'web' => 'Website',
                                'referral' => 'Referral',
                                'social' => 'Social Media',
                            ])
                            ->required(),
                        Forms\Components\Select::make('status')
                            ->options([
                                'new' => 'New',
                                'contacted' => 'Contacted',
                                'qualified' => 'Qualified',
                                'converted' => 'Converted',
                                'lost' => 'Lost',
                            ])
                            ->required(),
                        Forms\Components\Select::make('quality')
                            ->options([
                                'hot' => 'Hot',
                                'warm' => 'Warm',
                                'cold' => 'Cold',
                                'unqualified' => 'Unqualified',
                            ])
                            ->required(),
                        Forms\Components\TextInput::make('engagement_score')
                            ->numeric()
                            ->default(0),
                    ])->columns(2),

                Forms\Components\Section::make('Assignment')
                    ->schema([
                        Forms\Components\Select::make('assigned_to')
                            ->relationship('assignedTo', 'name')
                            ->searchable()
                            ->preload(),
                        Forms\Components\Select::make('customer_id')
                            ->relationship('customer', 'email')
                            ->searchable()
                            ->preload(),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->placeholder('No name'),
                Tables\Columns\TextColumn::make('phone')
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->placeholder('No email'),
                Tables\Columns\BadgeColumn::make('source')
                    ->colors([
                        'success' => 'whatsapp',
                        'primary' => 'web',
                        'warning' => 'referral',
                        'info' => 'social',
                    ]),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'secondary' => 'new',
                        'warning' => 'contacted',
                        'info' => 'qualified',
                        'success' => 'converted',
                        'danger' => 'lost',
                    ]),
                Tables\Columns\BadgeColumn::make('quality')
                    ->colors([
                        'danger' => 'hot',
                        'warning' => 'warm',
                        'secondary' => 'cold',
                        'gray' => 'unqualified',
                    ]),
                Tables\Columns\TextColumn::make('engagement_score')
                    ->sortable(),
                Tables\Columns\TextColumn::make('assignedTo.name')
                    ->label('Assigned To')
                    ->placeholder('Unassigned'),
                Tables\Columns\TextColumn::make('inquiries_count')
                    ->counts('inquiries')
                    ->label('Inquiries'),
                Tables\Columns\TextColumn::make('last_interaction_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                Tables\Filters\SelectFilter::make('source')
                    ->options([
                        'whatsapp' => 'WhatsApp',
                        'web' => 'Website',
                        'referral' => 'Referral',
                        'social' => 'Social Media',
                    ]),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'new' => 'New',
                        'contacted' => 'Contacted',
                        'qualified' => 'Qualified',
                        'converted' => 'Converted',
                        'lost' => 'Lost',
                    ]),
                Tables\Filters\SelectFilter::make('quality')
                    ->options([
                        'hot' => 'Hot',
                        'warm' => 'Warm',
                        'cold' => 'Cold',
                        'unqualified' => 'Unqualified',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('convert')
                    ->icon('heroicon-o-arrow-right-circle')
                    ->color('success')
                    ->action(function (Lead $record) {
                        $record->convertToCustomer();
                    })
                    ->visible(fn (Lead $record) => !$record->customer_id),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // RelationManagers\InquiriesRelationManager::class,
            // RelationManagers\ActivitiesRelationManager::class,
            // RelationManagers\FollowUpsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLeads::route('/'),
            'create' => Pages\CreateLead::route('/create'),
            'view' => Pages\ViewLead::route('/{record}'),
            'edit' => Pages\EditLead::route('/{record}/edit'),
        ];
    }
}