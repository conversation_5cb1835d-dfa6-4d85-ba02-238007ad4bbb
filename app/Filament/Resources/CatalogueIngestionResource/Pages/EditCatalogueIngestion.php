<?php

namespace App\Filament\Resources\CatalogueIngestionResource\Pages;

use App\Filament\Resources\CatalogueIngestionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCatalogueIngestion extends EditRecord
{
    protected static string $resource = CatalogueIngestionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}