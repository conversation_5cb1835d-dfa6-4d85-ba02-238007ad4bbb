<?php

namespace App\Filament\Resources\CatalogueIngestionResource\Pages;

use App\Filament\Resources\CatalogueIngestionResource;
use Filament\Resources\Pages\CreateRecord;

class CreateCatalogueIngestion extends CreateRecord
{
    protected static string $resource = CatalogueIngestionResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['user_id'] = auth()->id();
        
        return $data;
    }
}