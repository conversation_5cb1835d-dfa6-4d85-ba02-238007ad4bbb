<?php

namespace App\Filament\Resources\CatalogueIngestionResource\Pages;

use App\Filament\Resources\CatalogueIngestionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCatalogueIngestions extends ListRecords
{
    protected static string $resource = CatalogueIngestionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}