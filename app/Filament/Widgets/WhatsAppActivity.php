<?php

namespace App\Filament\Widgets;

use App\Models\Conversation;
use App\Models\Message;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class WhatsAppActivity extends BaseWidget
{
    protected static ?string $heading = 'Recent WhatsApp Conversations';
    
    protected static ?int $sort = 4;
    
    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Conversation::with(['customer'])
                    ->where('channel', 'whatsapp')
                    ->latest('last_message_at')
                    ->limit(10)
            )
            ->columns([
                Tables\Columns\TextColumn::make('wa_number')
                    ->label('Phone Number')
                    ->searchable(),
                    
                Tables\Columns\TextColumn::make('customer.full_name')
                    ->label('Customer')
                    ->default('Guest')
                    ->searchable(['first_name', 'last_name']),
                    
                Tables\Columns\TextColumn::make('state')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'agent_requested' => 'warning',
                        'assigned' => 'info',
                        'closed' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => ucwords(str_replace('_', ' ', $state))),
                    
                Tables\Columns\TextColumn::make('messages_count')
                    ->label('Messages')
                    ->counts('messages')
                    ->sortable(),
                    
                Tables\Columns\TextColumn::make('last_message_at')
                    ->label('Last Activity')
                    ->dateTime()
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\Action::make('view_messages')
                    ->label('View Chat')
                    ->icon('heroicon-o-chat-bubble-left-right')
                    ->url(fn (Conversation $record): string => route('filament.admin.resources.conversations.view', $record))
                    ->openUrlInNewTab(),
            ])
            ->defaultSort('last_message_at', 'desc');
    }
}