<?php

namespace App\Filament\Widgets;

use App\Models\Conversation;
use App\Models\ConversationAnalytics;
use App\Models\ConversationIntent;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class ConversationIntentStats extends BaseWidget
{
    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        $today = now()->startOfDay();
        $yesterday = now()->subDay()->startOfDay();
        
        // Get today's conversations
        $todayConversations = Conversation::where('created_at', '>=', $today)->count();
        $yesterdayConversations = Conversation::whereBetween('created_at', [$yesterday, $today])->count();
        
        // Get successful conversations
        $successfulToday = Conversation::where('created_at', '>=', $today)
            ->where('completion_status', 'successful')
            ->count();
        
        // Get average satisfaction score
        $avgSatisfaction = ConversationAnalytics::where('created_at', '>=', $today)
            ->avg('satisfaction_score') ?? 0;
        
        // Get most common intent today
        $topIntent = Conversation::where('created_at', '>=', $today)
            ->whereNotNull('conversation_intent_id')
            ->with('intent')
            ->select('conversation_intent_id', DB::raw('count(*) as count'))
            ->groupBy('conversation_intent_id')
            ->orderByDesc('count')
            ->first();
        
        return [
            Stat::make('Conversations Today', $todayConversations)
                ->description($this->getChangeDescription($todayConversations, $yesterdayConversations))
                ->descriptionIcon($this->getChangeIcon($todayConversations, $yesterdayConversations))
                ->color($this->getChangeColor($todayConversations, $yesterdayConversations)),
            
            Stat::make('Success Rate', $todayConversations > 0 ? round(($successfulToday / $todayConversations) * 100, 1) . '%' : '0%')
                ->description('Completed successfully')
                ->color($successfulToday > ($todayConversations * 0.7) ? 'success' : 'warning'),
            
            Stat::make('Avg Satisfaction', number_format($avgSatisfaction, 1) . '/5')
                ->description('Customer satisfaction')
                ->color($avgSatisfaction >= 4 ? 'success' : ($avgSatisfaction >= 3 ? 'warning' : 'danger')),
            
            Stat::make('Top Intent', $topIntent?->intent?->name ?? 'N/A')
                ->description($topIntent ? $topIntent->count . ' conversations' : 'No data')
                ->color('primary'),
        ];
    }
    
    private function getChangeDescription(int $today, int $yesterday): string
    {
        if ($yesterday == 0) {
            return 'No data from yesterday';
        }
        
        $change = (($today - $yesterday) / $yesterday) * 100;
        return abs(round($change, 1)) . '% from yesterday';
    }
    
    private function getChangeIcon(int $today, int $yesterday): string
    {
        if ($today > $yesterday) {
            return 'heroicon-m-arrow-trending-up';
        } elseif ($today < $yesterday) {
            return 'heroicon-m-arrow-trending-down';
        }
        return 'heroicon-m-minus';
    }
    
    private function getChangeColor(int $today, int $yesterday): string
    {
        if ($today > $yesterday) {
            return 'success';
        } elseif ($today < $yesterday) {
            return 'warning';
        }
        return 'gray';
    }
}