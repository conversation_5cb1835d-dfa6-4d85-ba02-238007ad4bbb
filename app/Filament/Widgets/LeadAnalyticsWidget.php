<?php

namespace App\Filament\Widgets;

use App\Models\Lead;
use App\Models\Inquiry;
use App\Models\Order;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class LeadAnalyticsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $totalLeads = Lead::count();
        $newLeadsToday = Lead::whereDate('created_at', today())->count();
        $newLeadsWeek = Lead::whereBetween('created_at', [now()->startOfWeek(), now()])->count();
        
        $hotLeads = Lead::where('quality', 'hot')->count();
        $conversionRate = $totalLeads > 0 ? round((Lead::where('status', 'converted')->count() / $totalLeads) * 100, 1) : 0;
        
        $pendingInquiries = Inquiry::where('status', 'open')->count();
        $overdueInquiries = Inquiry::where('response_due_at', '<', now())
            ->whereNotIn('status', ['resolved', 'closed'])
            ->count();
            
        $totalOrders = Order::count();
        $avgOrderValue = Order::avg('total_usd') ?: 0;

        return [
            Stat::make('Total Leads', $totalLeads)
                ->description($newLeadsToday . ' new today')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color('primary'),
                
            Stat::make('Hot Leads', $hotLeads)
                ->description($newLeadsWeek . ' new this week')
                ->descriptionIcon('heroicon-m-fire')
                ->color('warning'),
                
            Stat::make('Conversion Rate', $conversionRate . '%')
                ->description('Lead to customer conversion')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('success'),
                
            Stat::make('Open Inquiries', $pendingInquiries)
                ->description($overdueInquiries . ' overdue')
                ->descriptionIcon($overdueInquiries > 0 ? 'heroicon-m-exclamation-triangle' : 'heroicon-m-chat-bubble-left')
                ->color($overdueInquiries > 0 ? 'danger' : 'info'),
                
            Stat::make('Total Orders', $totalOrders)
                ->description('$' . number_format($avgOrderValue, 2) . ' avg value')
                ->descriptionIcon('heroicon-m-shopping-cart')
                ->color('success'),
        ];
    }
}