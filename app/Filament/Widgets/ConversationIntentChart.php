<?php

namespace App\Filament\Widgets;

use App\Models\Conversation;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Carbon;

class ConversationIntent<PERSON>hart extends ChartWidget
{
    protected static ?string $heading = 'Conversation Intents Distribution';

    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $startDate = now()->subDays(30);
        
        $intents = Conversation::where('created_at', '>=', $startDate)
            ->whereNotNull('conversation_intent_id')
            ->with('intent')
            ->get()
            ->groupBy('intent.category')
            ->map->count();
        
        return [
            'datasets' => [
                [
                    'label' => 'Conversations by Intent Category',
                    'data' => $intents->values()->toArray(),
                    'backgroundColor' => [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF',
                        '#FF9F40',
                        '#FF6384',
                        '#C9CBCF',
                        '#4BC0C0',
                        '#FF6384',
                    ],
                ],
            ],
            'labels' => $intents->keys()->map(fn($key) => str_replace('_', ' ', title_case($key)))->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }
}