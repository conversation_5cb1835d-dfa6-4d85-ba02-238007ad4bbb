<?php

namespace App\Filament\Widgets;

use App\Models\Order;
use App\Models\Quote;
use App\Models\Customer;
use App\Models\Product;
use App\Models\ExchangeRate;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseWidget
{
    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        // Get current exchange rate
        $currentRate = ExchangeRate::currentOfficial();
        
        // Calculate total revenue this month
        $monthlyRevenue = Order::where('created_at', '>=', now()->startOfMonth())
            ->sum('total_usd');
            
        // Calculate growth compared to last month
        $lastMonthRevenue = Order::whereBetween('created_at', [
            now()->subMonth()->startOfMonth(),
            now()->subMonth()->endOfMonth()
        ])->sum('total_usd');
        
        $revenueGrowth = $lastMonthRevenue > 0 
            ? (($monthlyRevenue - $lastMonthRevenue) / $lastMonthRevenue) * 100 
            : 0;

        return [
            Stat::make('Total Revenue (This Month)', '$' . number_format($monthlyRevenue, 2))
                ->description(($revenueGrowth >= 0 ? '+' : '') . number_format($revenueGrowth, 1) . '% from last month')
                ->descriptionIcon($revenueGrowth >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($revenueGrowth >= 0 ? 'success' : 'danger')
                ->chart([7, 2, 10, 3, 15, 4, 17]), // Sample chart data

            Stat::make('Total Orders', Order::count())
                ->description('All time')
                ->descriptionIcon('heroicon-m-shopping-bag')
                ->color('info'),

            Stat::make('Active Quotes', Quote::whereIn('status', ['draft', 'sent'])->count())
                ->description('Pending quotes')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('warning'),

            Stat::make('Total Customers', Customer::count())
                ->description('Registered users')
                ->descriptionIcon('heroicon-m-users')
                ->color('success'),

            Stat::make('Exchange Rate', $currentRate ? '1 ZAR = $' . number_format($currentRate->usd_per_zar, 4) : 'N/A')
                ->description($currentRate ? 'Updated ' . $currentRate->effective_at->diffForHumans() : 'No rate available')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('gray'),

            Stat::make('Product Catalog', Product::where('status', 'active')->count())
                ->description('Active products')
                ->descriptionIcon('heroicon-m-cube')
                ->color('primary'),
        ];
    }
}
