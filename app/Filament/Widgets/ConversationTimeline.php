<?php

namespace App\Filament\Widgets;

use App\Models\Conversation;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Carbon;

class ConversationTimeline extends ChartWidget
{
    protected static ?string $heading = 'Conversation Activity Timeline';

    protected static ?int $sort = 3;

    protected function getData(): array
    {
        $days = collect();
        $successfulData = collect();
        $failedData = collect();
        $abandonedData = collect();
        
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $days->push(now()->subDays($i)->format('M d'));
            
            $dayStart = Carbon::parse($date)->startOfDay();
            $dayEnd = Carbon::parse($date)->endOfDay();
            
            $successfulData->push(
                Conversation::whereBetween('created_at', [$dayStart, $dayEnd])
                    ->where('completion_status', 'successful')
                    ->count()
            );
            
            $failedData->push(
                Conversation::whereBetween('created_at', [$dayStart, $dayEnd])
                    ->where('completion_status', 'failed')
                    ->count()
            );
            
            $abandonedData->push(
                Conversation::whereBetween('created_at', [$dayStart, $dayEnd])
                    ->where('completion_status', 'abandoned')
                    ->count()
            );
        }
        
        return [
            'datasets' => [
                [
                    'label' => 'Successful',
                    'data' => $successfulData->toArray(),
                    'borderColor' => '#10B981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                ],
                [
                    'label' => 'Failed',
                    'data' => $failedData->toArray(),
                    'borderColor' => '#EF4444',
                    'backgroundColor' => 'rgba(239, 68, 68, 0.1)',
                ],
                [
                    'label' => 'Abandoned',
                    'data' => $abandonedData->toArray(),
                    'borderColor' => '#F59E0B',
                    'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                ],
            ],
            'labels' => $days->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }
}