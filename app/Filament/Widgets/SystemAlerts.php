<?php

namespace App\Filament\Widgets;

use App\Models\ExchangeRate;
use App\Models\PricingRule;
use Filament\Widgets\Widget;

class SystemAlerts extends Widget
{
    protected static string $view = 'filament.widgets.system-alerts';
    
    protected static ?int $sort = 0;
    
    protected int | string | array $columnSpan = 'full';

    protected function getViewData(): array
    {
        $alerts = [];
        
        // Check exchange rate freshness
        $latestRate = ExchangeRate::currentOfficial();
        if (!$latestRate || $latestRate->effective_at < now()->subHours(24)) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'Exchange Rate Update Needed',
                'message' => 'Exchange rates haven\'t been updated in the last 24 hours. This may affect pricing accuracy.',
                'action' => 'Update Now',
                'action_url' => route('filament.admin.resources.exchange-rates.index'),
            ];
        }
        
        // Check for expired pricing rules
        $expiredRules = PricingRule::where('effective_to', '<', now())
            ->count();
            
        if ($expiredRules > 0) {
            $alerts[] = [
                'type' => 'info',
                'title' => 'Expired Pricing Rules',
                'message' => "You have {$expiredRules} expired pricing rules that may need attention.",
                'action' => 'Review Rules',
                'action_url' => route('filament.admin.resources.pricing-rules.index'),
            ];
        }

        return compact('alerts');
    }
}