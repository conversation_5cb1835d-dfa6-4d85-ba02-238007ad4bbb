<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ExchangeRate extends Model
{
    protected $fillable = [
        'source',
        'usd_per_zar',
        'effective_at',
        'is_official',
        'from_currency',
        'to_currency',
        'rate',
        'is_active',
    ];

    protected $casts = [
        'usd_per_zar' => 'decimal:6',
        'effective_at' => 'datetime',
        'is_official' => 'boolean',
        'rate' => 'decimal:6',
        'is_active' => 'boolean',
    ];

    public static function currentOfficial()
    {
        return self::where('is_official', true)
            ->where('effective_at', '<=', now())
            ->orderBy('effective_at', 'desc')
            ->first();
    }
}
