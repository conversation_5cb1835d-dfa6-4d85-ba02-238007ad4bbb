<?php

namespace App\Models;

use App\Enums\ConversationState;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Conversation extends Model
{
    protected $fillable = [
        'channel',
        'customer_id',
        'conversation_intent_id',
        'conversation_flow_id',
        'wa_number',
        'state',
        'conversation_state',
        'completion_status',
        'confidence_score',
        'intent_analysis',
        'metadata',
        'flow_data',
        'current_flow',
        'current_step_key',
        'step_count',
        'last_message_at',
        'intent_detected_at',
        'flow_started_at',
        'flow_completed_at',
        'started_at',
        'escalated',
        'escalation_reason',
        'context'
    ];

    protected $casts = [
        'channel' => 'string',
        'state' => 'string',
        'conversation_state' => ConversationState::class,
        'completion_status' => 'string',
        'confidence_score' => 'decimal:2',
        'intent_analysis' => 'array',
        'metadata' => 'array',
        'flow_data' => 'array',
        'context' => 'array',
        'current_flow' => 'string',
        'step_count' => 'integer',
        'last_message_at' => 'datetime',
        'intent_detected_at' => 'datetime',
        'flow_started_at' => 'datetime',
        'flow_completed_at' => 'datetime',
        'started_at' => 'datetime',
        'escalated' => 'boolean'
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function messages(): HasMany
    {
        return $this->hasMany(Message::class);
    }

    public function contexts(): HasMany
    {
        return $this->hasMany(ConversationContext::class);
    }

    public function cart(): HasOne
    {
        return $this->hasOne(Cart::class);
    }

    public function intent(): BelongsTo
    {
        return $this->belongsTo(ConversationIntent::class, 'conversation_intent_id');
    }

    public function flow(): BelongsTo
    {
        return $this->belongsTo(ConversationFlow::class, 'conversation_flow_id');
    }

    public function analytics(): HasOne
    {
        return $this->hasOne(ConversationAnalytics::class);
    }

    public function quotations(): HasMany
    {
        return $this->hasMany(Quotation::class);
    }

    public function activeQuotations(): HasMany
    {
        return $this->quotations()->active();
    }

    // Enhanced context management using JSON field
    public function setContext(string $key, mixed $value): void
    {
        $context = $this->context ?? [];
        $context[$key] = $value;
        $this->update(['context' => $context]);
    }

    public function getContext(string $key, mixed $default = null): mixed
    {
        $context = $this->context ?? [];
        return $context[$key] ?? $default;
    }

    public function clearContext(string $key): void
    {
        $context = $this->context ?? [];
        unset($context[$key]);
        $this->update(['context' => $context]);
    }

    public function clearAllContext(): void
    {
        $this->update(['context' => []]);
    }

    // State management methods
    public function transitionTo(ConversationState $newState, ?string $reason = null): bool
    {
        if (!$this->conversation_state->canTransitionTo($newState)) {
            return false;
        }

        $this->update([
            'conversation_state' => $newState,
            'last_message_at' => now()
        ]);

        return true;
    }

    public function escalate(?string $reason = null): void
    {
        $this->update([
            'conversation_state' => ConversationState::ESCALATED,
            'escalated' => true,
            'escalation_reason' => $reason,
            'last_message_at' => now()
        ]);
    }

    public function isEscalated(): bool
    {
        return $this->escalated || $this->conversation_state === ConversationState::ESCALATED;
    }

    // Intent and Flow Management Methods
    public function detectIntent(string $userMessage): ?ConversationIntent
    {
        $intents = ConversationIntent::where('is_active', true)
            ->orderBy('priority', 'desc')
            ->get();
        
        $bestMatch = null;
        $highestScore = 0.0;
        
        foreach ($intents as $intent) {
            $score = $intent->matchesUserInput($userMessage);
            if ($score > $highestScore && $score > 0.5) {
                $highestScore = $score;
                $bestMatch = $intent;
            }
        }
        
        if ($bestMatch) {
            $this->update([
                'conversation_intent_id' => $bestMatch->id,
                'intent_detected_at' => now(),
                'confidence_score' => $highestScore,
                'intent_analysis' => [
                    'message' => $userMessage,
                    'detected_intent' => $bestMatch->name,
                    'confidence' => $highestScore,
                    'timestamp' => now()->toIso8601String(),
                ],
            ]);
        }
        
        return $bestMatch;
    }

    public function startFlow(ConversationFlow $flow): void
    {
        $firstStep = $flow->getFirstStep();
        
        $this->update([
            'conversation_flow_id' => $flow->id,
            'current_step_key' => $firstStep?->step_key,
            'flow_started_at' => now(),
            'step_count' => 0,
            'completion_status' => 'pending',
            'flow_data' => [],
        ]);
        
        if ($this->analytics) {
            $this->analytics->update([
                'conversation_flow_id' => $flow->id,
                'started_at' => now(),
            ]);
        } else {
            ConversationAnalytics::create([
                'conversation_id' => $this->id,
                'conversation_intent_id' => $this->conversation_intent_id,
                'conversation_flow_id' => $flow->id,
                'customer_id' => $this->customer_id,
                'started_at' => now(),
            ]);
        }
    }

    public function advanceFlow(string $userInput = null): ?FlowStep
    {
        if (!$this->flow || !$this->current_step_key) {
            return null;
        }
        
        $context = array_merge(
            $this->flow_data ?? [],
            ['user_input' => $userInput]
        );
        
        $nextStep = $this->flow->getNextStep($this->current_step_key, $context);
        
        if ($nextStep) {
            $this->update([
                'current_step_key' => $nextStep->step_key,
                'step_count' => $this->step_count + 1,
                'flow_data' => $context,
            ]);
            
            if ($nextStep->type === 'end') {
                $this->completeFlow($nextStep->content['success'] ?? true);
            }
        }
        
        return $nextStep;
    }

    public function completeFlow(bool $success = true): void
    {
        $this->update([
            'flow_completed_at' => now(),
            'completion_status' => $success ? 'successful' : 'failed',
        ]);
        
        if ($this->analytics) {
            $this->analytics->update([
                'ended_at' => now(),
                'goal_achieved' => $success,
            ]);
            $this->analytics->calculateDuration();
        }
    }

    public function abandonFlow(): void
    {
        $this->update([
            'flow_completed_at' => now(),
            'completion_status' => 'abandoned',
        ]);
        
        if ($this->analytics) {
            $this->analytics->update([
                'ended_at' => now(),
                'goal_achieved' => false,
            ]);
            $this->analytics->recordEvent('flow_abandoned', [
                'step' => $this->current_step_key,
                'reason' => 'user_abandoned',
            ]);
        }
    }
}
