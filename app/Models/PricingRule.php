<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PricingRule extends Model
{
    protected $fillable = [
        'scope_type',
        'scope_id',
        'key',
        'value',
        'value_type',
        'effective_from',
        'effective_to',
        'rule_name',
        'scope',
        'params',
        'active',
    ];

    protected $casts = [
        'scope_type' => 'string',
        'value_type' => 'string',
        'effective_from' => 'datetime',
        'effective_to' => 'datetime',
        'params' => 'array',
        'active' => 'boolean',
    ];

    public function getValueAsNumber()
    {
        if ($this->value_type === 'json') {
            return json_decode($this->value, true);
        }
        return floatval($this->value);
    }

    public static function getActiveRule($key, $scopeType = 'global', $scopeId = null)
    {
        $query = self::where('key', $key)
            ->where('scope_type', $scopeType)
            ->where(function ($q) {
                $q->whereNull('effective_from')
                    ->orWhere('effective_from', '<=', now());
            })
            ->where(function ($q) {
                $q->whereNull('effective_to')
                    ->orWhere('effective_to', '>=', now());
            });

        if ($scopeId) {
            $query->where('scope_id', $scopeId);
        }

        return $query->orderBy('created_at', 'desc')->first();
    }
}
