<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Notification extends Model
{
    protected $table = 'customer_notifications';
    
    protected $fillable = [
        'notifiable_type',
        'notifiable_id',
        'type',
        'channel',
        'title',
        'message',
        'data',
        'scheduled_at',
        'sent_at',
        'status',
        'failure_reason',
    ];

    protected $casts = [
        'data' => 'array',
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
    ];

    public function notifiable(): MorphTo
    {
        return $this->morphTo();
    }
}