<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Quotation extends Model
{
    protected $fillable = [
        'conversation_id',
        'product_id',
        'original_price_zar',
        'quantity',
        'exchange_rate',
        'product_price_usd',
        'markup_amount',
        'shipping_cost',
        'duties_amount',
        'handling_fee',
        'payment_processing_fee',
        'landing_cost_usd',
        'breakdown',
        'valid_until',
        'status'
    ];

    protected $casts = [
        'original_price_zar' => 'decimal:2',
        'exchange_rate' => 'decimal:4',
        'product_price_usd' => 'decimal:2',
        'markup_amount' => 'decimal:2',
        'shipping_cost' => 'decimal:2',
        'duties_amount' => 'decimal:2',
        'handling_fee' => 'decimal:2',
        'payment_processing_fee' => 'decimal:2',
        'landing_cost_usd' => 'decimal:2',
        'breakdown' => 'array',
        'valid_until' => 'datetime',
        'quantity' => 'integer'
    ];

    public function conversation(): BelongsTo
    {
        return $this->belongsTo(Conversation::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function isValid(): bool
    {
        return $this->valid_until > now() && $this->status === 'active';
    }

    public function isExpired(): bool
    {
        return $this->valid_until <= now() || $this->status === 'expired';
    }

    public function markExpired(): void
    {
        $this->update(['status' => 'expired']);
    }

    public function accept(): void
    {
        $this->update(['status' => 'accepted']);
    }

    public function decline(): void
    {
        $this->update(['status' => 'declined']);
    }

    public function getFormattedTotalAttribute(): string
    {
        return '$' . number_format($this->landing_cost_usd, 0);
    }

    public function getFormattedBreakdownAttribute(): array
    {
        $breakdown = $this->breakdown ?? [];
        $formatted = [];

        foreach ($breakdown as $key => $value) {
            $formatted[$key] = [
                'amount' => $value,
                'formatted' => '$' . number_format($value, 2)
            ];
        }

        return $formatted;
    }

    public function getDaysUntilExpiryAttribute(): int
    {
        return max(0, now()->diffInDays($this->valid_until, false));
    }

    public function getHoursUntilExpiryAttribute(): int
    {
        return max(0, now()->diffInHours($this->valid_until, false));
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active')->where('valid_until', '>', now());
    }

    public function scopeExpired($query)
    {
        return $query->where('status', 'expired')->orWhere('valid_until', '<=', now());
    }

    public function scopeAccepted($query)
    {
        return $query->where('status', 'accepted');
    }

    public function scopeForConversation($query, $conversationId)
    {
        return $query->where('conversation_id', $conversationId);
    }

    public function toWhatsAppMessage(?string $productName = null): string
    {
        $productName = $productName ?? ($this->product ? $this->product->title : 'Selected Product');

        $message = "💰 *Pricing for {$productName}*\n\n";
        $message .= "🛒 *Total Delivered: {$this->formatted_total}*\n\n";

        if ($this->breakdown) {
            $message .= "📊 *Breakdown:*\n";
            foreach ($this->formatted_breakdown as $key => $item) {
                $label = match($key) {
                    'base_usd' => 'Product Price',
                    'markup' => 'Service Fee',
                    'logistics' => 'Shipping',
                    'duties' => 'Import Duties',
                    'handling' => 'Handling',
                    'payment' => 'Payment Processing',
                    default => ucfirst($key)
                };
                $message .= "• {$label}: {$item['formatted']}\n";
            }
            $message .= "\n";
        }

        $message .= "⏰ *Valid until:* " . $this->valid_until->format('M j, Y g:i A') . "\n";
        $message .= "🚚 *Delivery:* 3-5 working days\n";
        $message .= "💳 *Payment:* 50% deposit required\n\n";

        $message .= "Ready to proceed? Reply:\n";
        $message .= "• 'ORDER' to place order\n";
        $message .= "• 'AGENT' to speak with our team";

        return $message;
    }

    protected static function booted()
    {
        // Auto-expire quotations
        static::creating(function ($quotation) {
            if (!$quotation->valid_until) {
                $quotation->valid_until = now()->addDays(3);
            }
        });
    }
}