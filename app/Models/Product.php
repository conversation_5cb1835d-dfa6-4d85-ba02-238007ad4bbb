<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Product extends Model
{
    protected $fillable = [
        'shop_id',
        'category_id',
        'name',
        'title',
        'slug',
        'description',
        'brand',
        'model',
        'outlet',
        'source_url',
        'image_url',
        'sku',
        'last_seen_price',
        'currency',
        'weight_kg',
        'volume_m3',
        'status',
        'is_active',
    ];

    protected $casts = [
        'weight_kg' => 'decimal:3',
        'volume_m3' => 'decimal:4',
        'last_seen_price' => 'decimal:2',
        'is_active' => 'boolean',
        'status' => 'string',
    ];

    public function shop(): BelongsTo
    {
        return $this->belongsTo(Shop::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class);
    }

    public function images(): HasMany
    {
        return $this->hasMany(ProductImage::class);
    }

    public function primaryImage()
    {
        return $this->hasOne(ProductImage::class)->where('is_primary', true);
    }
}
