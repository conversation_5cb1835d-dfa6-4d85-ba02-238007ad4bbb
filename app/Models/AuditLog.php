<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AuditLog extends Model
{
    protected $fillable = [
        'actor_type',
        'actor_id',
        'event',
        'payload',
    ];

    protected $casts = [
        'payload' => 'array',
    ];

    const ACTOR_BOT = 'bot';
    const ACTOR_ADMIN = 'admin';
    const ACTOR_SYSTEM = 'system';

    public static function log(string $actorType, ?int $actorId, string $event, array $payload = []): self
    {
        return self::create([
            'actor_type' => $actorType,
            'actor_id' => $actorId,
            'event' => $event,
            'payload' => $payload,
        ]);
    }
}
