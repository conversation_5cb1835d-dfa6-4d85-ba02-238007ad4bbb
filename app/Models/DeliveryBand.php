<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DeliveryBand extends Model
{
    protected $fillable = [
        'city',
        'band_key',
        'price_usd',
    ];

    protected $casts = [
        'price_usd' => 'decimal:2',
    ];

    public static function getPriceForCity(string $city): float
    {
        $band = self::where('city', $city)->first();
        return $band ? $band->price_usd : 12.00; // Default delivery price
    }
}
