<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductVariant extends Model
{
    protected $fillable = [
        'product_id',
        'sku',
        'attrs_json',
        'sa_price_zar',
        'currency',
        'barcode',
        'stock_status',
        'price_last_seen_at',
    ];

    protected $casts = [
        'attrs_json' => 'array',
        'sa_price_zar' => 'decimal:2',
        'stock_status' => 'string',
        'price_last_seen_at' => 'datetime',
    ];

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function cartItems(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }
}
