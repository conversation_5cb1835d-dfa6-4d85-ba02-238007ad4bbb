<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Catalogue extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'shop_id',
        'title',
        'slug',
        'description',
        'type',
        'pdf_url',
        'images',
        'cover_url',
        'page_count',
        'file_size',
        'valid_from',
        'valid_to',
        'status',
    ];

    protected $casts = [
        'images' => 'array',
        'valid_from' => 'date',
        'valid_to' => 'date',
        'file_size' => 'integer',
        'page_count' => 'integer',
    ];

    protected $dates = [
        'valid_from',
        'valid_to',
        'deleted_at',
    ];

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Get full URL for PDF file
     */
    public function getPdfUrlAttribute($value)
    {
        if (!$value) {
            return null;
        }
        
        // If it's already a full URL, return as is
        if (filter_var($value, FILTER_VALIDATE_URL)) {
            return $value;
        }
        
        // File paths are stored relative to uploads directory
        return asset('uploads/' . $value);
    }

    /**
     * Get full URL for cover file
     */
    public function getCoverUrlAttribute($value)
    {
        if (!$value) {
            return null;
        }
        
        // If it's already a full URL, return as is
        if (filter_var($value, FILTER_VALIDATE_URL)) {
            return $value;
        }
        
        // File paths are stored relative to uploads directory
        return asset('uploads/' . $value);
    }

    /**
     * Get full URLs for images
     */
    public function getImagesAttribute($value)
    {
        if (!$value) {
            return [];
        }
        
        // Handle potential double JSON encoding
        $images = $value;
        if (is_string($images)) {
            $images = json_decode($images, true);
            // If still a string, try decoding again (double encoded)
            if (is_string($images)) {
                $images = json_decode($images, true);
            }
        }
        
        if (!is_array($images)) {
            return [];
        }
        
        return array_map(function($image) {
            if (filter_var($image, FILTER_VALIDATE_URL)) {
                return $image;
            }
            return asset('uploads/' . $image);
        }, $images);
    }

    /**
     * Relationship with Shop
     */
    public function shop(): BelongsTo
    {
        return $this->belongsTo(Shop::class);
    }

    /**
     * Get catalogue ingestions
     */
    public function ingestions(): HasMany
    {
        return $this->hasMany(CatalogueIngestion::class);
    }

    /**
     * Get extracted products
     */
    public function extractedProducts(): HasMany
    {
        return $this->hasMany(ExtractedProduct::class);
    }

    /**
     * Generate and set slug from title
     */
    public static function boot()
    {
        parent::boot();

        static::creating(function ($catalogue) {
            if (empty($catalogue->slug)) {
                $catalogue->slug = static::generateUniqueSlug($catalogue->title);
            }
            
            // Auto-set page count for new catalogues with images
            if ($catalogue->type === 'images' && $catalogue->images) {
                $images = $catalogue->images;
                if (is_string($images)) {
                    $images = json_decode($images, true) ?? [];
                }
                $catalogue->page_count = is_array($images) ? count($images) : 0;
            }
        });

        static::updating(function ($catalogue) {
            if ($catalogue->isDirty('title') && empty($catalogue->slug)) {
                $catalogue->slug = static::generateUniqueSlug($catalogue->title);
            }
            
            // Auto-update page count when images change
            if ($catalogue->isDirty('images') && $catalogue->type === 'images') {
                $images = $catalogue->images;
                if (is_string($images)) {
                    $images = json_decode($images, true) ?? [];
                }
                $catalogue->page_count = is_array($images) ? count($images) : 0;
            }
        });
    }

    /**
     * Generate unique slug for catalogue
     */
    private static function generateUniqueSlug($title)
    {
        $baseSlug = Str::slug($title);
        $slug = $baseSlug;
        $counter = 1;

        while (static::where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Scope for active catalogues
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for valid catalogues (not expired)
     */
    public function scopeValid($query)
    {
        return $query->where(function ($query) {
            $query->whereNull('valid_to')
                  ->orWhere('valid_to', '>=', now());
        });
    }

    /**
     * Check if catalogue is currently valid
     */
    public function getIsValidAttribute()
    {
        if ($this->status !== 'active') {
            return false;
        }

        if ($this->valid_from && $this->valid_from->isFuture()) {
            return false;
        }

        if ($this->valid_to && $this->valid_to->isPast()) {
            return false;
        }

        return true;
    }

    /**
     * Get cover image URL with fallback
     */
    public function getCoverImageAttribute()
    {
        if ($this->cover_url) {
            // If it's already a full URL, return as is
            if (filter_var($this->cover_url, FILTER_VALIDATE_URL)) {
                return $this->cover_url;
            }
            // File paths are stored relative to uploads directory
            return asset('uploads/' . $this->cover_url);
        }

        if ($this->type === 'images' && !empty($this->images)) {
            $firstImage = $this->images[0];
            if (filter_var($firstImage, FILTER_VALIDATE_URL)) {
                return $firstImage;
            }
            return asset('uploads/' . $firstImage);
        }

        return '/images/placeholder-catalogue.jpg';
    }

    /**
     * Get formatted file size
     */
    public function getFormattedFileSizeAttribute()
    {
        if (!$this->file_size) {
            return null;
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = $this->file_size;
        $i = 0;

        while ($bytes >= 1024 && $i < count($units) - 1) {
            $bytes /= 1024;
            $i++;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }
}