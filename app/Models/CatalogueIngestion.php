<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CatalogueIngestion extends Model
{
    protected $fillable = [
        'catalogue_id',
        'user_id',
        'status',
        'started_at',
        'completed_at',
        'total_pages',
        'processed_pages',
        'products_found',
        'products_approved',
        'products_rejected',
        'ai_config',
        'extraction_metadata',
        'confidence_score',
        'processing_notes',
        'error_message',
        'reviewed_by',
        'reviewed_at',
        'review_notes',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'reviewed_at' => 'datetime',
        'ai_config' => 'array',
        'extraction_metadata' => 'array',
        'confidence_score' => 'decimal:2',
        'total_pages' => 'integer',
        'processed_pages' => 'integer',
        'products_found' => 'integer',
        'products_approved' => 'integer',
        'products_rejected' => 'integer',
    ];

    /**
     * Status constants
     */
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_EXTRACTING = 'extracting';
    const STATUS_REVIEW = 'review';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_FAILED = 'failed';

    /**
     * Get the catalogue being ingested
     */
    public function catalogue(): BelongsTo
    {
        return $this->belongsTo(Catalogue::class);
    }

    /**
     * Get the user who initiated the ingestion
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who reviewed the ingestion
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Get the extracted products
     */
    public function extractedProducts(): HasMany
    {
        return $this->hasMany(ExtractedProduct::class, 'ingestion_id');
    }

    /**
     * Get pending products for review
     */
    public function pendingProducts(): HasMany
    {
        return $this->extractedProducts()->where('review_status', 'pending');
    }

    /**
     * Get approved products
     */
    public function approvedProducts(): HasMany
    {
        return $this->extractedProducts()->where('review_status', 'approved');
    }

    /**
     * Check if ingestion is complete
     */
    public function isComplete(): bool
    {
        return in_array($this->status, [
            self::STATUS_APPROVED,
            self::STATUS_REJECTED,
            self::STATUS_FAILED
        ]);
    }

    /**
     * Check if ingestion is in progress
     */
    public function isProcessing(): bool
    {
        return in_array($this->status, [
            self::STATUS_PROCESSING,
            self::STATUS_EXTRACTING
        ]);
    }

    /**
     * Check if ready for review
     */
    public function isReadyForReview(): bool
    {
        return $this->status === self::STATUS_REVIEW;
    }

    /**
     * Get processing progress percentage
     */
    public function getProgressPercentage(): float
    {
        if (!$this->total_pages || $this->total_pages === 0) {
            return 0;
        }

        return round(($this->processed_pages / $this->total_pages) * 100, 2);
    }

    /**
     * Get status badge color for UI
     */
    public function getStatusColor(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'gray',
            self::STATUS_PROCESSING, self::STATUS_EXTRACTING => 'info',
            self::STATUS_REVIEW => 'warning',
            self::STATUS_APPROVED => 'success',
            self::STATUS_REJECTED => 'danger',
            self::STATUS_FAILED => 'danger',
            default => 'gray'
        };
    }

    /**
     * Start the ingestion process
     */
    public function startProcessing(): void
    {
        $this->update([
            'status' => self::STATUS_PROCESSING,
            'started_at' => now(),
        ]);
    }

    /**
     * Mark as ready for review
     */
    public function markForReview(): void
    {
        $this->update([
            'status' => self::STATUS_REVIEW,
            'completed_at' => now(),
        ]);
    }

    /**
     * Approve the ingestion
     */
    public function approve($userId, $notes = null): void
    {
        $this->update([
            'status' => self::STATUS_APPROVED,
            'reviewed_by' => $userId,
            'reviewed_at' => now(),
            'review_notes' => $notes,
        ]);
    }

    /**
     * Reject the ingestion
     */
    public function reject($userId, $notes = null): void
    {
        $this->update([
            'status' => self::STATUS_REJECTED,
            'reviewed_by' => $userId,
            'reviewed_at' => now(),
            'review_notes' => $notes,
        ]);
    }

    /**
     * Mark as failed
     */
    public function markAsFailed($errorMessage): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'error_message' => $errorMessage,
            'completed_at' => now(),
        ]);
    }

    /**
     * Update product counts
     */
    public function updateProductCounts(): void
    {
        $this->update([
            'products_found' => $this->extractedProducts()->count(),
            'products_approved' => $this->extractedProducts()
                ->where('review_status', 'approved')->count(),
            'products_rejected' => $this->extractedProducts()
                ->where('review_status', 'rejected')->count(),
        ]);
    }
}