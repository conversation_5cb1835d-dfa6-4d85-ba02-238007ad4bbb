<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Quote extends Model
{
    protected $fillable = [
        'customer_id',
        'cart_id',
        'shop_id',
        'total_usd',
        'breakdown_json',
        'valid_until',
        'status',
        'fx_snapshot_json',
    ];

    protected $casts = [
        'total_usd' => 'decimal:2',
        'breakdown_json' => 'array',
        'fx_snapshot_json' => 'array',
        'valid_until' => 'datetime',
        'status' => 'string',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function cart(): BelongsTo
    {
        return $this->belongsTo(Cart::class);
    }

    public function shop(): BelongsTo
    {
        return $this->belongsTo(Shop::class);
    }

    public function cartItems(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    public function order(): HasOne
    {
        return $this->hasOne(Order::class);
    }
}
