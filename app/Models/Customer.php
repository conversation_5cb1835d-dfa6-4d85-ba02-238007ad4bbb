<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone_e164',
        'phone',
        'whatsapp_opt_in',
        'is_blocked',
        'password',
    ];

    protected $hidden = [
        'password',
    ];

    protected $casts = [
        'whatsapp_opt_in' => 'boolean',
        'is_blocked' => 'boolean',
    ];

    public function addresses(): HasMany
    {
        return $this->hasMany(Address::class);
    }

    public function carts(): HasMany
    {
        return $this->hasMany(Cart::class);
    }

    public function quotes(): HasMany
    {
        return $this->hasMany(Quote::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function conversations(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Conversation::class);
    }

    public function intakeItems(): Has<PERSON><PERSON>
    {
        return $this->hasMany(IntakeItem::class);
    }

    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }
}
