<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Order extends Model
{
    protected $fillable = [
        'quote_id',
        'customer_id',
        'cart_snapshot',
        'total_usd',
        'city',
        'reference',
        'order_ref',
        'status',
        'breakdown_json',
        'shipping_pref',
        'notes',
    ];

    const STATUS_NEW = 'NEW';
    const STATUS_IN_PROGRESS = 'IN_PROGRESS';
    const STATUS_FULFILLED = 'FULFILLED';
    const STATUS_CANCELLED = 'CANCELLED';

    protected $casts = [
        'total_usd' => 'decimal:2',
        'breakdown_json' => 'array',
        'cart_snapshot' => 'array',
        'status' => 'string',
    ];

    public function quote(): BelongsTo
    {
        return $this->belongsTo(Quote::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    public function shipment(): HasOne
    {
        return $this->hasOne(Shipment::class);
    }
}
