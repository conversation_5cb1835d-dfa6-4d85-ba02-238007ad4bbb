<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CartItem extends Model
{
    protected $fillable = [
        'cart_id',
        'product_variant_id',
        'product_name',
        'outlet',
        'source_url',
        'price_value',
        'price_currency',
        'computed_total_usd',
        'meta',
        'qty',
        'quote_id',
    ];

    protected $casts = [
        'qty' => 'integer',
        'price_value' => 'decimal:2',
        'computed_total_usd' => 'decimal:2',
        'meta' => 'array',
    ];

    public function cart(): BelongsTo
    {
        return $this->belongsTo(Cart::class);
    }

    public function productVariant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class);
    }

    public function quote(): BelongsTo
    {
        return $this->belongsTo(Quote::class);
    }
}
