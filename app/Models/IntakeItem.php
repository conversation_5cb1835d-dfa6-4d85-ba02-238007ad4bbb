<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class IntakeItem extends Model
{
    protected $fillable = [
        'customer_id',
        'type',
        'raw_text',
        'raw_url',
        'media_id',
        'extracted_name',
        'extracted_outlet',
        'extracted_price',
        'currency',
        'confidence',
        'status',
        'ai_analysis',
    ];

    protected $casts = [
        'extracted_price' => 'decimal:2',
        'confidence' => 'integer',
        'ai_analysis' => 'array',
    ];

    const TYPE_TEXT = 'text';
    const TYPE_LINK = 'link';
    const TYPE_IMAGE = 'image';

    const STATUS_PARSED = 'parsed';
    const STATUS_NEEDS_PRICE = 'needs_price';
    const STATUS_NEEDS_OUTLET = 'needs_outlet';
    const STATUS_READY = 'ready';

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function isReady(): bool
    {
        return $this->status === self::STATUS_READY;
    }

    public function needsPrice(): bool
    {
        return $this->status === self::STATUS_NEEDS_PRICE;
    }

    public function needsOutlet(): bool
    {
        return $this->status === self::STATUS_NEEDS_OUTLET;
    }
}
