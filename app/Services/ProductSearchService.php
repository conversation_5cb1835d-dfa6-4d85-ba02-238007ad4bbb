<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ExternalProductSearch;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

class ProductSearchService
{
    public function searchByKeywords($keywords, $limit = 10)
    {
        $normalizedQuery = $this->normalizeSearchQuery($keywords);
        
        // Extract search components
        $components = $this->extractSearchComponents($normalizedQuery);
        
        $query = Product::query()
            ->where('status', 'active');
        
        // Build flexible search query
        $query->where(function($mainQuery) use ($components, $normalizedQuery) {
            // Exact match gets highest priority
            $mainQuery->where(function($q) use ($normalizedQuery) {
                $q->where('title', 'like', "%{$normalizedQuery}%")
                  ->orWhere('description', 'like', "%{$normalizedQuery}%")
                  ->orWhere('model', 'like', "%{$normalizedQuery}%");
            });
            
            // Brand + size matching
            if ($components['brand'] && $components['size']) {
                $mainQuery->orWhere(function($q) use ($components) {
                    $q->where('brand', 'like', "%{$components['brand']}%")
                      ->where(function($sizeQuery) use ($components) {
                          $sizeQuery->where('title', 'like', "%{$components['size']}%")
                                   ->orWhere('model', 'like', "%{$components['size']}%")
                                   ->orWhere('description', 'like', "%{$components['size']}%");
                      });
                });
            }
            
            // Brand + category matching
            if ($components['brand'] && $components['category']) {
                $mainQuery->orWhere(function($q) use ($components) {
                    $q->where('brand', 'like', "%{$components['brand']}%")
                      ->where(function($catQuery) use ($components) {
                          $catQuery->where('title', 'like', "%{$components['category']}%")
                                  ->orWhere('description', 'like', "%{$components['category']}%");
                      });
                });
            }
            
            // Fallback: any matching terms
            $mainQuery->orWhere(function($q) use ($components) {
                if ($components['brand']) {
                    $q->where('brand', 'like', "%{$components['brand']}%");
                }
                foreach ($components['terms'] as $term) {
                    $q->orWhere('title', 'like', "%{$term}%")
                      ->orWhere('description', 'like', "%{$term}%")
                      ->orWhere('model', 'like', "%{$term}%");
                }
            });
        });
        
        return $query->with(['primaryImage', 'variants'])
            ->limit($limit)
            ->get()
            ->map(function($product) {
                return [
                    'id' => $product->id,
                    'title' => $product->title,
                    'description' => $product->description,
                    'brand' => $product->brand,
                    'model' => $product->model,
                    'weight_kg' => $product->weight_kg,
                    'image' => $product->primaryImage?->image_url,
                    'price' => $product->variants->first()?->sa_price_zar ?? 0,
                    'source' => 'catalog',
                ];
            });
    }

    private function normalizeSearchQuery($keywords)
    {
        return strtolower(trim(preg_replace('/\s+/', ' ', $keywords)));
    }

    private function extractSearchComponents($query)
    {
        $components = [
            'brand' => null,
            'size' => null,
            'category' => null,
            'terms' => [],
        ];
        
        // Common brands
        $brands = ['samsung', 'lg', 'sony', 'panasonic', 'hisense', 'tcl', 'apple', 'iphone', 'macbook', 'dell', 'hp', 'lenovo'];
        
        // Screen sizes (TV/monitor)
        $sizes = [];
        if (preg_match('/(\d+)\s*(inch|"|in|\'\')/i', $query, $matches)) {
            $components['size'] = $matches[1];
            $sizes[] = $matches[1];
        }
        
        // TV specific sizes
        if (preg_match('/(\d+)(a\d+|nano\d+|oled|qled)/i', $query, $matches)) {
            $components['size'] = $matches[1];
            $sizes[] = $matches[1];
        }
        
        // Product categories
        $categories = ['tv', 'television', 'smart tv', 'laptop', 'phone', 'smartphone', 'tablet', 'monitor'];
        
        $words = explode(' ', $query);
        
        foreach ($words as $word) {
            $word = strtolower($word);
            
            // Check for brands
            foreach ($brands as $brand) {
                if (strpos($word, $brand) !== false || strpos($brand, $word) !== false) {
                    $components['brand'] = $brand;
                    break;
                }
            }
            
            // Check for categories
            foreach ($categories as $category) {
                if (strpos($query, $category) !== false) {
                    $components['category'] = $category;
                    break;
                }
            }
            
            // Collect meaningful terms (excluding common words)
            $stopWords = ['the', 'and', 'or', 'with', 'from', 'inch', 'smart'];
            if (!in_array($word, $stopWords) && strlen($word) > 2) {
                $components['terms'][] = $word;
            }
        }
        
        return $components;
    }

    public function searchByImage($imagePath)
    {
        // TODO: Implement image recognition using a service like Google Vision API
        // For now, we'll return similar products based on metadata
        
        // This is a placeholder implementation
        // In production, you'd use image recognition API
        
        return collect([]);
    }

    public function searchExternal($query, $sources = ['alibaba', 'aliexpress'])
    {
        $results = collect([]);
        
        foreach ($sources as $source) {
            $sourceResults = $this->searchBySource($source, $query);
            $results = $results->merge($sourceResults);
        }
        
        return $results;
    }

    private function searchBySource($source, $query)
    {
        switch ($source) {
            case 'alibaba':
                return $this->searchAlibaba($query);
            case 'aliexpress':
                return $this->searchAliexpress($query);
            case 'amazon':
                return $this->searchAmazon($query);
            default:
                return collect([]);
        }
    }

    private function searchAlibaba($query)
    {
        // This would integrate with Alibaba's API
        // For demonstration, we'll create mock results
        
        $mockResults = [
            [
                'source' => 'alibaba',
                'product_name' => "Sample Product from Alibaba - {$query}",
                'description' => 'High quality product matching your search',
                'price' => rand(10, 500),
                'currency' => 'USD',
                'weight_kg' => rand(1, 50) / 10,
                'image_url' => 'https://via.placeholder.com/300',
                'product_url' => 'https://www.alibaba.com/product/sample',
            ]
        ];
        
        // Save to database for future reference
        foreach ($mockResults as $result) {
            ExternalProductSearch::create($result);
        }
        
        return collect($mockResults);
    }

    private function searchAliexpress($query)
    {
        // Similar implementation for AliExpress
        $mockResults = [
            [
                'source' => 'aliexpress',
                'product_name' => "AliExpress Product - {$query}",
                'description' => 'Affordable product with fast shipping',
                'price' => rand(5, 200),
                'currency' => 'USD',
                'weight_kg' => rand(1, 30) / 10,
                'image_url' => 'https://via.placeholder.com/300',
                'product_url' => 'https://www.aliexpress.com/item/sample',
            ]
        ];
        
        foreach ($mockResults as $result) {
            ExternalProductSearch::create($result);
        }
        
        return collect($mockResults);
    }

    private function searchAmazon($query)
    {
        // Amazon API integration would go here
        return collect([]);
    }

    public function combineSearchResults($catalogResults, $externalResults)
    {
        $combined = collect([]);
        
        // Add catalog results first (preferred)
        foreach ($catalogResults as $result) {
            $combined->push([
                'type' => 'catalog',
                'data' => $result,
                'confidence' => 1.0,
            ]);
        }
        
        // Add external results
        foreach ($externalResults as $result) {
            $combined->push([
                'type' => 'external',
                'data' => $result,
                'confidence' => 0.8,
            ]);
        }
        
        return $combined;
    }
}