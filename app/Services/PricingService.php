<?php

namespace App\Services;

use App\Models\ProductVariant;
use App\Models\ExchangeRate;
use App\Models\PricingRule;
use App\Models\Quote;
use Carbon\Carbon;

class PricingService
{
    public function quote(int $variantId, int $qty, array $ctx = []): array
    {
        $variant = ProductVariant::with('product.category')->findOrFail($variantId);
        $fx = ExchangeRate::currentOfficial();
        
        if (!$fx) {
            throw new \Exception('No exchange rate found');
        }

        // Base conversion
        $baseUsd = $variant->sa_price_zar * $fx->usd_per_zar * $qty;

        // Calculate components
        $logistics = $this->calculateLogistics($variant, $qty, $ctx);
        $duties = $this->calculateDuties($variant, $baseUsd);
        $handling = $this->calculateHandling($baseUsd);
        
        $subtotal = $baseUsd + $logistics + $duties + $handling;
        
        // Markup calculation
        $markup = $this->calculateMarkup($variant, $subtotal);
        
        // Payment fees
        $payment = $this->calculatePaymentFees($subtotal + $markup);
        
        $total = round($subtotal + $markup + $payment, 2);

        return [
            'final_usd' => $total,
            'breakdown' => [
                'base_usd' => round($baseUsd, 2),
                'logistics' => round($logistics, 2),
                'duties' => round($duties, 2),
                'handling' => round($handling, 2),
                'markup' => round($markup, 2),
                'payment' => round($payment, 2)
            ],
            'fx_snapshot' => [
                'usd_per_zar' => $fx->usd_per_zar,
                'effective_at' => $fx->effective_at->toISOString()
            ],
            'valid_until' => Carbon::now()->addHours(48)->toISOString(),
        ];
    }

    protected function calculateLogistics(ProductVariant $variant, int $qty, array $ctx): float
    {
        $product = $variant->product;
        $weight = ($product->weight_kg ?? 1.0) * $qty;
        $volume = ($product->volume_m3 ?? 0.01) * $qty;
        
        // Base logistics cost (can be made configurable)
        $baseRate = 15.0; // USD base rate
        $weightRate = 2.5; // per kg
        $volumeRate = 50.0; // per m3
        
        return $baseRate + ($weight * $weightRate) + ($volume * $volumeRate);
    }

    protected function calculateDuties(ProductVariant $variant, float $baseUsd): float
    {
        // Default duty rate - can be enhanced with HS mapping
        $dutyRate = $this->getRuleValue('duty_percent', 'global') ?? 15.0;
        return ($baseUsd * $dutyRate) / 100;
    }

    protected function calculateHandling(float $baseUsd): float
    {
        $handlingPercent = $this->getRuleValue('handling_percent', 'global') ?? 5.0;
        $handlingFixed = $this->getRuleValue('handling_fixed_usd', 'global') ?? 5.0;
        
        return (($baseUsd * $handlingPercent) / 100) + $handlingFixed;
    }

    protected function calculateMarkup(ProductVariant $variant, float $subtotal): float
    {
        $product = $variant->product;
        
        // Try to get markup from most specific to general
        $markupPercent = $this->getRuleValue('markup_percent', 'product', $product->id)
            ?? $this->getRuleValue('markup_percent', 'category', $product->category_id)
            ?? $this->getRuleValue('markup_percent', 'shop', $product->shop_id)
            ?? $this->getRuleValue('markup_percent', 'global')
            ?? 50.0; // Default 50%

        $floorMargin = $this->getRuleValue('floor_margin_usd', 'global') ?? 10.0;
        
        return max($floorMargin, ($subtotal * $markupPercent) / 100);
    }

    protected function calculatePaymentFees(float $amount): float
    {
        $paymentPercent = $this->getRuleValue('payment_percent', 'global') ?? 3.0;
        $paymentFixed = $this->getRuleValue('payment_fixed_usd', 'global') ?? 1.0;
        
        return (($amount * $paymentPercent) / 100) + $paymentFixed;
    }

    protected function getRuleValue(string $key, string $scopeType, ?int $scopeId = null): ?float
    {
        $rule = PricingRule::getActiveRule($key, $scopeType, $scopeId);
        return $rule ? $rule->getValueAsNumber() : null;
    }

    public function createQuote(array $quoteData): Quote
    {
        return Quote::create($quoteData);
    }

    /**
     * Simple pricing calculation like in conversation samples
     * Returns simple USD price without complex breakdown
     */
    public function calculatePrice(float $zarPrice, int $qty = 1): array
    {
        $fx = ExchangeRate::currentOfficial();
        $rate = $fx ? $fx->usd_per_zar : 0.055; // Fallback rate

        // Simple calculation: ZAR to USD + 50% markup + basic shipping
        $baseUsd = $zarPrice * $rate * $qty;
        $withMarkup = $baseUsd * 1.5; // 50% markup
        $finalPrice = $withMarkup + 10; // Add basic shipping/handling

        return [
            'final_usd' => round($finalPrice, 0),
            'breakdown' => [
                'base_usd' => round($baseUsd, 2),
                'logistics' => 10.0,
                'duties' => 0.0,
                'handling' => 0.0,
                'markup' => round($withMarkup - $baseUsd, 2),
                'payment' => 0.0
            ],
            'fx_snapshot' => [
                'usd_per_zar' => $rate,
                'effective_at' => now()->toISOString()
            ],
            'valid_until' => now()->addHours(48)->toISOString(),
        ];
    }
}