<?php

namespace App\Services;

use Twilio\Rest\Client;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WhatsAppService
{
    protected $twilio;
    protected $from;
    protected $useCloudApi;
    protected $accessToken;
    protected $phoneNumberId;
    protected $apiVersion;
    protected $baseUrl;

    public function __construct()
    {
        // Twilio configuration
        $sid = config('services.twilio.sid');
        $token = config('services.twilio.token');
        $this->from = config('services.twilio.whatsapp_from');

        // WhatsApp Cloud API configuration
        $this->accessToken = config('whatsapp.access_token');
        $this->phoneNumberId = config('whatsapp.phone_number_id');
        $this->apiVersion = config('whatsapp.api_version', 'v22.0');
        $this->baseUrl = "https://graph.facebook.com/{$this->apiVersion}/{$this->phoneNumberId}";

        // Force Twilio mode only - disable Cloud API completely
        $this->useCloudApi = false;

        if ($sid && $token) {
            $this->twilio = new Client($sid, $token);
        } else {
            throw new \Exception('Twilio credentials not configured');
        }
    }

    public function sendMessage(string $to, string $message, array $mediaUrls = []): bool
    {
        if ($this->useCloudApi) {
            return $this->sendCloudApiMessage($to, $message, $mediaUrls);
        }

        return $this->sendTwilioMessage($to, $message, $mediaUrls);
    }

    protected function sendCloudApiMessage(string $to, string $message, array $mediaUrls = []): bool
    {
        // Check if we're in test mode
        if (config('app.env') === 'local' && env('WHATSAPP_TEST_MODE', false)) {
            Log::info('WHATSAPP TEST MODE: Would send message', [
                'to' => $to,
                'message' => substr($message, 0, 100)
            ]);
            return true;
        }

        try {
            $payload = [
                'messaging_product' => 'whatsapp',
                'to' => $this->cleanPhoneNumber($to),
                'type' => 'text',
                'text' => [
                    'body' => $message
                ]
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/messages', $payload);

            $responseData = $response->json();

            if ($response->successful()) {
                Log::info('WhatsApp Cloud API message sent successfully', [
                    'to' => $to,
                    'message_id' => $responseData['messages'][0]['id'] ?? null,
                    'response' => $responseData
                ]);
                return true;
            } else {
                Log::error('WhatsApp Cloud API message failed', [
                    'to' => $to,
                    'response' => $responseData,
                    'status' => $response->status()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('WhatsApp Cloud API Exception: ' . $e->getMessage(), [
                'to' => $to,
                'message' => $message
            ]);
            return false;
        }
    }

    protected function sendTwilioMessage(string $to, string $message, array $mediaUrls = []): bool
    {
        try {
            $messageData = [
                'from' => $this->formatPhoneNumber($this->from),
                'body' => $message,
            ];

            if (!empty($mediaUrls)) {
                $messageData['mediaUrl'] = $mediaUrls;
            }

            $result = $this->twilio->messages->create(
                $this->formatPhoneNumber($to),
                $messageData
            );

            Log::info('WhatsApp Twilio message sent', [
                'to' => $to,
                'formatted_to' => $this->formatPhoneNumber($to),
                'from' => $this->from,
                'formatted_from' => $this->formatPhoneNumber($this->from),
                'message_sid' => $result->sid,
                'status' => $result->status
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send Twilio WhatsApp message: ' . $e->getMessage(), [
                'to' => $to,
                'message' => $message
            ]);
            return false;
        }
    }

    public function sendProductCard(string $to, array $product, array $priceData): bool
    {
        try {
            $message = $this->formatProductMessage($product, $priceData);
            return $this->sendMessage($to, $message);
        } catch (\Exception $e) {
            Log::error('Failed to send product card: ' . $e->getMessage());
            return false;
        }
    }

    public function sendPriceQuote(string $to, array $quoteData): bool
    {
        try {
            $message = $this->formatPriceQuoteMessage($quoteData);
            return $this->sendMessage($to, $message);
        } catch (\Exception $e) {
            Log::error('Failed to send price quote: ' . $e->getMessage());
            return false;
        }
    }

    public function validateSignature(?string $signature, string $url, array $data): bool
    {
        // If no signature provided, validation fails
        if (!$signature) {
            return false;
        }

        $authToken = config('services.twilio.token');
        
        // Build the data string according to Twilio's format
        $dataString = $url;
        if (!empty($data)) {
            // Sort parameters alphabetically
            ksort($data);
            foreach ($data as $key => $value) {
                $dataString .= $key . $value;
            }
        }
        
        // Compute signature using HMAC-SHA1 with base64 encoding
        $computedSignature = base64_encode(hash_hmac('sha1', $dataString, $authToken, true));
        
        return hash_equals($signature, $computedSignature);
    }

    protected function formatPhoneNumber(string $phone): string
    {
        if (strpos($phone, 'whatsapp:') === 0) {
            return $phone;
        }
        return 'whatsapp:' . $phone;
    }

    protected function formatProductMessage(array $product, array $priceData): string
    {
        return "That will be $" . number_format($priceData['final_usd'], 0);
    }

    protected function formatPriceQuoteMessage(array $quoteData): string
    {
        return "That will be $" . number_format($quoteData['final_usd'], 0);
    }

    public function sendTemplateMessage(string $to, string $templateName, string $languageCode = 'en_US', array $parameters = []): bool
    {
        if (!$this->useCloudApi) {
            Log::warning('Template messages only supported with Cloud API');
            return false;
        }

        try {
            $payload = [
                'messaging_product' => 'whatsapp',
                'to' => $this->cleanPhoneNumber($to),
                'type' => 'template',
                'template' => [
                    'name' => $templateName,
                    'language' => [
                        'code' => $languageCode
                    ]
                ]
            ];

            if (!empty($parameters)) {
                $payload['template']['components'] = [
                    [
                        'type' => 'body',
                        'parameters' => $parameters
                    ]
                ];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/messages', $payload);

            $responseData = $response->json();

            if ($response->successful()) {
                Log::info('WhatsApp template message sent successfully', [
                    'to' => $to,
                    'template' => $templateName,
                    'message_id' => $responseData['messages'][0]['id'] ?? null
                ]);
                return true;
            } else {
                Log::error('WhatsApp template message failed', [
                    'to' => $to,
                    'template' => $templateName,
                    'response' => $responseData,
                    'status' => $response->status()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('WhatsApp template message exception: ' . $e->getMessage(), [
                'to' => $to,
                'template' => $templateName
            ]);
            return false;
        }
    }

    public function verifyWebhook(string $verifyToken, string $challenge, string $mode): string|bool
    {
        $expectedToken = config('whatsapp.webhook_verify_token');

        Log::info('Webhook verification debug', [
            'provided_token' => $verifyToken,
            'expected_token' => $expectedToken,
            'provided_length' => strlen($verifyToken),
            'expected_length' => strlen($expectedToken),
            'mode' => $mode,
            'tokens_match' => $verifyToken === $expectedToken,
            'mode_match' => $mode === 'subscribe'
        ]);

        if ($mode === 'subscribe' && $verifyToken === $expectedToken) {
            Log::info('Webhook verified successfully');
            return $challenge;
        }

        Log::error('Webhook verification failed', [
            'provided_token' => $verifyToken,
            'expected_token' => $expectedToken,
            'mode' => $mode,
            'reason' => $mode !== 'subscribe' ? 'mode_mismatch' : 'token_mismatch'
        ]);

        return false;
    }

    public function processWebhookMessage(array $data): array
    {
        // Only process Twilio webhooks now
        return $this->processTwilioWebhook($data);
    }

    protected function processTwilioWebhook(array $data): array
    {
        try {
            // Validate required Twilio fields
            if (!isset($data['MessageSid']) || !isset($data['From'])) {
                throw new \Exception('Invalid Twilio webhook - missing required fields');
            }

            $processedMessage = [
                'id' => $data['MessageSid'],
                'from' => str_replace('whatsapp:', '', $data['From']), // Remove whatsapp: prefix
                'timestamp' => time(),
                'type' => 'text',
                'text' => $data['Body'] ?? ''
            ];

            // Handle media if present
            if (isset($data['NumMedia']) && (int)$data['NumMedia'] > 0) {
                $mediaUrl = $data['MediaUrl0'] ?? null;
                $mediaType = $data['MediaContentType0'] ?? '';

                if ($mediaUrl) {
                    if (str_starts_with($mediaType, 'image/')) {
                        $processedMessage['type'] = 'image';
                        $processedMessage['image'] = ['id' => $mediaUrl, 'mime_type' => $mediaType];
                    } elseif (str_starts_with($mediaType, 'audio/')) {
                        $processedMessage['type'] = 'audio';
                        $processedMessage['audio'] = ['id' => $mediaUrl, 'mime_type' => $mediaType];
                    } elseif (str_starts_with($mediaType, 'video/')) {
                        $processedMessage['type'] = 'video';
                        $processedMessage['video'] = ['id' => $mediaUrl, 'mime_type' => $mediaType];
                    } else {
                        $processedMessage['type'] = 'document';
                        $processedMessage['document'] = ['id' => $mediaUrl, 'mime_type' => $mediaType];
                    }
                }
            }

            Log::info('Twilio WhatsApp webhook processed', [
                'message_sid' => $data['MessageSid'],
                'from' => $processedMessage['from'],
                'type' => $processedMessage['type']
            ]);

            return [
                'success' => true,
                'messages' => [$processedMessage]
            ];

        } catch (\Exception $e) {
            Log::error('Twilio webhook processing error', [
                'message' => $e->getMessage(),
                'data' => $data
            ]);

            return [
                'success' => false,
                'error' => 'Twilio processing error: ' . $e->getMessage()
            ];
        }
    }



    protected function cleanPhoneNumber(string $phone): string
    {
        // Remove whatsapp: prefix if present
        $phone = str_replace('whatsapp:', '', $phone);
        // Remove any non-digit characters except +
        $phone = preg_replace('/[^\d+]/', '', $phone);
        return $phone;
    }

    public function getApiType(): string
    {
        return $this->useCloudApi ? 'cloud_api' : 'twilio';
    }
}