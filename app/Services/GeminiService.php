<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Services\FileUploadService;
use Illuminate\Support\Facades\Storage;

class GeminiService
{
    private string $apiKey;
    private string $baseUrl = 'https://generativelanguage.googleapis.com/v1beta';

    public function __construct()
    {
        $this->apiKey = config('services.gemini.api_key');
        
        if (!$this->apiKey) {
            throw new \Exception('Gemini API key not configured');
        }
    }

    /**
     * Generate text response from Gemini
     */
    public function generateText(string $prompt, array $context = []): array
    {
        try {
            $response = Http::timeout(30)
                ->post("{$this->baseUrl}/models/gemini-1.5-flash:generateContent?key={$this->apiKey}", [
                    'contents' => [
                        [
                            'parts' => [
                                ['text' => $this->buildPrompt($prompt, $context)]
                            ]
                        ]
                    ],
                    'generationConfig' => [
                        'temperature' => 0.7,
                        'topK' => 40,
                        'topP' => 0.95,
                        'maxOutputTokens' => 1024,
                    ],
                    'safetySettings' => [
                        [
                            'category' => 'HARM_CATEGORY_HARASSMENT',
                            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                        ],
                        [
                            'category' => 'HARM_CATEGORY_HATE_SPEECH',
                            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                        ]
                    ]
                ]);

            if ($response->successful()) {
                $data = $response->json();
                
                if (isset($data['candidates'][0]['content']['parts'][0]['text'])) {
                    return [
                        'success' => true,
                        'text' => $data['candidates'][0]['content']['parts'][0]['text'],
                        'usage' => $data['usageMetadata'] ?? null
                    ];
                }
            }

            Log::error('Gemini API error', ['response' => $response->json()]);
            
            return [
                'success' => false,
                'error' => 'Failed to generate response',
                'details' => $response->json()
            ];

        } catch (\Exception $e) {
            Log::error('Gemini service error', ['error' => $e->getMessage()]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Analyze image and extract product information
     */
    public function analyzeProductImage(string $imagePath, ?string $prompt = null): array
    {
        try {
            // Get image data from public folder
            $fullPath = public_path($imagePath);
            if (!file_exists($fullPath)) {
                throw new \Exception('Image file not found: ' . $imagePath);
            }
            
            $imageData = file_get_contents($fullPath);
            $imageBase64 = base64_encode($imageData);
            $mimeType = mime_content_type($fullPath);

            $defaultPrompt = "You are a product recognition expert for YouzeAfrika, a cross-border shopping service from Zimbabwe to South Africa. Analyze this image carefully and identify products with their details.

IMPORTANT INSTRUCTIONS:
1. Look for ALL products visible in the image
2. Pay special attention to price tags, labels, and text
3. Look for South African retailers (Makro, Game, Takealot, Pick n Pay, Woolworths, etc.)
4. Extract exact prices in ZAR format (R1,299 or R 1299 or 1299.99)
5. Be very accurate with brand names and product names
6. Identify product categories correctly
7. Return confidence based on visibility and clarity

For each product, extract:
- name: Full product name as shown
- brand: Brand name (Samsung, Apple, LG, etc.)
- model: Model number/variant if visible
- price_zar: Numeric price value in ZAR (without R symbol)
- outlet: Store name if identifiable (Makro, Game, etc.)
- category: Product category (Electronics, Appliances, etc.)
- confidence: 0-100 based on clarity

Return ONLY valid JSON in this exact format:
{
  \"products\": [
    {
      \"name\": \"Product Name\",
      \"brand\": \"Brand Name\",
      \"model\": \"Model\",
      \"price_zar\": 1299.99,
      \"outlet\": \"Store Name\",
      \"category\": \"Category\",
      \"confidence\": 95
    }
  ]
}

Focus on accuracy. If unsure about a detail, use null or indicate low confidence.";

            $finalPrompt = $prompt ?? $defaultPrompt;

            $response = Http::timeout(30)
                ->post("{$this->baseUrl}/models/gemini-1.5-flash:generateContent?key={$this->apiKey}", [
                    'contents' => [
                        [
                            'parts' => [
                                ['text' => $finalPrompt],
                                [
                                    'inline_data' => [
                                        'mime_type' => $mimeType,
                                        'data' => $imageBase64
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'generationConfig' => [
                        'temperature' => 0.3, // Lower temperature for more accurate product recognition
                        'topK' => 32,
                        'topP' => 0.9,
                        'maxOutputTokens' => 2048,
                    ]
                ]);

            if ($response->successful()) {
                $data = $response->json();
                
                if (isset($data['candidates'][0]['content']['parts'][0]['text'])) {
                    $text = $data['candidates'][0]['content']['parts'][0]['text'];
                    
                    // Try to parse JSON response
                    $jsonData = $this->extractJSON($text);
                    
                    return [
                        'success' => true,
                        'raw_text' => $text,
                        'products' => $jsonData['products'] ?? [],
                        'catalogue_info' => $jsonData['catalogue_info'] ?? null,
                        'usage' => $data['usageMetadata'] ?? null
                    ];
                }
            }

            Log::error('Gemini vision API error', ['response' => $response->json()]);
            
            return [
                'success' => false,
                'error' => 'Failed to analyze image',
                'details' => $response->json()
            ];

        } catch (\Exception $e) {
            Log::error('Gemini vision service error', ['error' => $e->getMessage()]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Build prompt with context for YouzeAfrika chat
     */
    private function buildPrompt(string $userMessage, array $context = []): string
    {
        $systemPrompt = "YouzeAfrika AI Assistant - brief responses only.

Service: SA products → Zimbabwe, 7-14 days delivery.

CRITICAL RULES:
- NEVER ask customers about their budget
- Always provide exact prices when available
- Keep responses very brief (1-2 sentences max)
- Focus on finding products and showing total costs
- Help users find what they want, give them pricing

You help with:
1. Product searches 
2. Price quotes (ZAR→USD)
3. Store browsing
4. Order help

";

        $contextString = '';
        if (!empty($context['conversation_history'])) {
            $contextString .= "\nRecent conversation:\n" . implode("\n", $context['conversation_history']);
        }

        if (!empty($context['current_products'])) {
            $contextString .= "\nCurrent products being discussed:\n" . json_encode($context['current_products'], JSON_PRETTY_PRINT);
        }

        if (!empty($context['user_location'])) {
            $contextString .= "\nUser location: " . $context['user_location'];
        }

        return $systemPrompt . $contextString . "\n\nUser: " . $userMessage . "\n\nAssistant:";
    }

    /**
     * Extract JSON from text response
     */
    private function extractJSON(string $text): array
    {
        // Try to find JSON in the response
        $jsonPattern = '/\{.*\}/s';
        
        if (preg_match($jsonPattern, $text, $matches)) {
            $jsonData = json_decode($matches[0], true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $jsonData;
            }
        }

        // If no valid JSON found, try to parse structured text
        return $this->parseStructuredText($text);
    }

    /**
     * Parse structured text when JSON parsing fails
     */
    private function parseStructuredText(string $text): array
    {
        $products = [];
        $lines = explode("\n", $text);
        $currentProduct = null;

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            // Look for product indicators
            if (stripos($line, 'product') !== false || stripos($line, 'item') !== false) {
                if ($currentProduct) {
                    $products[] = $currentProduct;
                }
                $currentProduct = ['name' => '', 'brand' => '', 'confidence' => 50];
            }

            // Extract information
            if (stripos($line, 'name:') !== false) {
                $currentProduct['name'] = trim(str_ireplace('name:', '', $line));
            } elseif (stripos($line, 'brand:') !== false) {
                $currentProduct['brand'] = trim(str_ireplace('brand:', '', $line));
            } elseif (stripos($line, 'price') !== false && preg_match('/R?\s*(\d+[.,]\d+|\d+)/', $line, $matches)) {
                $currentProduct['estimated_price_zar'] = (float) str_replace(',', '', $matches[1]);
            }
        }

        if ($currentProduct) {
            $products[] = $currentProduct;
        }

        return ['products' => $products];
    }
}