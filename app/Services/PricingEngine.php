<?php

namespace App\Services;

use App\Models\ExchangeRate;
use App\Models\PricingRule;
use App\Models\DeliveryBand;
use Illuminate\Support\Facades\Log;

class PricingEngine
{
    /**
     * Calculate USD landed price according to guide.txt formula:
     * base = source_price * fx_rate
     * markup = base * markup_percent_by_outlet_or_category
     * delivery = delivery_table[city|weight_band]
     * payment_fee = (base + markup) * payment_rate
     * total_usd = round_up(base + markup + delivery + payment_fee + misc_duty, 0.50)
     */
    public function calculate(float $sourcePrice, string $currency = 'ZAR', ?string $outlet = null, string $city = 'Harare'): array
    {
        try {
            // Step 1: Convert to USD base price
            $fxRate = $this->getFxRate($currency);
            $baseUsd = $sourcePrice * $fxRate;
            
            // Step 2: Apply markup
            $markupPercent = $this->getMarkupPercent($outlet);
            $markup = $baseUsd * ($markupPercent / 100);
            
            // Step 3: Get delivery cost
            $delivery = $this->getDeliveryCost($city);
            
            // Step 4: Calculate payment fee
            $paymentRate = $this->getPaymentRate();
            $paymentFee = ($baseUsd + $markup) * ($paymentRate / 100);
            
            // Step 5: Add miscellaneous duties
            $miscDuty = $this->getMiscDuty($baseUsd);
            
            // Step 6: Calculate total and round up to nearest 0.50
            $subtotal = $baseUsd + $markup + $delivery + $paymentFee + $miscDuty;
            $totalUsd = $this->roundUp($subtotal, 0.5);
            
            return [
                'source_price' => $sourcePrice,
                'currency' => $currency,
                'fx_rate' => $fxRate,
                'base_usd' => round($baseUsd, 2),
                'markup_percent' => $markupPercent,
                'markup_usd' => round($markup, 2),
                'delivery' => round($delivery, 2),
                'payment_fee' => round($paymentFee, 2),
                'misc_duty' => round($miscDuty, 2),
                'subtotal' => round($subtotal, 2),
                'total_usd' => $totalUsd,
                'breakdown' => [
                    'base_usd' => round($baseUsd, 2),
                    'markup' => round($markup, 2),
                    'delivery' => round($delivery, 2),
                    'payment_fee' => round($paymentFee, 2),
                    'misc_duty' => round($miscDuty, 2),
                ],
                'valid_until' => now()->addHours(24)->format('Y-m-d H:i'),
            ];
            
        } catch (\Exception $e) {
            Log::error('Pricing calculation failed', [
                'source_price' => $sourcePrice,
                'currency' => $currency,
                'outlet' => $outlet,
                'error' => $e->getMessage()
            ]);
            
            throw new \Exception('Unable to calculate pricing: ' . $e->getMessage());
        }
    }

    /**
     * Get foreign exchange rate
     */
    protected function getFxRate(string $currency): float
    {
        if ($currency === 'USD') {
            return 1.0;
        }
        
        // Try to get current rate from database
        $exchangeRate = ExchangeRate::where('from_currency', $currency)
            ->where('to_currency', 'USD')
            ->where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($exchangeRate) {
            return (float) $exchangeRate->rate;
        }
        
        // Fallback rates
        $fallbackRates = [
            'ZAR' => 0.055, // 1 ZAR = 0.055 USD (approx 18.2 ZAR per USD)
            'EUR' => 1.10,
            'GBP' => 1.25,
        ];
        
        return $fallbackRates[$currency] ?? 0.055;
    }

    /**
     * Get markup percentage based on outlet or category
     */
    protected function getMarkupPercent(?string $outlet): float
    {
        // Try to get outlet-specific markup
        if ($outlet) {
            $rule = PricingRule::where('rule_name', 'outlet_markup')
                ->where('scope', 'outlet')
                ->where('active', true)
                ->whereJsonContains('params->outlet', $outlet)
                ->first();
                
            if ($rule && isset($rule->params['markup_percent'])) {
                return (float) $rule->params['markup_percent'];
            }
        }
        
        // Get global markup
        $globalRule = PricingRule::where('rule_name', 'global_markup')
            ->where('scope', 'global')
            ->where('active', true)
            ->first();
            
        if ($globalRule && isset($globalRule->params['markup_percent'])) {
            return (float) $globalRule->params['markup_percent'];
        }
        
        // Default markup
        return 25.0; // 25%
    }

    /**
     * Get delivery cost for city
     */
    protected function getDeliveryCost(string $city): float
    {
        return DeliveryBand::getPriceForCity($city);
    }

    /**
     * Get payment processing rate
     */
    protected function getPaymentRate(): float
    {
        $rule = PricingRule::where('rule_name', 'payment_fee')
            ->where('scope', 'global')
            ->where('active', true)
            ->first();
            
        if ($rule && isset($rule->params['rate_percent'])) {
            return (float) $rule->params['rate_percent'];
        }
        
        return 3.5; // 3.5% default
    }

    /**
     * Get miscellaneous duties
     */
    protected function getMiscDuty(float $baseUsd): float
    {
        // Simple duty calculation - could be more complex based on product category
        if ($baseUsd > 200) {
            return $baseUsd * 0.15; // 15% duty on expensive items
        } elseif ($baseUsd > 50) {
            return $baseUsd * 0.10; // 10% duty on mid-range items
        }
        
        return 5.0; // Flat $5 for cheap items
    }

    /**
     * Round up to nearest increment
     */
    protected function roundUp(float $value, float $increment): float
    {
        return ceil($value / $increment) * $increment;
    }

    /**
     * Static method for quick calculations
     */
    public static function for(?string $outlet = null, ?string $category = null): self
    {
        return new self();
    }

    /**
     * Fluent interface methods for the example in guide.txt
     */
    public function fx(float $priceValue, string $priceCurrency): self
    {
        $this->sourcePrice = $priceValue;
        $this->currency = $priceCurrency;
        return $this;
    }

    public function applyMarkup(): self
    {
        // This would be called in the fluent chain
        return $this;
    }

    public function addDelivery(string $city): self
    {
        $this->city = $city;
        return $this;
    }

    public function addPaymentFee(): self
    {
        // This would be called in the fluent chain
        return $this;
    }

    public function addDutiesIfAny(?string $category = null): self
    {
        $this->category = $category;
        return $this;
    }

    public function totalRounded(float $increment): float
    {
        $result = $this->calculate(
            $this->sourcePrice ?? 0,
            $this->currency ?? 'ZAR',
            null,
            $this->city ?? 'Harare'
        );
        
        return $this->roundUp($result['total_usd'], $increment);
    }

    // Properties for fluent interface
    protected ?float $sourcePrice = null;
    protected ?string $currency = null;
    protected ?string $city = null;
    protected ?string $category = null;
}
