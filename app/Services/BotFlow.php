<?php

namespace App\Services;

use App\Models\Customer;
use App\Models\IntakeItem;
use App\Models\Cart;
use App\Models\Order;
use App\Models\AuditLog;
use App\Services\WhatsAppService;
use App\Services\GeminiService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class BotFlow
{
    protected WhatsAppService $whatsAppService;
    protected GeminiService $geminiService;
    protected PricingEngine $pricingEngine;

    public function __construct(
        WhatsAppService $whatsAppService,
        GeminiService $geminiService,
        PricingEngine $pricingEngine
    ) {
        $this->whatsAppService = $whatsAppService;
        $this->geminiService = $geminiService;
        $this->pricingEngine = $pricingEngine;
    }

    public static function greet(Customer $customer): void
    {
        // Check if customer already has a name (returning customer)
        if (!empty($customer->first_name)) {
            $message = "Hi {$customer->first_name}! What would you like to do today? (Send product name, link, or photo).";
            self::setState($customer->id, 'INTENT');
        } else {
            // New customer - ask for name
            $message = "Hi! I'm You<PERSON> Africa 🤖. I help you get USD delivered pricing from SA shops. What's your name?";
            self::setState($customer->id, 'IDENTIFY');
        }

        app(WhatsAppService::class)->sendMessage($customer->phone_e164, $message);

        AuditLog::log(AuditLog::ACTOR_BOT, null, 'greeting_sent', [
            'customer_id' => $customer->id,
            'phone' => $customer->phone_e164,
            'has_name' => !empty($customer->first_name)
        ]);
    }

    public static function identify(Customer $customer, array $message): void
    {
        $text = trim($message['text'] ?? '');

        // If customer doesn't have a name yet, save it
        if (empty($customer->first_name) && !empty($text)) {
            $customer->update(['first_name' => $text]);
            $response = "Thanks, {$customer->first_name}. Send a product name, link, or photo (e.g., 'Defy 13-place dishwasher from Makro').";
            self::setState($customer->id, 'INTENT');

            app(WhatsAppService::class)->sendMessage($customer->phone_e164, $response);

            AuditLog::log(AuditLog::ACTOR_BOT, null, 'customer_identified', [
                'customer_id' => $customer->id,
                'name' => $customer->first_name
            ]);
        } else if (empty($customer->first_name)) {
            // Still waiting for name
            $response = "Please tell me your name so I can help you better.";
            app(WhatsAppService::class)->sendMessage($customer->phone_e164, $response);
        } else {
            // Customer already has name, move to intent
            $response = "Hi {$customer->first_name}! What would you like to do today? (Send product name, link, or photo).";
            self::setState($customer->id, 'INTENT');
            app(WhatsAppService::class)->sendMessage($customer->phone_e164, $response);
        }
    }

    public static function askForItem(Customer $customer, array $message): void
    {
        // This is the INTENT state - user has already been asked what they want to do
        // Now they've responded, so move to capture their item
        self::captureItem($customer, $message);
    }

    public static function captureItem(Customer $customer, array $message): void
    {
        $type = $message['type'] ?? 'text';

        // Extract media information based on message type
        $mediaId = null;
        $rawUrl = null;

        switch ($type) {
            case 'image':
                $mediaId = $message['image']['id'] ?? null;
                break;
            case 'audio':
                $mediaId = $message['audio']['id'] ?? null;
                break;
            case 'video':
                $mediaId = $message['video']['id'] ?? null;
                break;
            case 'document':
                $mediaId = $message['document']['id'] ?? null;
                break;
            case 'text':
                // Check if text contains a URL
                $text = $message['text'] ?? '';
                if (filter_var($text, FILTER_VALIDATE_URL)) {
                    $rawUrl = $text;
                }
                break;
        }

        // Create intake item
        $intakeItem = IntakeItem::create([
            'customer_id' => $customer->id,
            'type' => $type,
            'raw_text' => $message['text'] ?? null,
            'raw_url' => $rawUrl,
            'media_id' => $mediaId,
            'status' => IntakeItem::STATUS_PARSED
        ]);

        // Store current intake item
        self::setContext($customer->id, 'current_intake_item', $intakeItem->id);
        self::setState($customer->id, 'ENRICH_ITEM');

        // Process the item
        self::enrichItem($customer);
    }

    public static function enrichItem(Customer $customer): void
    {
        $intakeItemId = self::getContext($customer->id, 'current_intake_item');
        $intakeItem = IntakeItem::find($intakeItemId);
        
        if (!$intakeItem) {
            self::reset($customer);
            return;
        }
        
        try {
            switch ($intakeItem->type) {
                case IntakeItem::TYPE_TEXT:
                    self::enrichFromText($customer, $intakeItem);
                    break;
                case IntakeItem::TYPE_LINK:
                    self::enrichFromLink($customer, $intakeItem);
                    break;
                case IntakeItem::TYPE_IMAGE:
                    self::enrichFromImage($customer, $intakeItem);
                    break;
            }
        } catch (\Exception $e) {
            Log::error('Error enriching item', ['error' => $e->getMessage(), 'intake_item' => $intakeItem->id]);
            self::askForMissingInfo($customer, $intakeItem);
        }
    }

    protected static function enrichFromText(Customer $customer, IntakeItem $intakeItem): void
    {
        $text = $intakeItem->raw_text;
        
        // Use Gemini to extract product info
        $prompt = "Extract product information from: '{$text}'. Return JSON with: name, outlet, price_zar (number only), confidence (0-100)";
        $result = app(GeminiService::class)->generateText($prompt);
        
        if ($result['success']) {
            $data = json_decode($result['text'], true);
            if ($data) {
                $intakeItem->update([
                    'extracted_name' => $data['name'] ?? null,
                    'extracted_outlet' => $data['outlet'] ?? null,
                    'extracted_price' => $data['price_zar'] ?? null,
                    'confidence' => $data['confidence'] ?? 50,
                    'status' => self::determineItemStatus($data)
                ]);
            }
        }
        
        self::checkItemReadiness($customer, $intakeItem);
    }

    public static function downloadTwilioMedia(string $mediaUrl): ?string
    {
        try {
            // Check if this is a Twilio media URL or a regular URL
            $isTwilioUrl = str_contains($mediaUrl, 'api.twilio.com') || str_contains($mediaUrl, 'twilio.com');

            Log::info('Downloading media', [
                'url' => $mediaUrl,
                'is_twilio_url' => $isTwilioUrl
            ]);

            if ($isTwilioUrl) {
                // Twilio media URLs require authentication
                $twilioSid = config('services.twilio.sid');
                $twilioToken = config('services.twilio.token');

                if (!$twilioSid || !$twilioToken) {
                    Log::error('Twilio credentials not configured for media download');
                    return null;
                }

                $response = \Illuminate\Support\Facades\Http::withBasicAuth($twilioSid, $twilioToken)
                    ->timeout(30)
                    ->get($mediaUrl);
            } else {
                // Regular URL - download without authentication
                Log::info('Downloading as regular URL (not Twilio media)');
                $response = \Illuminate\Support\Facades\Http::timeout(30)->get($mediaUrl);
            }

            if ($response->successful()) {
                Log::info('Media downloaded successfully', ['size' => strlen($response->body()) . ' bytes']);
                return $response->body();
            }

            Log::error('Failed to download media', [
                'url' => $mediaUrl,
                'status' => $response->status(),
                'error' => $response->body()
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error('Exception downloading media', [
                'url' => $mediaUrl,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    protected static function enrichFromLink(Customer $customer, IntakeItem $intakeItem): void
    {
        // For now, ask user for details
        $response = "I see you sent a link. What's the product name and price you see?";
        app(WhatsAppService::class)->sendMessage($customer->phone_e164, $response);
        
        $intakeItem->update(['status' => IntakeItem::STATUS_NEEDS_PRICE]);
    }

    protected static function enrichFromImage(Customer $customer, IntakeItem $intakeItem): void
    {
        try {
            // Download image from Twilio media URL
            $mediaUrl = $intakeItem->media_id; // This contains the Twilio media URL

            Log::info('Starting image analysis', [
                'customer_id' => $customer->id,
                'intake_item_id' => $intakeItem->id,
                'media_url' => $mediaUrl
            ]);

            if (!$mediaUrl) {
                throw new \Exception('No media URL found');
            }

            // Download the image
            Log::info('Downloading image from Twilio', ['url' => $mediaUrl]);
            $imageData = self::downloadTwilioMedia($mediaUrl);
            if (!$imageData) {
                // In development mode, try to use a sample image for testing
                if (app()->environment(['local', 'development']) && file_exists(public_path('uploads/temp/sample_product.jpg'))) {
                    Log::info('Using sample image for development testing');
                    $imageData = file_get_contents(public_path('uploads/temp/sample_product.jpg'));
                }

                if (!$imageData) {
                    throw new \Exception('Failed to download image from Twilio');
                }
            }

            Log::info('Image downloaded successfully', ['size' => strlen($imageData) . ' bytes']);

            // Save temporarily for Gemini analysis
            $tempPath = 'uploads/temp/image_' . time() . '_' . uniqid() . '.jpg';
            $fullPath = public_path($tempPath);

            // Ensure directory exists
            $tempDir = dirname($fullPath);
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
                Log::info('Created temp directory', ['path' => $tempDir]);
            }

            file_put_contents($fullPath, $imageData);
            Log::info('Image saved temporarily', ['path' => $tempPath, 'file_exists' => file_exists($fullPath)]);

            // Analyze with Gemini
            Log::info('Starting Gemini analysis', ['image_path' => $tempPath]);
            $geminiResult = app(\App\Services\GeminiService::class)->analyzeProductImage($tempPath);
            Log::info('Gemini analysis completed', ['success' => $geminiResult['success'] ?? false]);

            // Clean up temporary file
            if (file_exists($fullPath)) {
                unlink($fullPath);
                Log::info('Temporary file cleaned up', ['path' => $tempPath]);
            }

            if ($geminiResult['success'] && !empty($geminiResult['products'])) {
                // Extract the first product found
                $product = $geminiResult['products'][0];

                $intakeItem->update([
                    'extracted_name' => $product['name'] ?? null,
                    'extracted_outlet' => $product['outlet'] ?? null,
                    'extracted_price' => $product['price_zar'] ?? null,
                    'confidence' => $product['confidence'] ?? 70,
                    'ai_analysis' => json_encode($geminiResult),
                    'status' => self::determineItemStatus($product)
                ]);

                Log::info('Gemini image analysis successful', [
                    'customer_id' => $customer->id,
                    'intake_item_id' => $intakeItem->id,
                    'product_name' => $product['name'] ?? 'unknown',
                    'confidence' => $product['confidence'] ?? 0,
                    'products_found' => count($geminiResult['products'])
                ]);

                // Send confirmation message based on what was found
                $confirmationMessage = self::generateImageAnalysisResponse($product);
                app(WhatsAppService::class)->sendMessage($customer->phone_e164, $confirmationMessage);

                self::checkItemReadiness($customer, $intakeItem);
                return;
            }

            // If Gemini failed or found no products, fall back to manual input
            Log::warning('Gemini analysis returned no products', [
                'customer_id' => $customer->id,
                'intake_item_id' => $intakeItem->id,
                'gemini_success' => $geminiResult['success'] ?? false,
                'gemini_error' => $geminiResult['error'] ?? null,
                'raw_text' => $geminiResult['raw_text'] ?? null,
                'products_count' => count($geminiResult['products'] ?? [])
            ]);
            throw new \Exception('Gemini analysis failed or no products found: ' . ($geminiResult['error'] ?? 'No products detected'));

        } catch (\Exception $e) {
            Log::error('Image analysis failed completely', [
                'customer_id' => $customer->id,
                'intake_item_id' => $intakeItem->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'media_url' => $intakeItem->media_id
            ]);

            // Provide more specific error messages based on the failure type
            $errorMessage = $e->getMessage();
            if (str_contains($errorMessage, 'timeout') || str_contains($errorMessage, 'cURL error 28')) {
                $response = "I can see your image, but I'm having network issues downloading it. Please try sending it again, or tell me: What product is this and what's the price you see?";
            } elseif (str_contains($errorMessage, 'Failed to download')) {
                $response = "I can see your image, but I'm having trouble accessing it. Please try sending it again, or tell me: What product is this and what's the price you see?";
            } else {
                $response = "I can see you sent an image, but I'm having trouble analyzing it. What product is this and what's the price you see?";
            }

            app(WhatsAppService::class)->sendMessage($customer->phone_e164, $response);
            $intakeItem->update(['status' => IntakeItem::STATUS_NEEDS_PRICE]);
        }
    }

    protected static function determineItemStatus(array $data): string
    {
        if (empty($data['name'])) return IntakeItem::STATUS_NEEDS_PRICE;
        if (empty($data['outlet'])) return IntakeItem::STATUS_NEEDS_OUTLET;
        if (empty($data['price_zar']) || $data['price_zar'] <= 0) return IntakeItem::STATUS_NEEDS_PRICE;
        
        return IntakeItem::STATUS_READY;
    }

    protected static function checkItemReadiness(Customer $customer, IntakeItem $intakeItem): void
    {
        if ($intakeItem->isReady()) {
            self::setState($customer->id, 'PRICE');
            self::price($customer);
        } else {
            self::askForMissingInfo($customer, $intakeItem);
        }
    }

    protected static function askForMissingInfo(Customer $customer, IntakeItem $intakeItem): void
    {
        if ($intakeItem->needsPrice()) {
            $response = "I couldn't see a price on that. What's the price you see (e.g., R3499 or ZAR 3499)?";
        } elseif ($intakeItem->needsOutlet()) {
            $response = "Which shop is this from? (Makro, Game, Takealot, etc.)";
        } else {
            $response = "I need more details. Can you tell me the product name and price?";
        }
        
        app(WhatsAppService::class)->sendMessage($customer->phone_e164, $response);
    }

    public static function price(Customer $customer): void
    {
        $intakeItemId = self::getContext($customer->id, 'current_intake_item');
        $intakeItem = IntakeItem::find($intakeItemId);
        
        if (!$intakeItem || !$intakeItem->isReady()) {
            self::reset($customer);
            return;
        }
        
        try {
            $pricing = app(PricingEngine::class)->calculate(
                $intakeItem->extracted_price,
                $intakeItem->currency,
                $intakeItem->extracted_outlet
            );
            
            $response = "Delivered to Harare: $" . number_format($pricing['total_usd'], 2) . 
                       " (FX {$pricing['fx_rate']}, markup {$pricing['markup_percent']}%, delivery $" . 
                       number_format($pricing['delivery'], 2) . "). Add to cart or order now?";
            
            // Store pricing for next step
            self::setContext($customer->id, 'current_pricing', $pricing);
            self::setState($customer->id, 'CART_OR_ORDER');
            
        } catch (\Exception $e) {
            Log::error('Pricing calculation failed', ['error' => $e->getMessage()]);
            $response = "I'm having trouble calculating the price. Let me connect you with our team.";
        }
        
        app(WhatsAppService::class)->sendMessage($customer->phone_e164, $response);
    }

    public static function cartOrOrder(Customer $customer, array $message): void
    {
        $text = strtolower($message['text'] ?? '');
        
        if (str_contains($text, 'cart')) {
            self::addToCart($customer);
        } elseif (str_contains($text, 'order')) {
            self::setState($customer->id, 'ORDER_CONFIRM');
            self::confirmOrder($customer, $message);
        } else {
            $response = "Would you like to add to cart or order now?";
            app(WhatsAppService::class)->sendMessage($customer->phone_e164, $response);
        }
    }

    protected static function addToCart(Customer $customer): void
    {
        // Implementation for adding to cart
        $response = "Added to cart! Send another product or say 'checkout'.";
        app(WhatsAppService::class)->sendMessage($customer->phone_e164, $response);
        self::setState($customer->id, 'INTENT');
    }

    public static function confirmOrder(Customer $customer, array $message): void
    {
        $text = strtolower($message['text'] ?? '');
        
        if (str_contains($text, 'confirm') || str_contains($text, 'yes')) {
            // Create order
            $reference = 'YA-' . strtoupper(substr(md5(time() . $customer->id), 0, 4));
            
            $order = Order::create([
                'customer_id' => $customer->id,
                'reference' => $reference,
                'status' => Order::STATUS_NEW,
                'total_usd' => self::getContext($customer->id, 'current_pricing')['total_usd'] ?? 0,
                'city' => 'Harare' // Default for now
            ]);
            
            $response = "🎉 Order placed! Ref #{$reference}. We'll follow up shortly.";
            self::setState($customer->id, 'DONE');
            
            AuditLog::log(AuditLog::ACTOR_BOT, null, 'order_created', [
                'customer_id' => $customer->id,
                'order_id' => $order->id,
                'reference' => $reference
            ]);
        } else {
            $intakeItemId = self::getContext($customer->id, 'current_intake_item');
            $intakeItem = IntakeItem::find($intakeItemId);
            $pricing = self::getContext($customer->id, 'current_pricing');
            
            $response = "Order: 1× {$intakeItem->extracted_name} ({$intakeItem->extracted_outlet}, R" . 
                       number_format($intakeItem->extracted_price, 2) . ") → $" . 
                       number_format($pricing['total_usd'], 2) . ". Deliver to Harare. Confirm?";
        }
        
        app(WhatsAppService::class)->sendMessage($customer->phone_e164, $response);
    }

    public static function reset(Customer $customer): void
    {
        self::setState($customer->id, 'GREET');
        self::greet($customer);
    }

    // State management helpers
    protected static function setState(int $customerId, string $state): void
    {
        Cache::put("bot_state_{$customerId}", $state, now()->addHours(24));
    }

    public static function getState(int $customerId): string
    {
        return Cache::get("bot_state_{$customerId}", 'GREET');
    }

    protected static function setContext(int $customerId, string $key, $value): void
    {
        Cache::put("bot_context_{$customerId}_{$key}", $value, now()->addHours(24));
    }

    protected static function getContext(int $customerId, string $key)
    {
        return Cache::get("bot_context_{$customerId}_{$key}");
    }

    /**
     * Generate appropriate response based on image analysis results
     */
    protected static function generateImageAnalysisResponse(array $product): string
    {
        $name = $product['name'] ?? 'this product';
        $hasPrice = !empty($product['price_zar']) && $product['price_zar'] > 0;
        $hasOutlet = !empty($product['outlet']);

        if ($hasPrice && $hasOutlet) {
            return "Great! I found: *{$name}* from *{$product['outlet']}* for *R" .
                   number_format($product['price_zar'], 2) . "*. Let me calculate the USD delivered price for you! 💰";
        }

        if ($hasPrice && !$hasOutlet) {
            return "I can see: *{$name}* for *R" . number_format($product['price_zar'], 2) .
                   "*. Which shop is this from? (Makro, Game, Takealot, etc.)";
        }

        if (!$hasPrice && $hasOutlet) {
            return "I can see: *{$name}* from *{$product['outlet']}*. What's the price you see?";
        }

        // No price or outlet found
        return "I can see: *{$name}*. Please tell me:\n1. What's the price you see?\n2. Which shop is this from?";
    }
}
