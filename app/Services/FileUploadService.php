<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;

class FileUploadService
{
    /**
     * Upload file to public folder
     */
    public static function uploadToPublic(UploadedFile $file, string $directory, ?string $filename = null): string
    {
        // Ensure directory exists
        $publicPath = public_path($directory);
        if (!file_exists($publicPath)) {
            mkdir($publicPath, 0755, true);
        }

        // Generate filename if not provided
        if (!$filename) {
            $filename = Str::random(20) . '.' . $file->getClientOriginalExtension();
        }

        // Move file to public directory
        $file->move($publicPath, $filename);

        // Return relative path from public folder
        return $directory . '/' . $filename;
    }

    /**
     * Delete file from public folder
     */
    public static function deleteFromPublic(string $path): bool
    {
        $fullPath = public_path($path);
        
        if (file_exists($fullPath)) {
            return unlink($fullPath);
        }
        
        return false;
    }

    /**
     * Get URL for public file
     */
    public static function getPublicUrl(string $path): string
    {
        // Remove leading slash if present
        $path = ltrim($path, '/');
        
        // Return URL relative to public folder
        return asset($path);
    }

    /**
     * Check if file exists in public folder
     */
    public static function existsInPublic(string $path): bool
    {
        return file_exists(public_path($path));
    }

    /**
     * Get file size from public folder
     */
    public static function getFileSize(string $path): int
    {
        $fullPath = public_path($path);
        return file_exists($fullPath) ? filesize($fullPath) : 0;
    }

    /**
     * Copy file within public folder
     */
    public static function copyInPublic(string $from, string $to): bool
    {
        $fromPath = public_path($from);
        $toPath = public_path($to);
        
        // Ensure destination directory exists
        $toDir = dirname($toPath);
        if (!file_exists($toDir)) {
            mkdir($toDir, 0755, true);
        }
        
        return copy($fromPath, $toPath);
    }

    /**
     * Move file within public folder
     */
    public static function moveInPublic(string $from, string $to): bool
    {
        $fromPath = public_path($from);
        $toPath = public_path($to);
        
        // Ensure destination directory exists
        $toDir = dirname($toPath);
        if (!file_exists($toDir)) {
            mkdir($toDir, 0755, true);
        }
        
        return rename($fromPath, $toPath);
    }

    /**
     * Create directory structure for uploads
     */
    public static function ensureUploadDirectories(): void
    {
        $directories = [
            'uploads',
            'uploads/catalogues',
            'uploads/catalogues/pdf',
            'uploads/catalogues/images',
            'uploads/catalogues/covers',
            'uploads/shops',
            'uploads/shops/logos',
            'uploads/products',
            'uploads/temp',
            'uploads/chat',
        ];

        foreach ($directories as $dir) {
            $path = public_path($dir);
            if (!file_exists($path)) {
                mkdir($path, 0755, true);
            }
        }
    }
}