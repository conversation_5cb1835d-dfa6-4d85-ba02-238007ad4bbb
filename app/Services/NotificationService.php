<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\Order;
use App\Models\Lead;
use App\Models\Customer;
use App\Services\WhatsAppService;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    protected $whatsAppService;

    public function __construct(WhatsAppService $whatsAppService)
    {
        $this->whatsAppService = $whatsAppService;
    }

    public function sendOrderConfirmation(Order $order): void
    {
        $customer = $order->customer;
        
        $message = "🎉 *Order Confirmed!*\n\n" .
                  "Order #: {$order->id}\n" .
                  "Total: \${$order->total_usd}\n" .
                  "Items: " . $order->items->count() . "\n\n" .
                  "We'll keep you updated on your order progress.\n" .
                  "Questions? Reply 'agent' anytime.";

        $this->scheduleNotification($customer, 'order_confirmed', 'whatsapp', 
            'Order Confirmed', $message, ['order_id' => $order->id]);
    }

    public function sendOrderShipped(Order $order, string $trackingNumber): void
    {
        $customer = $order->customer;
        
        $message = "📦 *Your Order Has Shipped!*\n\n" .
                  "Order #: {$order->id}\n" .
                  "Tracking #: {$trackingNumber}\n\n" .
                  "Your package is on its way to Zimbabwe!\n" .
                  "Expected delivery: " . now()->addDays(7)->format('M j') . "\n\n" .
                  "Track your order: https://youzeafrika.com/track/{$trackingNumber}";

        $this->scheduleNotification($customer, 'order_shipped', 'whatsapp',
            'Order Shipped', $message, ['order_id' => $order->id, 'tracking_number' => $trackingNumber]);
    }

    public function sendDeliveryConfirmation(Order $order): void
    {
        $customer = $order->customer;
        
        $message = "✅ *Package Delivered!*\n\n" .
                  "Order #: {$order->id} has been delivered!\n\n" .
                  "🌟 *How was your experience?*\n" .
                  "Reply with:\n" .
                  "• 'feedback' to leave a review\n" .
                  "• 'reorder' to buy again\n" .
                  "• 'agent' for support\n\n" .
                  "Thank you for choosing Youzeafrika! 🇿🇼❤️🇿🇦";

        $this->scheduleNotification($customer, 'order_delivered', 'whatsapp',
            'Order Delivered', $message, ['order_id' => $order->id]);
    }

    public function sendLeadFollowUp(Lead $lead, string $message): void
    {
        if ($lead->phone) {
            $this->scheduleNotification($lead, 'lead_followup', 'whatsapp',
                'Follow Up', $message, ['lead_id' => $lead->id]);
        }
    }

    public function sendInquiryResponse(Lead $lead, string $response): void
    {
        if ($lead->phone) {
            $this->scheduleNotification($lead, 'inquiry_response', 'whatsapp',
                'Inquiry Response', $response, ['lead_id' => $lead->id]);
        }
    }

    public function sendPriceAlert(Lead $lead, array $products): void
    {
        $message = "💰 *Price Alert!*\n\n" .
                  "Good news! Prices have dropped on products you showed interest in:\n\n";
        
        foreach ($products as $product) {
            $message .= "🏷️ *{$product['name']}*\n";
            $message .= "~~\${$product['old_price']}~~ → \${$product['new_price']}\n";
            $message .= "Save \${$product['savings']}!\n\n";
        }
        
        $message .= "Limited time offer. Reply 'buy now' to order!";

        $this->scheduleNotification($lead, 'price_alert', 'whatsapp',
            'Price Drop Alert', $message, ['products' => $products]);
    }

    public function processScheduledNotifications(): void
    {
        $notifications = Notification::where('status', 'pending')
            ->where('scheduled_at', '<=', now())
            ->limit(50)
            ->get();

        foreach ($notifications as $notification) {
            try {
                $this->sendNotification($notification);
            } catch (\Exception $e) {
                Log::error('Failed to send notification', [
                    'notification_id' => $notification->id,
                    'error' => $e->getMessage()
                ]);
                
                $notification->update([
                    'status' => 'failed',
                    'failure_reason' => $e->getMessage()
                ]);
            }
        }
    }

    private function scheduleNotification(
        $notifiable, 
        string $type, 
        string $channel, 
        string $title, 
        string $message, 
        array $data = [],
        ?\Carbon\Carbon $scheduledAt = null
    ): Notification {
        return Notification::create([
            'notifiable_type' => get_class($notifiable),
            'notifiable_id' => $notifiable->id,
            'type' => $type,
            'channel' => $channel,
            'title' => $title,
            'message' => $message,
            'data' => $data,
            'scheduled_at' => $scheduledAt ?: now(),
        ]);
    }

    private function sendNotification(Notification $notification): void
    {
        $notifiable = $notification->notifiable;
        
        switch ($notification->channel) {
            case 'whatsapp':
                $this->sendWhatsAppNotification($notifiable, $notification);
                break;
            case 'email':
                $this->sendEmailNotification($notifiable, $notification);
                break;
            case 'sms':
                $this->sendSMSNotification($notifiable, $notification);
                break;
        }

        $notification->update([
            'status' => 'sent',
            'sent_at' => now()
        ]);
    }

    private function sendWhatsAppNotification($notifiable, Notification $notification): void
    {
        $phone = $notifiable->phone ?? $notifiable->wa_number;
        
        if (!$phone) {
            throw new \Exception('No phone number available for WhatsApp notification');
        }

        $success = $this->whatsAppService->sendMessage($phone, $notification->message);
        
        if (!$success) {
            throw new \Exception('WhatsApp message delivery failed');
        }
    }

    private function sendEmailNotification($notifiable, Notification $notification): void
    {
        if (!$notifiable->email) {
            throw new \Exception('No email address available');
        }

        // Email sending logic would go here
        // Mail::to($notifiable->email)->send(new OrderNotification($notification));
        
        Log::info('Email notification would be sent', [
            'to' => $notifiable->email,
            'subject' => $notification->title
        ]);
    }

    private function sendSMSNotification($notifiable, Notification $notification): void
    {
        // SMS service integration would go here
        Log::info('SMS notification would be sent', [
            'to' => $notifiable->phone,
            'message' => $notification->message
        ]);
    }
}