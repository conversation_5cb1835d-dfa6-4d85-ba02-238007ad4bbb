<?php

namespace App\Services\Pricing;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ExchangeRateService
{
    private string $apiUrl;
    private ?string $apiKey;

    public function __construct()
    {
        $this->apiUrl = config('services.exchange_rate.api_url', 'https://api.exchangerate-api.com/v4/latest');
        $this->apiKey = config('services.exchange_rate.api_key');
    }

    public function getRate(string $from, string $to): float
    {
        $cacheKey = "exchange_rate_{$from}_to_{$to}";

        return Cache::remember($cacheKey, 3600, function () use ($from, $to) {
            return $this->fetchRate($from, $to);
        });
    }

    private function fetchRate(string $from, string $to): float
    {
        try {
            // Try primary API
            $rate = $this->fetchFromPrimaryApi($from, $to);
            if ($rate) {
                return $rate;
            }

            // Fallback to backup API
            $rate = $this->fetchFromBackupApi($from, $to);
            if ($rate) {
                return $rate;
            }

            // Use hardcoded fallback rates
            return $this->getFallbackRate($from, $to);

        } catch (\Exception $e) {
            Log::error('Exchange rate fetch failed', [
                'from' => $from,
                'to' => $to,
                'error' => $e->getMessage()
            ]);

            return $this->getFallbackRate($from, $to);
        }
    }

    private function fetchFromPrimaryApi(string $from, string $to): ?float
    {
        try {
            $url = $this->apiKey
                ? "{$this->apiUrl}/{$from}?access_key={$this->apiKey}"
                : "{$this->apiUrl}/{$from}";

            $response = Http::timeout(10)->get($url);

            if ($response->successful()) {
                $data = $response->json();
                return $data['rates'][$to] ?? null;
            }
        } catch (\Exception $e) {
            Log::warning('Primary exchange rate API failed', ['error' => $e->getMessage()]);
        }

        return null;
    }

    private function fetchFromBackupApi(string $from, string $to): ?float
    {
        try {
            // Use a free backup service
            $response = Http::timeout(10)->get("https://api.fixer.io/latest", [
                'base' => $from,
                'symbols' => $to
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['rates'][$to] ?? null;
            }
        } catch (\Exception $e) {
            Log::warning('Backup exchange rate API failed', ['error' => $e->getMessage()]);
        }

        return null;
    }

    private function getFallbackRate(string $from, string $to): float
    {
        // Hardcoded rates as of a recent date (should be updated periodically)
        $rates = [
            'ZAR_USD' => 0.055, // 1 ZAR = 0.055 USD
            'USD_ZAR' => 18.18, // 1 USD = 18.18 ZAR
            'ZAR_EUR' => 0.050, // 1 ZAR = 0.050 EUR
            'EUR_ZAR' => 20.00, // 1 EUR = 20.00 ZAR
            'USD_EUR' => 0.91,  // 1 USD = 0.91 EUR
            'EUR_USD' => 1.10,  // 1 EUR = 1.10 USD
        ];

        $rateKey = "{$from}_{$to}";

        if (isset($rates[$rateKey])) {
            Log::info('Using fallback exchange rate', [
                'from' => $from,
                'to' => $to,
                'rate' => $rates[$rateKey]
            ]);

            return $rates[$rateKey];
        }

        // If specific rate not found, try to calculate via USD
        if ($from !== 'USD' && $to !== 'USD') {
            $fromToUsd = $this->getFallbackRate($from, 'USD');
            $usdToTarget = $this->getFallbackRate('USD', $to);
            return $fromToUsd * $usdToTarget;
        }

        // Last resort - return 1 (no conversion)
        Log::warning('No exchange rate available, returning 1', [
            'from' => $from,
            'to' => $to
        ]);

        return 1.0;
    }

    public function getAllRates(string $baseCurrency = 'USD'): array
    {
        $cacheKey = "all_rates_{$baseCurrency}";

        return Cache::remember($cacheKey, 3600, function () use ($baseCurrency) {
            try {
                $url = $this->apiKey
                    ? "{$this->apiUrl}/{$baseCurrency}?access_key={$this->apiKey}"
                    : "{$this->apiUrl}/{$baseCurrency}";

                $response = Http::timeout(10)->get($url);

                if ($response->successful()) {
                    $data = $response->json();
                    return $data['rates'] ?? [];
                }
            } catch (\Exception $e) {
                Log::error('Failed to fetch all exchange rates', ['error' => $e->getMessage()]);
            }

            return [];
        });
    }

    public function convertAmount(float $amount, string $from, string $to): float
    {
        $rate = $this->getRate($from, $to);
        return $amount * $rate;
    }

    public function getHistoricalRate(string $from, string $to, string $date): ?float
    {
        // This would require a different API endpoint for historical data
        // For now, return current rate as fallback
        return $this->getRate($from, $to);
    }

    public function isRateStale(string $from, string $to, int $maxAgeMinutes = 60): bool
    {
        $cacheKey = "exchange_rate_{$from}_to_{$to}";
        $cachedAt = Cache::get($cacheKey . '_timestamp');

        if (!$cachedAt) {
            return true;
        }

        return now()->diffInMinutes($cachedAt) > $maxAgeMinutes;
    }

    public function refreshRate(string $from, string $to): float
    {
        $cacheKey = "exchange_rate_{$from}_to_{$to}";
        Cache::forget($cacheKey);

        return $this->getRate($from, $to);
    }

    public function getMarketInsights(): array
    {
        $zarUsd = $this->getRate('ZAR', 'USD');
        $usdZar = $this->getRate('USD', 'ZAR');

        // Get historical comparison (mock data for now)
        $insights = [
            'current_zar_usd' => $zarUsd,
            'current_usd_zar' => $usdZar,
            'trend' => $zarUsd > 0.056 ? 'strengthening' : 'weakening',
            'recommendation' => $zarUsd > 0.056 ? 'Good time to import' : 'Consider waiting',
            'volatility' => 'moderate',
            'last_updated' => now()->toISOString()
        ];

        return $insights;
    }
}