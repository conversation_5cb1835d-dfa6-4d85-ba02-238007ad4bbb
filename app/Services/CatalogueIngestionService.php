<?php

namespace App\Services;

use App\Models\Catalogue;
use App\Models\CatalogueIngestion;
use App\Models\ExtractedProduct;
use App\Models\Product;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CatalogueIngestionService
{
    /**
     * Gemini AI API configuration
     */
    protected $geminiApiKey;
    protected $geminiModel = 'gemini-1.5-flash';
    protected $maxTokens = 4096;

    public function __construct()
    {
        $this->geminiApiKey = config('services.gemini.api_key');
    }

    /**
     * Start a new catalogue ingestion process
     */
    public function startIngestion(Catalogue $catalogue, int $userId): CatalogueIngestion
    {
        $ingestion = CatalogueIngestion::create([
            'catalogue_id' => $catalogue->id,
            'user_id' => $userId,
            'status' => CatalogueIngestion::STATUS_PENDING,
            'total_pages' => $catalogue->page_count ?? 1,
            'ai_config' => [
                'model' => $this->geminiModel,
                'max_tokens' => $this->maxTokens,
                'provider' => 'gemini',
                'timestamp' => now()->toIso8601String(),
            ],
        ]);

        return $ingestion;
    }

    /**
     * Process a catalogue for product extraction
     */
    public function processCatalogue(CatalogueIngestion $ingestion): void
    {
        try {
            $ingestion->startProcessing();
            $catalogue = $ingestion->catalogue;

            if ($catalogue->type === 'pdf') {
                $this->processPdfCatalogue($ingestion, $catalogue);
            } else {
                $this->processImageCatalogue($ingestion, $catalogue);
            }

            // Update product counts
            $ingestion->updateProductCounts();
            
            // Mark as ready for review
            $ingestion->markForReview();

        } catch (\Exception $e) {
            Log::error('Catalogue ingestion failed', [
                'ingestion_id' => $ingestion->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $ingestion->markAsFailed($e->getMessage());
        }
    }

    /**
     * Process PDF catalogue
     */
    protected function processPdfCatalogue(CatalogueIngestion $ingestion, Catalogue $catalogue): void
    {
        // For PDF catalogues, we'll need to:
        // 1. Convert PDF pages to images (using Imagick or similar)
        // 2. Process each page image with AI
        // For now, we'll implement a simplified version
        
        $pdfUrl = $catalogue->pdf_url;
        $this->extractProductsFromContent($ingestion, $pdfUrl, 'pdf');
    }

    /**
     * Process image catalogue
     */
    protected function processImageCatalogue(CatalogueIngestion $ingestion, Catalogue $catalogue): void
    {
        $images = $catalogue->images ?? [];
        
        foreach ($images as $index => $imageUrl) {
            $pageNumber = $index + 1;
            
            // Update progress
            $ingestion->update(['processed_pages' => $pageNumber]);
            
            // Extract products from this page
            $this->extractProductsFromImage($ingestion, $imageUrl, $pageNumber);
        }
    }

    /**
     * Extract products from a single image using AI
     */
    protected function extractProductsFromImage(CatalogueIngestion $ingestion, string $imageUrl, int $pageNumber): void
    {
        try {
            // Prepare the prompt for product extraction
            $prompt = $this->buildExtractionPrompt();
            
            // Call Gemini Vision API
            $response = $this->callGeminiAPI($imageUrl, $prompt);
            
            if ($response && isset($response['products'])) {
                $this->saveExtractedProducts($ingestion, $response['products'], $pageNumber);
            }

        } catch (\Exception $e) {
            Log::error('Failed to extract products from image', [
                'ingestion_id' => $ingestion->id,
                'page' => $pageNumber,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Build the extraction prompt for AI
     */
    protected function buildExtractionPrompt(): string
    {
        return "Analyze this catalog page and extract all products. For each product, identify:

1. Product name
2. Description (if available)
3. SKU or product code
4. Brand name
5. Model number
6. Price (with currency)
7. Unit (piece, box, pack, etc.)
8. Specifications (technical details)
9. Features (key selling points)
10. Category (best guess based on product type)

Return the data as a JSON array with the following structure:
{
    \"products\": [
        {
            \"name\": \"Product Name\",
            \"description\": \"Product description\",
            \"sku\": \"SKU123\",
            \"brand\": \"Brand Name\",
            \"model\": \"Model XYZ\",
            \"price\": 99.99,
            \"currency\": \"USD\",
            \"unit\": \"piece\",
            \"specifications\": {
                \"key\": \"value\"
            },
            \"features\": [\"feature1\", \"feature2\"],
            \"category\": \"Category Name\",
            \"confidence\": 0.95,
            \"bounding_box\": {
                \"x\": 0,
                \"y\": 0,
                \"width\": 100,
                \"height\": 100
            }
        }
    ]
}

Be thorough but only extract actual products, not headers, footers, or promotional content.";
    }

    /**
     * Call Gemini Vision API
     */
    protected function callGeminiAPI(string $imageUrl, string $prompt): ?array
    {
        if (!$this->geminiApiKey) {
            // Fallback to mock data for testing
            return $this->getMockExtractionData();
        }

        try {
            // Download image and convert to base64
            $imageData = Http::get($imageUrl);
            if (!$imageData->successful()) {
                Log::error('Failed to download image for Gemini API', ['url' => $imageUrl]);
                return $this->getMockExtractionData();
            }

            $base64Image = base64_encode($imageData->body());
            $mimeType = $imageData->header('Content-Type') ?? 'image/jpeg';

            // Call Gemini API
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post("https://generativelanguage.googleapis.com/v1beta/models/{$this->geminiModel}:generateContent?key={$this->geminiApiKey}", [
                'contents' => [
                    [
                        'parts' => [
                            [
                                'text' => $prompt
                            ],
                            [
                                'inline_data' => [
                                    'mime_type' => $mimeType,
                                    'data' => $base64Image
                                ]
                            ]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'temperature' => 0.2,
                    'maxOutputTokens' => $this->maxTokens,
                ]
            ]);

            if ($response->successful()) {
                $responseData = $response->json();
                $content = $responseData['candidates'][0]['content']['parts'][0]['text'] ?? '';
                
                // Parse JSON from response
                if (preg_match('/\{.*\}/s', $content, $matches)) {
                    return json_decode($matches[0], true);
                }
                
                Log::info('Gemini API response content', ['content' => $content]);
            } else {
                Log::error('Gemini API call failed', [
                    'status' => $response->status(),
                    'response' => $response->json(),
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Gemini API call failed', [
                'error' => $e->getMessage(),
            ]);
        }

        // Fallback to mock data if API fails
        return $this->getMockExtractionData();
    }

    /**
     * Get mock extraction data for testing
     */
    protected function getMockExtractionData(): array
    {
        return [
            'products' => [
                [
                    'name' => 'Sample Product ' . Str::random(5),
                    'description' => 'This is a sample product extracted from the catalogue',
                    'sku' => 'SKU-' . rand(10000, 99999),
                    'brand' => 'Sample Brand',
                    'model' => 'Model-' . rand(100, 999),
                    'price' => rand(10, 1000) + (rand(0, 99) / 100),
                    'currency' => 'USD',
                    'unit' => 'piece',
                    'specifications' => [
                        'weight' => '1.5 kg',
                        'dimensions' => '30x20x10 cm',
                        'color' => 'Black',
                    ],
                    'features' => [
                        'High quality material',
                        'Durable construction',
                        '1 year warranty',
                    ],
                    'category' => 'Electronics',
                    'confidence' => rand(70, 99) / 100,
                    'bounding_box' => [
                        'x' => rand(0, 500),
                        'y' => rand(0, 500),
                        'width' => rand(100, 300),
                        'height' => rand(100, 300),
                    ],
                ],
            ],
        ];
    }

    /**
     * Save extracted products to database
     */
    protected function saveExtractedProducts(CatalogueIngestion $ingestion, array $products, int $pageNumber): void
    {
        foreach ($products as $productData) {
            ExtractedProduct::create([
                'ingestion_id' => $ingestion->id,
                'catalogue_id' => $ingestion->catalogue_id,
                'shop_id' => $ingestion->catalogue->shop_id,
                'extracted_name' => $productData['name'] ?? 'Unknown Product',
                'extracted_description' => $productData['description'] ?? null,
                'extracted_sku' => $productData['sku'] ?? null,
                'extracted_brand' => $productData['brand'] ?? null,
                'extracted_model' => $productData['model'] ?? null,
                'extracted_price' => $productData['price'] ?? null,
                'extracted_currency' => $productData['currency'] ?? 'USD',
                'extracted_unit' => $productData['unit'] ?? 'piece',
                'extracted_specifications' => $productData['specifications'] ?? [],
                'extracted_features' => $productData['features'] ?? [],
                'extracted_category' => $productData['category'] ?? null,
                'page_number' => $pageNumber,
                'bounding_box' => $productData['bounding_box'] ?? null,
                'confidence_score' => ($productData['confidence'] ?? 0.5) * 100,
                'ai_metadata' => [
                    'extraction_timestamp' => now()->toIso8601String(),
                    'model_used' => $this->geminiModel,
                    'provider' => 'gemini',
                ],
            ]);
        }

        // Update product count
        $ingestion->increment('products_found', count($products));
    }

    /**
     * Find potential duplicates for an extracted product
     */
    public function findDuplicates(ExtractedProduct $extractedProduct): array
    {
        $query = Product::where('shop_id', $extractedProduct->shop_id);

        // Match by SKU if available
        if ($extractedProduct->extracted_sku) {
            $skuMatches = (clone $query)
                ->where('sku', $extractedProduct->extracted_sku)
                ->get();
            
            if ($skuMatches->isNotEmpty()) {
                return $skuMatches->toArray();
            }
        }

        // Match by similar name
        if ($extractedProduct->extracted_name) {
            $nameMatches = (clone $query)
                ->where('name', 'LIKE', '%' . $extractedProduct->extracted_name . '%')
                ->limit(5)
                ->get();
            
            if ($nameMatches->isNotEmpty()) {
                return $nameMatches->toArray();
            }
        }

        return [];
    }

    /**
     * Import approved products
     */
    public function importApprovedProducts(CatalogueIngestion $ingestion): int
    {
        $imported = 0;

        DB::transaction(function () use ($ingestion, &$imported) {
            $approvedProducts = $ingestion->extractedProducts()
                ->where('review_status', ExtractedProduct::STATUS_APPROVED)
                ->whereNull('imported_product_id')
                ->get();

            foreach ($approvedProducts as $extractedProduct) {
                if ($product = $extractedProduct->createProduct()) {
                    $imported++;
                }
            }

            // Update ingestion status
            if ($imported > 0) {
                $ingestion->update([
                    'status' => CatalogueIngestion::STATUS_APPROVED,
                    'products_approved' => $imported,
                ]);
            }
        });

        return $imported;
    }

    /**
     * Bulk approve products
     */
    public function bulkApprove(array $productIds, int $userId): int
    {
        return ExtractedProduct::whereIn('id', $productIds)
            ->where('review_status', ExtractedProduct::STATUS_PENDING)
            ->update([
                'review_status' => ExtractedProduct::STATUS_APPROVED,
                'reviewed_by' => $userId,
                'reviewed_at' => now(),
            ]);
    }

    /**
     * Bulk reject products
     */
    public function bulkReject(array $productIds, int $userId, ?string $reason = null): int
    {
        return ExtractedProduct::whereIn('id', $productIds)
            ->where('review_status', ExtractedProduct::STATUS_PENDING)
            ->update([
                'review_status' => ExtractedProduct::STATUS_REJECTED,
                'reviewed_by' => $userId,
                'reviewed_at' => now(),
                'review_notes' => $reason,
            ]);
    }
}