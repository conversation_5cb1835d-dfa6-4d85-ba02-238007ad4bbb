<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register chat order services
        $this->app->singleton(\App\Services\ProductSearchService::class);
        $this->app->singleton(\App\Services\LandingCostCalculatorService::class);
        $this->app->singleton(\App\Services\ChatOrderService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
