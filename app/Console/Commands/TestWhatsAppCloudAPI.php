<?php

namespace App\Console\Commands;

use App\Services\WhatsAppService;
use Illuminate\Console\Command;

class TestWhatsAppCloudAPI extends Command
{
    protected $signature = 'whatsapp:test {phone?} {--message=Hello from WhatsApp Cloud API test!}';

    protected $description = 'Test WhatsApp Cloud API integration';

    public function handle(WhatsAppService $whatsappService): int
    {
        $phone = $this->argument('phone') ?? '263775240298';
        $message = $this->option('message');

        $this->info('Testing WhatsApp Cloud API...');
        $this->info('API Type: ' . $whatsappService->getApiType());
        $this->info('Phone: ' . $phone);
        $this->info('Message: ' . $message);
        $this->newLine();

        // Test text message
        $this->info('Sending text message...');
        $result = $whatsappService->sendMessage($phone, $message);

        if ($result) {
            $this->info('✅ Text message sent successfully!');
        } else {
            $this->error('❌ Failed to send text message');
            return self::FAILURE;
        }

        $this->newLine();

        // Test template message if using Cloud API
        if ($whatsappService->getApiType() === 'cloud_api') {
            $this->info('Sending template message...');
            $templateResult = $whatsappService->sendTemplateMessage($phone, 'hello_world');

            if ($templateResult) {
                $this->info('✅ Template message sent successfully!');
            } else {
                $this->warn('⚠️  Template message failed (this is normal if template is not approved)');
            }
        }

        $this->newLine();
        $this->info('Test completed!');

        if ($whatsappService->getApiType() === 'cloud_api') {
            $this->info('Webhook URL: ' . config('app.url') . '/api/whatsapp/cloud-webhook');
            $this->info('Verify Token: ' . config('whatsapp.webhook_verify_token'));
        }

        return self::SUCCESS;
    }
}