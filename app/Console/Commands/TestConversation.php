<?php

namespace App\Console\Commands;

use App\Services\ConversationService;
use App\Models\Conversation;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class TestConversation extends Command
{
    protected $signature = 'conversation:test
                            {--phone=whatsapp:263775240298 : Phone number to simulate}
                            {--reset : Reset conversation history}
                            {--scenario= : Run a specific test scenario}';

    protected $description = 'Test WhatsApp bot conversations interactively';

    protected ConversationService $conversationService;
    protected $testPhone;
    protected $lastResponse = null;

    public function handle(ConversationService $conversationService): int
    {
        $this->conversationService = $conversationService;
        $this->testPhone = $this->option('phone');

        if ($this->option('reset')) {
            $this->resetConversation();
        }

        if ($scenario = $this->option('scenario')) {
            return $this->runScenario($scenario);
        }

        return $this->interactiveMode();
    }

    protected function interactiveMode(): int
    {
        $this->displayHeader();

        $this->info("Phone: {$this->testPhone}");
        $this->info("Type 'exit' to quit, 'reset' to clear history, 'help' for commands");
        $this->line(str_repeat('=', 60));

        while (true) {
            $input = $this->ask('You');

            if (strtolower($input) === 'exit') {
                $this->info('Goodbye! 👋');
                break;
            }

            if (strtolower($input) === 'reset') {
                $this->resetConversation();
                continue;
            }

            if (strtolower($input) === 'help') {
                $this->showHelp();
                continue;
            }

            if (strtolower($input) === 'context') {
                $this->showContext();
                continue;
            }

            if (strtolower($input) === 'history') {
                $this->showHistory();
                continue;
            }

            // Process the message
            $this->processMessage($input);
        }

        return self::SUCCESS;
    }

    protected function processMessage(string $message): void
    {
        $this->info("Processing: {$message}");
        $this->line('');

        // Capture the response by temporarily intercepting WhatsApp sends
        $responses = [];

        // Override WhatsApp service temporarily
        app()->bind(\App\Services\WhatsAppService::class, function() use (&$responses) {
            return new class($responses) {
                private $responses;

                public function __construct(&$responses) {
                    $this->responses = &$responses;
                }

                public function sendMessage($to, $message, $mediaUrls = []) {
                    $this->responses[] = $message;
                    return true;
                }

                public function sendTemplateMessage($to, $templateName, $languageCode = 'en_US', $parameters = []) {
                    $this->responses[] = "[Template: {$templateName}]";
                    return true;
                }

                public function getApiType() {
                    return 'cloud_api';
                }
            };
        });

        try {
            // Process through conversation service
            $this->conversationService->processIncomingMessage(
                $this->testPhone,
                $message,
                null,
                null
            );

            // Display bot responses
            if (!empty($responses)) {
                foreach ($responses as $response) {
                    $this->displayBotResponse($response);
                }
                $this->lastResponse = implode("\n", $responses);
            } else {
                $this->warn('Bot: [No response generated]');
            }

        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
            if ($this->option('verbose')) {
                $this->error($e->getTraceAsString());
            }
        }

        // Reset the binding
        app()->forgetInstance(\App\Services\WhatsAppService::class);

        $this->line(str_repeat('-', 60));
    }

    protected function displayBotResponse(string $response): void
    {
        // Format the response for better readability
        $lines = explode("\n", $response);

        $this->info('Bot:');
        foreach ($lines as $line) {
            if (trim($line) === '') {
                $this->line('');
            } elseif (str_starts_with($line, '*') && str_ends_with($line, '*')) {
                $this->comment('  ' . $line); // Headers in yellow
            } elseif (str_contains($line, '•')) {
                $this->line('  ' . $line); // Bullet points
            } elseif (str_contains($line, '💵') || str_contains($line, '📦') || str_contains($line, '📱')) {
                $this->info('  ' . $line); // Important info with emojis
            } else {
                $this->line('  ' . $line); // Regular text
            }
        }
    }

    protected function runScenario(string $scenario): int
    {
        $scenarios = [
            'greeting' => [
                'hello',
                'hi there',
                'good morning'
            ],
            'product' => [
                'hello',
                'I need a Samsung TV',
                'what about 55 inch?',
                'how much with delivery?',
                'add to cart'
            ],
            'full' => [
                'hello',
                'show me iphones',
                'iPhone 15 Pro',
                'what\'s the price?',
                'add to cart',
                'checkout',
                'my address is Harare',
                'confirm order'
            ],
            'support' => [
                'hello',
                'I have a problem',
                'speak to agent',
                'urgent help needed'
            ]
        ];

        if (!isset($scenarios[$scenario])) {
            $this->error("Unknown scenario: {$scenario}");
            $this->info('Available scenarios: ' . implode(', ', array_keys($scenarios)));
            return self::FAILURE;
        }

        $this->displayHeader();
        $this->info("Running scenario: {$scenario}");
        $this->line(str_repeat('=', 60));

        foreach ($scenarios[$scenario] as $message) {
            $this->comment("You: {$message}");
            $this->processMessage($message);
            sleep(1); // Pause between messages
        }

        $this->info("\nScenario complete!");
        return self::SUCCESS;
    }

    protected function resetConversation(): void
    {
        try {
            // Clean phone number
            $phone = str_replace('whatsapp:', '', $this->testPhone);

            // Delete conversation and related data
            $conversation = Conversation::where('wa_number', $this->testPhone)
                ->orWhere('wa_number', $phone)
                ->first();

            if ($conversation) {
                // Delete messages
                DB::table('messages')->where('conversation_id', $conversation->id)->delete();

                // Delete cart items if exists
                if ($conversation->cart_id) {
                    DB::table('cart_items')->where('cart_id', $conversation->cart_id)->delete();
                    DB::table('carts')->where('id', $conversation->cart_id)->delete();
                }

                // Delete conversation
                $conversation->delete();

                $this->info('✅ Conversation history cleared');
            } else {
                $this->info('No existing conversation found');
            }
        } catch (\Exception $e) {
            $this->error('Failed to reset: ' . $e->getMessage());
        }
    }

    protected function showContext(): void
    {
        try {
            $conversation = Conversation::where('wa_number', $this->testPhone)
                ->orWhere('wa_number', str_replace('whatsapp:', '', $this->testPhone))
                ->first();

            if ($conversation) {
                $this->info('Current Context:');
                $this->table(
                    ['Field', 'Value'],
                    [
                        ['Status', $conversation->status],
                        ['Stage', $conversation->stage ?? 'N/A'],
                        ['Cart ID', $conversation->cart_id ?? 'None'],
                        ['Customer ID', $conversation->customer_id ?? 'None'],
                        ['Context', json_encode($conversation->context, JSON_PRETTY_PRINT)],
                        ['Created', $conversation->created_at->diffForHumans()],
                    ]
                );
            } else {
                $this->warn('No active conversation');
            }
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
        }
    }

    protected function showHistory(): void
    {
        try {
            $conversation = Conversation::where('wa_number', $this->testPhone)
                ->orWhere('wa_number', str_replace('whatsapp:', '', $this->testPhone))
                ->first();

            if ($conversation) {
                $messages = DB::table('messages')
                    ->where('conversation_id', $conversation->id)
                    ->orderBy('created_at', 'desc')
                    ->limit(10)
                    ->get();

                if ($messages->count() > 0) {
                    $this->info('Recent Messages:');
                    foreach ($messages->reverse() as $msg) {
                        $sender = $msg->sender_type === 'user' ? 'You' : 'Bot';
                        $content = json_decode($msg->content);
                        $text = $content->body ?? '[Media]';

                        if ($sender === 'You') {
                            $this->comment("{$sender}: {$text}");
                        } else {
                            $this->info("{$sender}: " . substr($text, 0, 100) . (strlen($text) > 100 ? '...' : ''));
                        }
                    }
                } else {
                    $this->warn('No message history');
                }
            } else {
                $this->warn('No active conversation');
            }
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
        }
    }

    protected function showHelp(): void
    {
        $this->info('Available Commands:');
        $this->table(
            ['Command', 'Description'],
            [
                ['exit', 'Quit the conversation tester'],
                ['reset', 'Clear conversation history'],
                ['context', 'Show current conversation context'],
                ['history', 'Show recent message history'],
                ['help', 'Show this help message'],
            ]
        );

        $this->info("\nTest Scenarios (run with --scenario=name):");
        $this->line('  greeting - Test greeting flows');
        $this->line('  product  - Test product search and pricing');
        $this->line('  full     - Complete order flow');
        $this->line('  support  - Test support escalation');
    }

    protected function displayHeader(): void
    {
        $this->line('');
        $this->info('╔══════════════════════════════════════════════════════════╗');
        $this->info('║        WhatsApp Bot Conversation Tester                   ║');
        $this->info('║        Youzeafrika - Smart Product Assistant              ║');
        $this->info('╚══════════════════════════════════════════════════════════╝');
        $this->line('');
    }
}