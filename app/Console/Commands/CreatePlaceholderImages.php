<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CreatePlaceholderImages extends Command
{
    protected $signature = 'images:create-placeholders';
    protected $description = 'Create placeholder images for catalogues';

    public function handle()
    {
        $this->info('Creating placeholder images...');
        
        // Create placeholder catalogue image
        $this->createPlaceholderImage(
            public_path('images/placeholder-catalogue.jpg'),
            400,
            300,
            'Catalogue'
        );
        
        // Create placeholder covers for different shops
        $shops = ['makro', 'game', 'shoprite', 'checkers', 'woolworths'];
        foreach ($shops as $shop) {
            $this->createPlaceholderImage(
                public_path("images/catalogue-covers/{$shop}-placeholder.jpg"),
                400,
                300,
                ucfirst($shop)
            );
        }
        
        $this->info('Placeholder images created successfully!');
    }
    
    private function createPlaceholderImage($path, $width, $height, $text)
    {
        // Ensure directory exists
        $dir = dirname($path);
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }
        
        // Create image
        $image = imagecreatetruecolor($width, $height);
        
        // Colors
        $bg = imagecolorallocate($image, 245, 245, 245);
        $border = imagecolorallocate($image, 200, 200, 200);
        $textColor = imagecolorallocate($image, 100, 100, 100);
        
        // Fill background
        imagefilledrectangle($image, 0, 0, $width, $height, $bg);
        
        // Draw border
        imagerectangle($image, 0, 0, $width - 1, $height - 1, $border);
        
        // Add diagonal lines for placeholder effect
        for ($i = 0; $i < $width; $i += 20) {
            imageline($image, $i, 0, $i + $height, $height, $border);
        }
        
        // Add text
        $fontSize = 5; // Built-in font size
        $textWidth = imagefontwidth($fontSize) * strlen($text);
        $textHeight = imagefontheight($fontSize);
        $x = ($width - $textWidth) / 2;
        $y = ($height - $textHeight) / 2;
        
        imagestring($image, $fontSize, $x, $y, $text, $textColor);
        
        // Save image
        imagejpeg($image, $path, 85);
        imagedestroy($image);
        
        $this->info("Created: $path");
    }
}