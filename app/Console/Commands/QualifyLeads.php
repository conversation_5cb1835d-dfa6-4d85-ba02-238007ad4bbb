<?php

namespace App\Console\Commands;

use App\Models\Lead;
use App\Services\LeadCaptureService;
use Illuminate\Console\Command;

class QualifyLeads extends Command
{
    protected $signature = 'leads:qualify';
    
    protected $description = 'Auto-qualify leads based on engagement';

    public function handle(LeadCaptureService $leadCaptureService): void
    {
        $this->info('Processing lead qualification...');
        
        // Get leads that need qualification
        $leads = Lead::where('quality', 'cold')
            ->where('created_at', '>=', now()->subDays(30))
            ->get();

        $qualified = 0;
        
        foreach ($leads as $lead) {
            $oldQuality = $lead->quality;
            $leadCaptureService->qualifyLead($lead);
            
            if ($lead->quality !== $oldQuality) {
                $qualified++;
                $this->line("Lead #{$lead->id} upgraded from {$oldQuality} to {$lead->quality}");
            }
        }
        
        $this->info("Processed {$leads->count()} leads. {$qualified} qualified for upgrade.");
    }
}