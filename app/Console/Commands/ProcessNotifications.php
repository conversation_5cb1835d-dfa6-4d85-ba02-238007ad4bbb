<?php

namespace App\Console\Commands;

use App\Services\NotificationService;
use Illuminate\Console\Command;

class ProcessNotifications extends Command
{
    protected $signature = 'notifications:process';
    
    protected $description = 'Process scheduled notifications';

    public function handle(NotificationService $notificationService): void
    {
        $this->info('Processing scheduled notifications...');
        
        $notificationService->processScheduledNotifications();
        
        $this->info('Notifications processed successfully.');
    }
}