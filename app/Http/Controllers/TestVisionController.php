<?php

namespace App\Http\Controllers;

use App\Services\GeminiService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class TestVisionController extends Controller
{
    protected GeminiService $geminiService;

    public function __construct(GeminiService $geminiService)
    {
        $this->geminiService = $geminiService;
    }

    /**
     * Test Gemini API configuration
     */
    public function testGeminiConfig(): JsonResponse
    {
        try {
            // Test basic text generation first
            $textResult = $this->geminiService->generateText("Say 'Hello, Gemini API is working!'");

            return response()->json([
                'success' => true,
                'api_key_configured' => !empty(config('services.gemini.api_key')),
                'api_key_length' => strlen(config('services.gemini.api_key') ?? ''),
                'text_generation_test' => $textResult,
                'message' => 'Gemini API configuration test completed'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'api_key_configured' => !empty(config('services.gemini.api_key')),
                'api_key_length' => strlen(config('services.gemini.api_key') ?? ''),
            ], 500);
        }
    }

    /**
     * Test Twilio media download
     */
    public function testTwilioDownload(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'media_url' => 'required|url'
            ]);

            $mediaUrl = $request->input('media_url');

            // Test the download function
            $imageData = \App\Services\BotFlow::downloadTwilioMedia($mediaUrl);

            if ($imageData) {
                return response()->json([
                    'success' => true,
                    'downloaded' => true,
                    'size' => strlen($imageData) . ' bytes',
                    'first_bytes' => bin2hex(substr($imageData, 0, 16))
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'downloaded' => false,
                    'error' => 'Failed to download media'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test vision API with uploaded image
     */
    public function testImageUpload(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120' // 5MB max
            ]);

            if ($request->hasFile('image')) {
                $image = $request->file('image');
                $filename = 'test_' . time() . '.' . $image->getClientOriginalExtension();
                $path = $image->move(public_path('uploads/temp'), $filename);

                $relativePath = 'uploads/temp/' . $filename;

                // Analyze the image
                $result = $this->geminiService->analyzeProductImage($relativePath);

                // Clean up
                if (file_exists(public_path($relativePath))) {
                    unlink(public_path($relativePath));
                }

                return response()->json([
                    'success' => true,
                    'vision_result' => $result
                ]);
            }

            return response()->json([
                'success' => false,
                'error' => 'No image file received'
            ], 400);

        } catch (\Exception $e) {
            Log::error('Vision test error', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test with existing sample images
     */
    public function testSampleImages(): JsonResponse
    {
        $results = [];
        $sampleDir = base_path('docs/samples');

        if (is_dir($sampleDir)) {
            $images = glob($sampleDir . '/*.{jpg,jpeg,png,gif}', GLOB_BRACE);

            foreach (array_slice($images, 0, 3) as $imagePath) { // Test first 3 images
                $filename = basename($imagePath);
                $tempPath = 'uploads/temp/sample_' . $filename;

                // Copy to public temp directory
                copy($imagePath, public_path($tempPath));

                // Analyze
                $result = $this->geminiService->analyzeProductImage($tempPath);

                $results[$filename] = [
                    'success' => $result['success'],
                    'products_found' => count($result['products'] ?? []),
                    'products' => $result['products'] ?? [],
                    'error' => $result['error'] ?? null
                ];

                // Clean up
                if (file_exists(public_path($tempPath))) {
                    unlink(public_path($tempPath));
                }
            }
        }

        return response()->json([
            'success' => true,
            'sample_results' => $results
        ]);
    }

    /**
     * Test complete WhatsApp image flow
     */
    public function testCompleteImageFlow(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'phone' => 'required|string',
                'image_url' => 'required|url'
            ]);

            $phone = $request->input('phone');
            $imageUrl = $request->input('image_url');

            // Simulate Twilio webhook data
            $webhookData = [
                'MessageSid' => 'TEST_' . uniqid(),
                'From' => 'whatsapp:' . $phone,
                'To' => 'whatsapp:+14155238886',
                'Body' => '',
                'NumMedia' => '1',
                'MediaUrl0' => $imageUrl,
                'MediaContentType0' => 'image/jpeg'
            ];

            Log::info('Testing complete WhatsApp image flow', [
                'phone' => $phone,
                'image_url' => $imageUrl,
                'webhook_data' => $webhookData
            ]);

            // Process through WhatsApp service
            $whatsappService = app(\App\Services\WhatsAppService::class);
            $result = $whatsappService->processWebhookMessage($webhookData);

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'error' => 'WhatsApp service failed',
                    'details' => $result
                ], 500);
            }

            // Get the processed message
            $message = $result['messages'][0] ?? null;
            if (!$message) {
                return response()->json([
                    'success' => false,
                    'error' => 'No message processed'
                ], 500);
            }

            // Find or create customer
            $phoneE164 = str_replace('whatsapp:', '', $phone);
            $customer = \App\Models\Customer::firstOrCreate(
                ['phone_e164' => $phoneE164],
                ['first_name' => 'Test User']
            );

            // Test the BotFlow captureItem method
            \App\Services\BotFlow::captureItem($customer, $message);

            // Get the created intake item
            $intakeItem = \App\Models\IntakeItem::where('customer_id', $customer->id)
                ->latest()
                ->first();

            return response()->json([
                'success' => true,
                'webhook_processing' => $result,
                'processed_message' => $message,
                'customer' => $customer,
                'intake_item' => $intakeItem,
                'media_url_captured' => $intakeItem->media_id ?? null
            ]);

        } catch (\Exception $e) {
            Log::error('Test complete image flow failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Simulate WhatsApp image processing
     */
    public function simulateWhatsAppFlow(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'customer_phone' => 'required|string',
                'image_url' => 'required|url'
            ]);

            // Simulate downloading image from WhatsApp media URL
            $imageData = file_get_contents($request->image_url);
            if (!$imageData) {
                throw new \Exception('Could not download image');
            }

            // Save temporarily
            $filename = 'whatsapp_' . time() . '.jpg';
            $tempPath = 'uploads/temp/' . $filename;
            file_put_contents(public_path($tempPath), $imageData);

            // Process with Gemini
            $visionResult = $this->geminiService->analyzeProductImage($tempPath);

            // Clean up
            if (file_exists(public_path($tempPath))) {
                unlink(public_path($tempPath));
            }

            // Simulate creating intake item and processing through bot flow
            $simulation = [
                'customer_phone' => $request->customer_phone,
                'vision_analysis' => $visionResult,
                'next_bot_action' => $this->determineNextBotAction($visionResult),
                'suggested_response' => $this->generateBotResponse($visionResult)
            ];

            return response()->json([
                'success' => true,
                'simulation' => $simulation
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    private function determineNextBotAction(array $visionResult): string
    {
        if (!$visionResult['success']) {
            return 'ask_for_manual_details';
        }

        $products = $visionResult['products'] ?? [];

        if (empty($products)) {
            return 'ask_for_manual_details';
        }

        $hasPrice = false;
        $hasOutlet = false;

        foreach ($products as $product) {
            if (!empty($product['price_zar'])) $hasPrice = true;
            if (!empty($product['outlet'])) $hasOutlet = true;
        }

        if (!$hasPrice) return 'ask_for_price';
        if (!$hasOutlet) return 'ask_for_outlet';

        return 'calculate_pricing';
    }

    private function generateBotResponse(array $visionResult): string
    {
        if (!$visionResult['success'] || empty($visionResult['products'])) {
            return "I can see you sent an image, but I'm having trouble analyzing it. What product is this and what's the price you see?";
        }

        $product = $visionResult['products'][0];
        $name = $product['name'] ?? 'this product';

        if (empty($product['price_zar'])) {
            return "I can see a {$name}. What's the price you see?";
        }

        if (empty($product['outlet'])) {
            return "I can see a {$name} for R" . number_format($product['price_zar'], 2) . ". Which shop is this from? (Makro, Game, Takealot, etc.)";
        }

        return "Great! I found: {$name} from {$product['outlet']} for R" . number_format($product['price_zar'], 2) . ". Let me calculate the USD delivered price for you.";
    }
}