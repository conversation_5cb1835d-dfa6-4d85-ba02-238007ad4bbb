<?php

namespace App\Http\Controllers;

use App\Services\PricingEngine;
use App\Services\WhatsAppService;
use App\Models\Customer;
use App\Models\Cart;
use App\Models\Order;
use App\Models\IntakeItem;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class BotController extends Controller
{
    protected PricingEngine $pricingEngine;
    protected WhatsAppService $whatsAppService;

    public function __construct(
        PricingEngine $pricingEngine,
        WhatsAppService $whatsAppService
    ) {
        $this->pricingEngine = $pricingEngine;
        $this->whatsAppService = $whatsAppService;
    }

    /**
     * Compute USD landed price as per guide.txt
     * POST /bot/price/quote
     */
    public function quote(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'source_price' => 'required|numeric|min:0',
                'currency' => 'required|string|max:3',
                'outlet' => 'nullable|string',
                'city' => 'nullable|string',
                'category' => 'nullable|string',
            ]);

            $pricing = $this->pricingEngine->calculate(
                $validated['source_price'],
                $validated['currency'],
                $validated['outlet'] ?? null,
                $validated['city'] ?? 'Harare'
            );

            return response()->json([
                'success' => true,
                'data' => $pricing
            ]);

        } catch (\Exception $e) {
            Log::error('Bot price quote failed', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Unable to calculate pricing'
            ], 500);
        }
    }

    /**
     * Add item to cart
     * POST /bot/cart/add
     */
    public function addToCart(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'customer_id' => 'required|exists:customers,id',
                'product_name' => 'required|string',
                'outlet' => 'required|string',
                'source_price' => 'required|numeric|min:0',
                'currency' => 'string|max:3',
                'source_url' => 'nullable|url',
            ]);

            $customer = Customer::find($validated['customer_id']);

            // Get or create active cart
            $cart = Cart::where('customer_id', $customer->id)
                ->where('status', 'active')
                ->first();

            if (!$cart) {
                $cart = Cart::create([
                    'customer_id' => $customer->id,
                    'status' => 'active'
                ]);
            }

            // Calculate pricing
            $pricing = $this->pricingEngine->calculate(
                $validated['source_price'],
                $validated['currency'] ?? 'ZAR',
                $validated['outlet']
            );

            // Add to cart
            $cartItem = $cart->items()->create([
                'product_name' => $validated['product_name'],
                'outlet' => $validated['outlet'],
                'source_url' => $validated['source_url'],
                'price_value' => $validated['source_price'],
                'price_currency' => $validated['currency'] ?? 'ZAR',
                'computed_total_usd' => $pricing['total_usd'],
                'meta' => json_encode($pricing)
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'cart_id' => $cart->id,
                    'item_id' => $cartItem->id,
                    'total_usd' => $pricing['total_usd']
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Bot add to cart failed', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Unable to add item to cart'
            ], 500);
        }
    }

    /**
     * Confirm order
     * POST /bot/order/confirm
     */
    public function confirmOrder(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'customer_id' => 'required|exists:customers,id',
                'cart_id' => 'nullable|exists:carts,id',
                'city' => 'nullable|string',
            ]);

            $customer = Customer::find($validated['customer_id']);
            $cart = null;
            $totalUsd = 0;

            if (!empty($validated['cart_id'])) {
                $cart = Cart::find($validated['cart_id']);
                $totalUsd = $cart->items()->sum('computed_total_usd');
            }

            // Generate reference number as per guide
            $reference = 'YA-' . strtoupper(substr(md5(time() . $customer->id), 0, 4));

            // Create a simple quote first (required by Order model)
            $quote = \App\Models\Quote::create([
                'customer_id' => $customer->id,
                'total_usd' => $totalUsd,
                'status' => 'accepted',
                'valid_until' => now()->addDays(7)
            ]);

            // Create order
            $order = Order::create([
                'quote_id' => $quote->id,
                'customer_id' => $customer->id,
                'order_ref' => $reference, // Keep existing field
                'reference' => $reference, // New guide field
                'status' => 'NEW',
                'total_usd' => $totalUsd,
                'city' => $validated['city'] ?? 'Harare',
                'cart_snapshot' => $cart ? $cart->toJson() : null,
                'breakdown_json' => json_encode([
                    'cart_id' => $cart?->id,
                    'items_count' => $cart ? $cart->items()->count() : 0,
                    'created_via' => 'whatsapp_bot'
                ])
            ]);

            // Mark cart as converted if it exists
            if ($cart) {
                $cart->update(['status' => 'converted']);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'order_id' => $order->id,
                    'reference' => $reference,
                    'total_usd' => $totalUsd,
                    'status' => 'NEW'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Bot order confirm failed', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Unable to confirm order'
            ], 500);
        }
    }
}