<?php

namespace App\Http\Controllers;

use App\Services\WhatsAppService;
use App\Services\BotFlow;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class SimpleBotController extends Controller
{
    protected WhatsAppService $whatsappService;

    public function __construct(WhatsAppService $whatsappService)
    {
        $this->whatsappService = $whatsappService;
    }

    /**
     * Webhook verification - Twi<PERSON> doesn't need verification
     */
    public function verify(Request $request): Response|string
    {
        Log::info('Twilio webhook verification (no parameters needed)');
        return response('OK', 200);
    }

    /**
     * Handle incoming Twilio WhatsApp messages using simplified state machine
     */
    public function handle(Request $request): Response
    {
        try {
            $data = $request->all();

            Log::info('Twilio WhatsApp webhook received', [
                'message_sid' => $data['MessageSid'] ?? null,
                'from' => $data['From'] ?? null,
                'body' => $data['Body'] ?? null,
                'num_media' => $data['NumMedia'] ?? 0
            ]);

            $result = $this->whatsappService->processWebhookMessage($data);

            if ($result['success']) {
                foreach ($result['messages'] as $message) {
                    $this->processMessage($message);
                }
            }

            // Always return TwiML XML response for Twilio
            return $this->twimlResponse();

        } catch (\Exception $e) {
            Log::error('Twilio webhook error: ' . $e->getMessage(), [
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            // Even on error, return valid TwiML
            return $this->twimlResponse();
        }
    }

    /**
     * Process individual message using BotFlow state machine
     */
    protected function processMessage(array $message): void
    {
        try {
            $phoneE164 = $this->cleanPhoneNumber($message['from']);

            // Find or create customer
            $customer = $this->identifyCustomer($phoneE164);

            // Check for special commands first
            if ($this->handleSpecialCommands($customer, $message)) {
                return;
            }

            // Check for greeting intents (hi, hello, hey, etc.)
            if ($this->isGreetingMessage($message)) {
                BotFlow::reset($customer);
                return;
            }

            // Get current state
            $state = BotFlow::getState($customer->id);

            Log::info('Processing message in state', [
                'customer_id' => $customer->id,
                'phone' => $phoneE164,
                'state' => $state,
                'message_type' => $message['type']
            ]);

            // Process based on current state
            switch ($state) {
                case 'GREET':
                    BotFlow::greet($customer);
                    break;

                case 'IDENTIFY':
                    BotFlow::identify($customer, $message);
                    break;

                case 'INTENT':
                    BotFlow::askForItem($customer, $message);
                    break;

                case 'CAPTURE_ITEM':
                    BotFlow::captureItem($customer, $message);
                    break;

                case 'ENRICH_ITEM':
                    BotFlow::enrichItem($customer);
                    break;

                case 'PRICE':
                    BotFlow::price($customer);
                    break;

                case 'CART_OR_ORDER':
                    BotFlow::cartOrOrder($customer, $message);
                    break;

                case 'ORDER_CONFIRM':
                    BotFlow::confirmOrder($customer, $message);
                    break;

                default:
                    BotFlow::reset($customer);
                    break;
            }

        } catch (\Exception $e) {
            Log::error('Error processing WhatsApp message', [
                'error' => $e->getMessage(),
                'message' => $message
            ]);

            // Send friendly error message
            $this->whatsappService->sendMessage(
                $message['from'],
                "I'm having a moment. Please try again or type 'help' for assistance."
            );
        }
    }

    /**
     * Identify or create customer from phone number
     */
    protected function identifyCustomer(string $phoneE164): Customer
    {
        $customer = Customer::where('phone_e164', $phoneE164)->first();
        
        if (!$customer) {
            $customer = Customer::create([
                'phone_e164' => $phoneE164,
                'phone' => $phoneE164,
                'is_blocked' => false,
            ]);
            
            Log::info('New customer created', [
                'customer_id' => $customer->id,
                'phone' => $phoneE164
            ]);
        }
        
        return $customer;
    }

    /**
     * Clean phone number to E164 format
     */
    protected function cleanPhoneNumber(string $phone): string
    {
        // Remove whatsapp: prefix if present
        $phone = str_replace('whatsapp:', '', $phone);
        
        // Remove any non-digit characters except +
        $phone = preg_replace('/[^\d+]/', '', $phone);
        
        // Ensure it starts with +
        if (!str_starts_with($phone, '+')) {
            $phone = '+' . $phone;
        }
        
        return $phone;
    }

    /**
     * Handle special commands that work in any state
     */
    protected function handleSpecialCommands(Customer $customer, array $message): bool
    {
        $text = strtolower($message['text'] ?? '');
        
        // Reset/restart command
        if (in_array($text, ['reset', 'restart', 'start over', 'begin'])) {
            BotFlow::reset($customer);
            return true;
        }
        
        // Help command
        if (in_array($text, ['help', 'assistance', 'support'])) {
            $helpMessage = "🆘 *How I can help:*\n\n" .
                          "🔍 *Search Products:* Just type what you want\n" .
                          "📸 *Send Photos:* I can analyze product images\n" .
                          "💰 *Get Prices:* I'll show you USD delivered pricing\n" .
                          "🛒 *Place Orders:* I'll guide you through the process\n\n" .
                          "Type 'restart' to begin again.";
            
            $this->whatsappService->sendMessage($customer->phone_e164, $helpMessage);
            return true;
        }
        
        // Agent/human request
        if (in_array($text, ['agent', 'human', 'person', 'representative'])) {
            $agentMessage = "🙋‍♂️ *Connecting you to our team...*\n\n" .
                           "A human agent will assist you shortly. Please describe what you need help with.\n\n" .
                           "⏰ *Available:* Mon-Fri 8AM-5PM CAT, Sat 9AM-2PM CAT";
            
            $this->whatsappService->sendMessage($customer->phone_e164, $agentMessage);
            return true;
        }
        
        return false;
    }

    /**
     * Check if message is a greeting intent
     */
    protected function isGreetingMessage(array $message): bool
    {
        $text = strtolower(trim($message['text'] ?? ''));

        // Greeting patterns from guide.txt: hi|hello|hey|morning|afternoon
        $greetingPatterns = [
            'hi', 'hello', 'hey', 'hola', 'howdy',
            'good morning', 'morning', 'good afternoon', 'afternoon',
            'good evening', 'evening', 'good day',
            '👋', '👋🏽', '👋🏾', '👋🏿', '👋🏻', '👋🏼'
        ];

        foreach ($greetingPatterns as $pattern) {
            if ($text === $pattern || str_starts_with($text, $pattern . ' ')) {
                return true;
            }
        }

        return false;
    }

    /**
     * Return proper TwiML response for Twilio
     */
    protected function twimlResponse(string $message = ''): Response
    {
        $twiml = '<?xml version="1.0" encoding="UTF-8"?><Response>';

        if (!empty($message)) {
            $twiml .= '<Message>' . htmlspecialchars($message) . '</Message>';
        }

        $twiml .= '</Response>';

        return response($twiml, 200)->header('Content-Type', 'application/xml');
    }
}
