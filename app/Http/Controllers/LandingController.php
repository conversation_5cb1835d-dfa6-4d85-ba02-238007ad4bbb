<?php

namespace App\Http\Controllers;

use App\Models\Shop;
use App\Models\Product;
use App\Models\Catalogue;
use Illuminate\Http\Request;
use Inertia\Inertia;

class LandingController extends Controller
{
    public function index()
    {
        // Get featured shops
        $shops = Shop::where('status', 'active')
            ->select('id', 'name', 'slug', 'logo_url', 'website', 'status')
            ->get()
            ->map(function ($shop) {
                return [
                    'id' => $shop->id,
                    'name' => $shop->name,
                    'slug' => $shop->slug,
                    'logo_url' => $shop->logo_url ? asset($shop->logo_url) : asset("/uploads/shops/logos/{$shop->slug}.jpg"),
                    'description' => ucfirst($shop->slug) . ' - South African retailer', // Generate description
                    'active' => $shop->status === 'active',
                    'catalogue_count' => rand(5, 20) // Mock catalogue count for now
                ];
            });

        // Get real catalogues from database
        $catalogues = Catalogue::with('shop')
            ->where('status', 'active')
            ->where(function($query) {
                $query->whereNull('valid_to')
                      ->orWhere('valid_to', '>=', now());
            })
            ->orderBy('created_at', 'desc')
            ->limit(12)
            ->get()
            ->map(function ($catalogue) {
                return [
                    'id' => $catalogue->id,
                    'slug' => $catalogue->slug,
                    'shop_slug' => $catalogue->shop->slug ?? 'default',
                    'shop_logo_url' => $catalogue->shop->logo_url ? asset($catalogue->shop->logo_url) : asset("/uploads/shops/logos/{$catalogue->shop->slug}.jpg"),
                    'title' => $catalogue->title,
                    'type' => $catalogue->type,
                    'cover_url' => $catalogue->cover_url ? asset($catalogue->cover_url) : '/images/catalogue-placeholder.jpg',
                    'pdf_url' => $catalogue->pdf_url ? asset($catalogue->pdf_url) : null,
                    'images' => $catalogue->type === 'images' && $catalogue->images 
                        ? collect($catalogue->images)->map(fn($img) => asset($img))->toArray()
                        : [],
                    'page_count' => $catalogue->page_count ?? 0,
                    'updated_at' => $catalogue->updated_at->toISOString()
                ];
            });

        // Get featured products
        $featured_products = Product::with(['variants', 'shop', 'category'])
            ->where('featured', true)
            ->where('status', 'active')
            ->limit(6)
            ->get()
            ->map(function ($product) {
                $variant = $product->variants->first();
                return [
                    'id' => $product->id,
                    'name' => $product->title, // Use title field from migration
                    'description' => $product->description,
                    'image_url' => '/images/products/placeholder.jpg', // No image_url field in migration
                    'shop_name' => $product->shop->name,
                    'price_zar' => $variant?->sa_price_zar ?? 0,
                    'category' => $product->category->name ?? 'General'
                ];
            });

        return Inertia::render('Landing', [
            'shops' => $shops,
            'catalogues' => $catalogues,
            'featured_products' => $featured_products
        ]);
    }

    public function catalogues(Request $request)
    {
        // Get filters from request
        $shopId = $request->get('shop');
        $type = $request->get('type');
        $search = $request->get('search');
        
        // Build query
        $query = Catalogue::with('shop')
            ->where('status', 'active')
            ->where(function($q) {
                $q->whereNull('valid_to')
                  ->orWhere('valid_to', '>=', now());
            });
        
        // Apply filters
        if ($shopId) {
            $query->where('shop_id', $shopId);
        }
        
        if ($type) {
            $query->where('type', $type);
        }
        
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        // Get catalogues
        $catalogues = $query->orderBy('created_at', 'desc')
            ->paginate(12)
            ->through(function ($catalogue) {
                return [
                    'id' => $catalogue->id,
                    'slug' => $catalogue->slug,
                    'title' => $catalogue->title,
                    'description' => $catalogue->description,
                    'type' => $catalogue->type,
                    'shop' => [
                        'id' => $catalogue->shop->id,
                        'name' => $catalogue->shop->name,
                        'slug' => $catalogue->shop->slug,
                        'logo_url' => $catalogue->shop->logo_url ? asset($catalogue->shop->logo_url) : asset("/uploads/shops/logos/{$catalogue->shop->slug}.jpg"),
                    ],
                    'cover_url' => $catalogue->cover_url ? asset($catalogue->cover_url) : '/images/catalogue-placeholder.jpg',
                    'page_count' => $catalogue->page_count ?? 0,
                    'valid_from' => $catalogue->valid_from?->format('Y-m-d'),
                    'valid_to' => $catalogue->valid_to?->format('Y-m-d'),
                    'created_at' => $catalogue->created_at->diffForHumans(),
                    'updated_at' => $catalogue->updated_at->toISOString()
                ];
            });
        
        // Get shops for filter dropdown
        $shops = Shop::where('status', 'active')
            ->select('id', 'name', 'slug', 'logo_url')
            ->orderBy('name')
            ->get()
            ->map(function ($shop) {
                return [
                    'id' => $shop->id,
                    'name' => $shop->name,
                    'slug' => $shop->slug,
                    'logo_url' => $shop->logo_url ? asset($shop->logo_url) : asset("/uploads/shops/logos/{$shop->slug}.jpg"),
                ];
            });
        
        return Inertia::render('Catalogues/Index', [
            'catalogues' => $catalogues,
            'shops' => $shops,
            'filters' => [
                'shop' => $shopId,
                'type' => $type,
                'search' => $search
            ]
        ]);
    }

    public function catalogue(Catalogue $catalogue)
    {
        // Load the shop relationship
        $catalogue->load('shop');
            
        // Format catalogue data for frontend
        $catalogueData = [
            'id' => $catalogue->id,
            'slug' => $catalogue->slug,
            'title' => $catalogue->title,
            'description' => $catalogue->description,
            'type' => $catalogue->type,
            'shop' => [
                'id' => $catalogue->shop->id,
                'name' => $catalogue->shop->name,
                'slug' => $catalogue->shop->slug,
                'logo_url' => $catalogue->shop->logo_url ? asset($catalogue->shop->logo_url) : asset("/uploads/shops/logos/{$catalogue->shop->slug}.jpg"),
            ],
            'cover_url' => $catalogue->cover_url ? asset($catalogue->cover_url) : null,
            'pdf_url' => $catalogue->pdf_url ? asset($catalogue->pdf_url) : null,
            'images' => $catalogue->type === 'images' && $catalogue->images 
                ? collect($catalogue->images)->map(fn($img) => asset($img))->toArray()
                : [],
            'page_count' => $catalogue->page_count,
            'valid_from' => $catalogue->valid_from?->format('Y-m-d'),
            'valid_to' => $catalogue->valid_to?->format('Y-m-d'),
            'created_at' => $catalogue->created_at->toISOString(),
        ];
        
        return Inertia::render('Catalogues/Viewer', [
            'catalogue' => $catalogueData
        ]);
    }
}