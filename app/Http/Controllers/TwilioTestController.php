<?php

namespace App\Http\Controllers;

use App\Services\WhatsAppService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TwilioTestController extends Controller
{
    protected $whatsappService;

    public function __construct(WhatsAppService $whatsappService)
    {
        $this->whatsappService = $whatsappService;
    }

    /**
     * Test Twilio configuration
     */
    public function testConfig()
    {
        $config = [
            'twilio_sid' => config('services.twilio.sid'),
            'twilio_token' => config('services.twilio.token') ? 'SET' : 'NOT SET',
            'twilio_from' => config('services.twilio.whatsapp_from'),
            'force_mode' => config('services.twilio.force_mode'),
            'skip_verification' => config('services.twilio.skip_verification'),
        ];

        return response()->json([
            'success' => true,
            'message' => 'Twilio configuration check',
            'config' => $config,
            'using_cloud_api' => property_exists($this->whatsappService, 'useCloudApi') ? 
                $this->whatsappService->useCloudApi : 'Unknown'
        ]);
    }

    /**
     * Test sending a message via Twilio
     */
    public function testSendMessage(Request $request)
    {
        $to = $request->input('to', '+************'); // Default test number
        $message = $request->input('message', 'Test message from Youze Afrika bot via Twilio');

        try {
            $result = $this->whatsappService->sendMessage($to, $message);

            return response()->json([
                'success' => $result,
                'message' => $result ? 'Message sent successfully' : 'Failed to send message',
                'to' => $to,
                'sent_message' => $message
            ]);

        } catch (\Exception $e) {
            Log::error('Twilio test send failed', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'to' => $to
            ], 500);
        }
    }

    /**
     * Simulate a Twilio webhook message
     */
    public function simulateWebhook(Request $request)
    {
        // Simulate Twilio webhook data
        $twilioData = [
            'MessageSid' => 'SM' . uniqid(),
            'From' => 'whatsapp:+************',
            'To' => 'whatsapp:+***********',
            'Body' => $request->input('message', 'Hello from test'),
            'NumMedia' => '0',
            'AccountSid' => config('services.twilio.sid'),
        ];

        try {
            $result = $this->whatsappService->processWebhookMessage($twilioData);

            return response()->json([
                'success' => true,
                'message' => 'Webhook simulation completed',
                'webhook_data' => $twilioData,
                'processing_result' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('Twilio webhook simulation failed', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'webhook_data' => $twilioData
            ], 500);
        }
    }

    /**
     * Test the complete flow: webhook -> bot processing
     */
    public function testCompleteFlow(Request $request)
    {
        $message = $request->input('message', 'hi');
        $from = $request->input('from', '+************');

        // Simulate Twilio webhook
        $twilioData = [
            'MessageSid' => 'SM' . uniqid(),
            'From' => 'whatsapp:' . $from,
            'To' => 'whatsapp:+***********',
            'Body' => $message,
            'NumMedia' => '0',
            'AccountSid' => config('services.twilio.sid'),
        ];

        try {
            // Process webhook
            $webhookResult = $this->whatsappService->processWebhookMessage($twilioData);
            
            $response = [
                'success' => true,
                'message' => 'Complete flow test completed',
                'steps' => [
                    'webhook_processing' => $webhookResult,
                ]
            ];

            // If webhook processing succeeded, simulate bot processing
            if ($webhookResult['success'] && !empty($webhookResult['messages'])) {
                $processedMessage = $webhookResult['messages'][0];
                
                $response['steps']['bot_processing'] = [
                    'message_id' => $processedMessage['id'],
                    'from' => $processedMessage['from'],
                    'text' => $processedMessage['text'] ?? '',
                    'type' => $processedMessage['type'],
                    'note' => 'Message would be processed by BotFlow service'
                ];
            }

            return response()->json($response);

        } catch (\Exception $e) {
            Log::error('Complete flow test failed', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'test_data' => $twilioData
            ], 500);
        }
    }
}
