<?php

namespace App\Http\Controllers;

use App\Services\BotFlow;
use App\Services\PricingEngine;
use App\Models\Customer;
use App\Models\IntakeItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SimpleBotTestController extends Controller
{
    /**
     * Test the simplified bot flow
     */
    public function testFlow(Request $request)
    {
        try {
            // Create a test customer
            $customer = Customer::firstOrCreate(
                ['phone_e164' => '+263771234567'],
                [
                    'first_name' => 'Test',
                    'phone' => '+263771234567',
                    'is_blocked' => false,
                ]
            );

            $results = [];

            // Test 1: Greeting
            $results['greeting'] = $this->testGreeting($customer);

            // Test 2: Identification
            $results['identification'] = $this->testIdentification($customer);

            // Test 3: Item capture
            $results['item_capture'] = $this->testItemCapture($customer);

            // Test 4: Pricing
            $results['pricing'] = $this->testPricing();

            return response()->json([
                'success' => true,
                'message' => 'Simple bot flow test completed',
                'results' => $results,
                'customer_id' => $customer->id,
                'current_state' => BotFlow::getState($customer->id)
            ]);

        } catch (\Exception $e) {
            Log::error('Bot flow test failed', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    protected function testGreeting(Customer $customer): array
    {
        try {
            BotFlow::greet($customer);
            $state = BotFlow::getState($customer->id);
            
            return [
                'success' => true,
                'state_after_greeting' => $state,
                'expected_state' => 'IDENTIFY'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    protected function testIdentification(Customer $customer): array
    {
        try {
            $message = ['text' => 'John Doe', 'type' => 'text'];
            BotFlow::identify($customer, $message);
            $state = BotFlow::getState($customer->id);
            
            return [
                'success' => true,
                'state_after_identify' => $state,
                'expected_state' => 'INTENT',
                'customer_name' => $customer->fresh()->first_name
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    protected function testItemCapture(Customer $customer): array
    {
        try {
            $message = [
                'text' => 'Samsung Galaxy A15 128GB from Game, R3,499',
                'type' => 'text'
            ];
            
            BotFlow::captureItem($customer, $message);
            
            // Check if intake item was created
            $intakeItem = IntakeItem::where('customer_id', $customer->id)
                ->orderBy('created_at', 'desc')
                ->first();
            
            return [
                'success' => true,
                'intake_item_created' => $intakeItem ? true : false,
                'intake_item_id' => $intakeItem?->id,
                'intake_item_status' => $intakeItem?->status,
                'extracted_data' => [
                    'name' => $intakeItem?->extracted_name,
                    'outlet' => $intakeItem?->extracted_outlet,
                    'price' => $intakeItem?->extracted_price
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    protected function testPricing(): array
    {
        try {
            $pricingEngine = new PricingEngine();
            $result = $pricingEngine->calculate(3499, 'ZAR', 'Game', 'Harare');
            
            return [
                'success' => true,
                'pricing_calculation' => $result,
                'total_usd' => $result['total_usd'],
                'breakdown_included' => isset($result['breakdown'])
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Simulate a complete conversation flow
     */
    public function simulateConversation(Request $request)
    {
        try {
            // Create a test customer
            $customer = Customer::firstOrCreate(
                ['phone_e164' => '+263771234568'],
                [
                    'phone' => '+263771234568',
                    'is_blocked' => false,
                ]
            );

            $conversation = [];

            // Step 1: Start conversation
            BotFlow::greet($customer);
            $conversation[] = [
                'step' => 'GREET',
                'state' => BotFlow::getState($customer->id),
                'action' => 'Bot sent greeting'
            ];

            // Step 2: User provides name
            $message = ['text' => 'Tapi', 'type' => 'text'];
            BotFlow::identify($customer, $message);
            $conversation[] = [
                'step' => 'IDENTIFY',
                'state' => BotFlow::getState($customer->id),
                'action' => 'User provided name: Tapi'
            ];

            // Step 3: User sends product info
            $message = [
                'text' => 'Samsung Galaxy A15 128GB from Game, R3,499',
                'type' => 'text'
            ];
            BotFlow::captureItem($customer, $message);
            $conversation[] = [
                'step' => 'CAPTURE_ITEM',
                'state' => BotFlow::getState($customer->id),
                'action' => 'User sent product info'
            ];

            // Step 4: Bot processes and prices
            BotFlow::enrichItem($customer);
            $conversation[] = [
                'step' => 'ENRICH_ITEM',
                'state' => BotFlow::getState($customer->id),
                'action' => 'Bot enriched item data'
            ];

            return response()->json([
                'success' => true,
                'message' => 'Conversation simulation completed',
                'conversation_flow' => $conversation,
                'customer' => $customer->fresh(),
                'final_state' => BotFlow::getState($customer->id)
            ]);

        } catch (\Exception $e) {
            Log::error('Conversation simulation failed', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reset test data
     */
    public function resetTestData(Request $request)
    {
        try {
            // Clean up test customers and their data
            $testCustomers = Customer::whereIn('phone_e164', ['+263771234567', '+263771234568'])->get();
            
            foreach ($testCustomers as $customer) {
                // Clear cache
                \Illuminate\Support\Facades\Cache::forget("bot_state_{$customer->id}");
                \Illuminate\Support\Facades\Cache::forget("bot_context_{$customer->id}_current_intake_item");
                \Illuminate\Support\Facades\Cache::forget("bot_context_{$customer->id}_current_pricing");
                
                // Delete related data
                IntakeItem::where('customer_id', $customer->id)->delete();
                
                // Delete customer
                $customer->delete();
            }

            return response()->json([
                'success' => true,
                'message' => 'Test data reset successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
