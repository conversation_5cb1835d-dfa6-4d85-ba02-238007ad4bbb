<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class FileController extends Controller
{
    public function serveUpload($path)
    {
        // Check if file exists in storage/app/public
        $filePath = storage_path('app/public/' . $path);
        
        if (!file_exists($filePath)) {
            // Check if file exists in public/uploads
            $publicPath = public_path('uploads/' . $path);
            if (!file_exists($publicPath)) {
                abort(404, 'File not found');
            }
            $filePath = $publicPath;
        }

        // Create response with file
        $response = response()->file($filePath);
        
        // Add CORS headers
        $response->headers->set('Access-Control-Allow-Origin', '*');
        $response->headers->set('Access-Control-Allow-Methods', 'GET, OPTIONS');
        $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Origin');
        $response->headers->set('Access-Control-Max-Age', '86400');

        return $response;
    }
}
