<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Catalogue;
use App\Models\Shop;
use App\Services\FileUploadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CatalogueController extends Controller
{
    /**
     * List catalogues with pagination
     */
    public function index(Request $request)
    {
        $query = Catalogue::with('shop');
        
        // Filter by shop
        if ($request->has('shop_id')) {
            $query->where('shop_id', $request->shop_id);
        }
        
        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }
        
        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        $catalogues = $query->orderBy('created_at', 'desc')
                           ->paginate($request->get('per_page', 12));
        
        return response()->json([
            'success' => true,
            'data' => $catalogues
        ]);
    }
    
    /**
     * Get single catalogue
     */
    public function show(Catalogue $catalogue)
    {
        $catalogue->load('shop');
        
        return response()->json([
            'success' => true,
            'data' => $catalogue
        ]);
    }
    
    /**
     * Upload new catalogue
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'shop_id' => 'required|exists:shops,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'valid_from' => 'nullable|date',
            'valid_to' => 'nullable|date|after:valid_from',
            'type' => 'required|in:pdf,images',
            'pdf_file' => 'required_if:type,pdf|file|mimes:pdf|max:51200', // 50MB max
            'images' => 'required_if:type,images|array',
            'images.*' => 'image|mimes:jpeg,jpg,png,webp|max:10240', // 10MB per image
            'cover_image' => 'nullable|image|mimes:jpeg,jpg,png,webp|max:5120', // 5MB
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            $shop = Shop::findOrFail($request->shop_id);
            $slug = Str::slug($request->title) . '-' . time();
            
            // Create catalogue record
            $catalogue = new Catalogue();
            $catalogue->shop_id = $shop->id;
            $catalogue->title = $request->title;
            $catalogue->slug = $slug;
            $catalogue->description = $request->description;
            $catalogue->type = $request->type;
            $catalogue->valid_from = $request->valid_from;
            $catalogue->valid_to = $request->valid_to;
            $catalogue->status = 'active';
            
            // Handle PDF upload
            if ($request->type === 'pdf' && $request->hasFile('pdf_file')) {
                $pdfFile = $request->file('pdf_file');
                $pdfPath = FileUploadService::uploadToPublic(
                    $pdfFile,
                    'uploads/catalogues/pdf/' . $shop->slug,
                    $slug . '.pdf'
                );
                $catalogue->pdf_url = $pdfPath;
                $catalogue->file_size = $pdfFile->getSize();
                
                // Extract page count (simplified - you might want to use a PDF library)
                $catalogue->page_count = 1; // Default, update with actual count if needed
            }
            
            // Handle images upload
            if ($request->type === 'images' && $request->hasFile('images')) {
                $imagePaths = [];
                foreach ($request->file('images') as $index => $image) {
                    $imagePath = FileUploadService::uploadToPublic(
                        $image,
                        'uploads/catalogues/images/' . $shop->slug . '/' . $slug,
                        'page_' . ($index + 1) . '.' . $image->getClientOriginalExtension()
                    );
                    $imagePaths[] = $imagePath;
                }
                $catalogue->images = $imagePaths;
                $catalogue->page_count = count($imagePaths);
            }
            
            // Handle cover image
            if ($request->hasFile('cover_image')) {
                $coverImage = $request->file('cover_image');
                $coverPath = FileUploadService::uploadToPublic(
                    $coverImage,
                    'uploads/catalogues/covers/' . $shop->slug,
                    $slug . '_cover.' . $coverImage->getClientOriginalExtension()
                );
                $catalogue->cover_url = $coverPath;
            } elseif ($request->type === 'images' && isset($imagePaths[0])) {
                // Use first image as cover if no cover provided
                $catalogue->cover_url = $imagePaths[0];
            }
            
            $catalogue->save();
            
            return response()->json([
                'success' => true,
                'message' => 'Catalogue uploaded successfully',
                'data' => $catalogue->load('shop')
            ], 201);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to upload catalogue: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Update catalogue
     */
    public function update(Request $request, Catalogue $catalogue)
    {
        
        $validator = Validator::make($request->all(), [
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'valid_from' => 'nullable|date',
            'valid_to' => 'nullable|date|after:valid_from',
            'status' => 'nullable|in:active,inactive,expired',
            'cover_image' => 'nullable|image|mimes:jpeg,jpg,png,webp|max:5120',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            // Update basic fields
            if ($request->has('title')) {
                $catalogue->title = $request->title;
            }
            if ($request->has('description')) {
                $catalogue->description = $request->description;
            }
            if ($request->has('valid_from')) {
                $catalogue->valid_from = $request->valid_from;
            }
            if ($request->has('valid_to')) {
                $catalogue->valid_to = $request->valid_to;
            }
            if ($request->has('status')) {
                $catalogue->status = $request->status;
            }
            
            // Handle cover image update
            if ($request->hasFile('cover_image')) {
                // Delete old cover if exists
                if ($catalogue->cover_url) {
                    FileUploadService::deleteFromPublic($catalogue->cover_url);
                }
                
                $coverImage = $request->file('cover_image');
                $coverPath = FileUploadService::uploadToPublic(
                    $coverImage,
                    'uploads/catalogues/covers/' . $catalogue->shop->slug,
                    $catalogue->slug . '_cover.' . $coverImage->getClientOriginalExtension()
                );
                $catalogue->cover_url = $coverPath;
            }
            
            $catalogue->save();
            
            return response()->json([
                'success' => true,
                'message' => 'Catalogue updated successfully',
                'data' => $catalogue->load('shop')
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to update catalogue: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Delete catalogue
     */
    public function destroy(Catalogue $catalogue)
    {
        try {
            
            // Delete associated files
            if ($catalogue->pdf_url) {
                FileUploadService::deleteFromPublic($catalogue->pdf_url);
            }
            
            if ($catalogue->images) {
                foreach ($catalogue->images as $imagePath) {
                    FileUploadService::deleteFromPublic($imagePath);
                }
            }
            
            if ($catalogue->cover_url && $catalogue->cover_url !== ($catalogue->images[0] ?? null)) {
                FileUploadService::deleteFromPublic($catalogue->cover_url);
            }
            
            $catalogue->delete();
            
            return response()->json([
                'success' => true,
                'message' => 'Catalogue deleted successfully'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to delete catalogue: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get catalogue pages (for image-based catalogues)
     */
    public function getPages(Catalogue $catalogue)
    {
        
        if ($catalogue->type !== 'images') {
            return response()->json([
                'success' => false,
                'error' => 'This catalogue is not image-based'
            ], 400);
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'catalogue_id' => $catalogue->id,
                'title' => $catalogue->title,
                'pages' => $catalogue->images,
                'page_count' => $catalogue->page_count
            ]
        ]);
    }
}