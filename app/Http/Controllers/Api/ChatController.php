<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Conversation;
use App\Models\Customer;
use App\Models\Message;
use App\Services\ConversationService;
use App\Services\GeminiService;
use App\Services\FileUploadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ChatController extends Controller
{
    public function __construct(
        private ConversationService $conversationService,
        private GeminiService $geminiService
    ) {}

    /**
     * Start or get existing conversation
     */
    public function startConversation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'session_id' => 'nullable|string',
            'phone_number' => 'nullable|string',
            'name' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Create or find customer
        $sessionId = $request->input('session_id', Str::uuid());
        $phoneNumber = $request->input('phone_number');
        $name = $request->input('name', 'Web User');

        $customer = null;
        if ($phoneNumber) {
            $customer = Customer::firstOrCreate(
                ['wa_number' => $phoneNumber],
                ['name' => $name]
            );
        }

        // Create or find conversation
        $conversation = Conversation::firstOrCreate([
            'channel' => 'web',
            'customer_id' => $customer?->id,
            'wa_number' => $phoneNumber,
        ], [
            'state' => 'active',
            'last_message_at' => now(),
        ]);

        // Get recent messages
        $messages = Message::where('conversation_id', $conversation->id)
            ->orderBy('created_at', 'asc')
            ->limit(20)
            ->get()
            ->map(function ($message) {
                return [
                    'id' => $message->id,
                    'role' => $message->role,
                    'type' => $message->type,
                    'content' => $message->payload_json['text'] ?? $message->payload_json['body'] ?? '',
                    'timestamp' => $message->created_at->toISOString(),
                    'products' => $message->payload_json['products'] ?? null,
                ];
            });

        return response()->json([
            'success' => true,
            'conversation_id' => $conversation->id,
            'session_id' => $sessionId,
            'messages' => $messages
        ]);
    }

    /**
     * Send message to chat
     */
    public function sendMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'conversation_id' => 'required|integer|exists:conversations,id',
            'message' => 'required|string|max:2000',
            'image' => 'nullable|image|max:10240' // 10MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $conversationId = $request->input('conversation_id');
            $message = $request->input('message');
            $imagePath = null;

            // Handle image upload
            if ($request->hasFile('image')) {
                $image = $request->file('image');
                $filename = 'chat-images/' . time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
                $imagePath = $image->storeAs('', $filename, 'local');
            }

            // Process message with conversation service
            $response = $this->processWebChatMessage($conversationId, $message, $imagePath);

            return response()->json([
                'success' => true,
                'response' => $response
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to process message: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload and analyze image
     */
    public function analyzeImage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|image|max:10240' // 10MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $image = $request->file('image');
            $filename = time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
            $imagePath = FileUploadService::uploadToPublic($image, 'uploads/temp', $filename);

            // Analyze with Gemini Vision
            $analysisResult = $this->geminiService->analyzeProductImage($imagePath);

            // Clean up temporary file
            FileUploadService::deleteFromPublic($imagePath);

            if ($analysisResult['success']) {
                return response()->json([
                    'success' => true,
                    'products' => $analysisResult['products'] ?? [],
                    'analysis' => $analysisResult['raw_text'] ?? '',
                    'catalogue_info' => $analysisResult['catalogue_info'] ?? null
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to analyze image'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to analyze image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get conversation messages
     */
    public function getMessages(Request $request, int $conversationId)
    {
        $conversation = Conversation::findOrFail($conversationId);
        
        $messages = Message::where('conversation_id', $conversation->id)
            ->orderBy('created_at', 'asc')
            ->limit(50)
            ->get()
            ->map(function ($message) {
                return [
                    'id' => $message->id,
                    'role' => $message->role,
                    'type' => $message->type,
                    'content' => $message->payload_json['text'] ?? $message->payload_json['body'] ?? '',
                    'timestamp' => $message->created_at->toISOString(),
                    'products' => $message->payload_json['products'] ?? null,
                ];
            });

        return response()->json([
            'success' => true,
            'messages' => $messages
        ]);
    }

    /**
     * Generate AI response for given prompt
     */
    public function generateResponse(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'prompt' => 'required|string|max:1000',
            'conversation_id' => 'nullable|integer|exists:conversations,id',
            'context' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $prompt = $request->input('prompt');
            $conversationId = $request->input('conversation_id');
            $context = $request->input('context', []);

            // Add conversation context if available
            if ($conversationId) {
                $conversation = Conversation::findOrFail($conversationId);
                $context = array_merge($context, $this->getConversationContext($conversation));
            }

            $response = $this->geminiService->generateText($prompt, $context);

            if ($response['success']) {
                return response()->json([
                    'success' => true,
                    'text' => $response['text'],
                    'usage' => $response['usage'] ?? null
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => $response['error']
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to generate response: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process web chat message using ConversationService
     */
    private function processWebChatMessage(int $conversationId, string $message, ?string $imagePath = null): array
    {
        $conversation = Conversation::findOrFail($conversationId);
        
        // Store user message
        Message::create([
            'conversation_id' => $conversation->id,
            'role' => 'user',
            'type' => $imagePath ? 'image' : 'text',
            'payload_json' => [
                'text' => $message,
                'image_path' => $imagePath,
            ],
        ]);

        // Process with AI
        if ($imagePath) {
            $response = $this->processImageMessage($conversation, $message, $imagePath);
        } else {
            $response = $this->processTextMessage($conversation, $message);
        }
        
        if ($response) {
            // Store assistant message
            Message::create([
                'conversation_id' => $conversation->id,
                'role' => 'assistant',
                'type' => 'text',
                'payload_json' => $response,
            ]);
        }

        // Update conversation timestamp
        $conversation->update(['last_message_at' => now()]);

        return $response ?: [
            'text' => 'I apologize, but I encountered an error processing your message. Please try again.',
            'timestamp' => now()->toISOString()
        ];
    }

    private function processTextMessage(Conversation $conversation, string $text): array
    {
        $context = $this->getConversationContext($conversation);
        
        // Use Gemini AI for response
        $aiResponse = $this->geminiService->generateText($text, $context);
        
        if ($aiResponse['success']) {
            return [
                'text' => $aiResponse['text'],
                'timestamp' => now()->toISOString(),
                'ai_usage' => $aiResponse['usage'] ?? null
            ];
        }

        // Fallback response
        return [
            'text' => 'I can help you find products from South African stores. What are you looking for?',
            'timestamp' => now()->toISOString()
        ];
    }

    private function processImageMessage(Conversation $conversation, string $text, string $imagePath): array
    {
        // Analyze image with Gemini Vision
        $analysisResult = $this->geminiService->analyzeProductImage($imagePath);
        
        if ($analysisResult['success']) {
            $products = $analysisResult['products'];
            $responseText = "I can see " . count($products) . " product(s) in your image:\n\n";
            
            foreach ($products as $index => $product) {
                $responseText .= ($index + 1) . ". **" . ($product['name'] ?? 'Unknown Product') . "**\n";
                
                if (!empty($product['brand'])) {
                    $responseText .= "   Brand: " . $product['brand'] . "\n";
                }
                
                if (!empty($product['estimated_price_zar'])) {
                    $responseText .= "   ZAR Price: R" . number_format($product['estimated_price_zar'], 2) . "\n";
                }
                
                $responseText .= "   Confidence: " . ($product['confidence'] ?? 50) . "%\n\n";
            }
            
            $responseText .= "Would you like me to find exact pricing for any of these products?";
            
            return [
                'text' => $responseText,
                'products' => $products,
                'image_analysis' => $analysisResult,
                'timestamp' => now()->toISOString()
            ];
        }
        
        return [
            'text' => 'I can see you\'ve shared an image, but I\'m having trouble analyzing it right now. Could you describe what product you\'re looking for?',
            'timestamp' => now()->toISOString()
        ];
    }

    private function getConversationContext(Conversation $conversation): array
    {
        $recentMessages = Message::where('conversation_id', $conversation->id)
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get()
            ->reverse()
            ->map(function ($message) {
                $role = $message->role === 'user' ? 'User' : 'Assistant';
                return $role . ': ' . ($message->payload_json['text'] ?? '');
            })
            ->toArray();

        return [
            'conversation_history' => $recentMessages,
            'customer_number' => $conversation->wa_number ?? 'web_user',
            'conversation_state' => $conversation->state
        ];
    }
}