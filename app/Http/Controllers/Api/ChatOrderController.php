<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ChatOrderService;
use App\Services\LandingCostCalculatorService;
use App\Services\ConversationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ChatOrderController extends Controller
{
    private $chatOrderService;
    private $conversationService;

    public function __construct(
        ChatOrderService $chatOrderService,
        ConversationService $conversationService
    ) {
        $this->chatOrderService = $chatOrderService;
        $this->conversationService = $conversationService;
    }

    public function webhook(Request $request)
    {
        \Illuminate\Support\Facades\Log::info('🔴 CHAT ORDER webhook received', $request->all());

        // Handle WhatsApp webhook verification
        if ($request->has('hub.verify_token')) {
            $verifyToken = config('services.whatsapp.verify_token', 'youzeafrika_verify');
            if ($request->input('hub.verify_token') === $verifyToken) {
                return response($request->input('hub.challenge'));
            }
            return response('Invalid verify token', 403);
        }

        // Process incoming message using improved ConversationService
        $data = $request->all();

        if (isset($data['entry'][0]['changes'][0]['value']['messages'][0])) {
            $message = $data['entry'][0]['changes'][0]['value']['messages'][0];
            $from = $message['from'];
            $messageBody = $message['text']['body'] ?? '';
            $messageType = $message['type'];

            \Illuminate\Support\Facades\Log::info('🔴 Processing message via improved ConversationService', [
                'from' => $from,
                'body' => $messageBody,
                'type' => $messageType
            ]);

            // Use our improved ConversationService instead of ChatOrderService
            $this->conversationService->processIncomingMessage(
                $from,
                $messageBody,
                null, // no media URL for now
                $messageType === 'text' ? null : $messageType
            );
        }
        
        return response()->json(['status' => 'ok']);
    }

    public function processMessage(Request $request)
    {
        $request->validate([
            'session_id' => 'required|string',
            'message' => 'required_without:image|string',
            'image' => 'required_without:message|file|image',
            'channel' => 'string|in:whatsapp,telegram,chat',
            'customer_phone' => 'nullable|string',
        ]);

        $imagePath = null;
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('chat-images', 'public');
        }

        $response = $this->chatOrderService->processMessage(
            $request->session_id,
            $request->message ?? '',
            $imagePath ? Storage::disk('public')->path($imagePath) : null
        );

        return response()->json($response);
    }

    public function getSession(Request $request, $sessionId)
    {
        $chatOrder = $this->chatOrderService->getOrCreateSession($sessionId);
        
        return response()->json([
            'session' => $chatOrder,
            'items' => $chatOrder->items,
            'breakdown' => $chatOrder->landing_cost_breakdown,
        ]);
    }

    public function addItem(Request $request, $sessionId)
    {
        $request->validate([
            'product' => 'required|array',
            'quantity' => 'integer|min:1',
        ]);

        $chatOrder = $this->chatOrderService->getOrCreateSession($sessionId);
        $item = $chatOrder->addItem($request->product, $request->quantity ?? 1);
        
        return response()->json([
            'message' => 'Item added successfully',
            'item' => $item,
            'cart' => $chatOrder->items,
        ]);
    }

    public function removeItem(Request $request, $sessionId, $itemId)
    {
        $chatOrder = $this->chatOrderService->getOrCreateSession($sessionId);
        $chatOrder->removeItem($itemId);
        
        return response()->json([
            'message' => 'Item removed successfully',
            'cart' => $chatOrder->items,
        ]);
    }

    public function calculateCost($sessionId)
    {
        $chatOrder = $this->chatOrderService->getOrCreateSession($sessionId);
        $breakdown = app(LandingCostCalculatorService::class)->calculateForChatOrder($chatOrder);
        
        return response()->json([
            'breakdown' => $breakdown,
            'formatted' => app(LandingCostCalculatorService::class)->formatBreakdownForChat($breakdown),
        ]);
    }

    public function confirmOrder(Request $request, $sessionId)
    {
        $request->validate([
            'customer_name' => 'nullable|string',
            'customer_phone' => 'nullable|string',
        ]);

        $chatOrder = $this->chatOrderService->getOrCreateSession($sessionId);
        
        if ($request->customer_name) {
            $chatOrder->update(['customer_name' => $request->customer_name]);
        }
        
        if ($request->customer_phone) {
            $chatOrder->update(['customer_phone' => $request->customer_phone]);
        }
        
        $response = $this->chatOrderService->processMessage($sessionId, 'confirm');
        
        return response()->json($response);
    }

    private function downloadWhatsAppImage($mediaId)
    {
        // This would download the image from WhatsApp
        // Implementation depends on WhatsApp Business API setup
        
        // For now, return a placeholder
        return null;
    }

    private function sendWhatsAppMessage($to, $response)
    {
        // Send message via WhatsApp Business API
        // Implementation depends on provider (Twilio, WhatsApp Cloud API, etc.)
        
        $token = config('services.whatsapp.token');
        $phoneId = config('services.whatsapp.phone_id');
        
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'text',
            'text' => [
                'body' => $response['message'],
            ],
        ];
        
        // If there are options, add them as interactive buttons
        if (isset($response['options']) && !empty($response['options'])) {
            $payload['type'] = 'interactive';
            $payload['interactive'] = [
                'type' => 'button',
                'body' => [
                    'text' => $response['message'],
                ],
                'action' => [
                    'buttons' => array_map(function($option, $index) {
                        return [
                            'type' => 'reply',
                            'reply' => [
                                'id' => 'option_' . $index,
                                'title' => substr($option, 0, 20), // WhatsApp button limit
                            ],
                        ];
                    }, $response['options'], array_keys($response['options'])),
                ],
            ];
        }
        
        // Make HTTP request to WhatsApp API
        // Http::withToken($token)->post("https://graph.facebook.com/v17.0/{$phoneId}/messages", $payload);
    }
}