<?php

namespace App\Http\Controllers;

use App\Services\WhatsAppService;
use App\Services\ConversationService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class WhatsAppWebhookController extends Controller
{
    protected WhatsAppService $whatsappService;
    protected ConversationService $conversationService;

    public function __construct(
        WhatsAppService $whatsappService,
        ConversationService $conversationService
    ) {
        $this->whatsappService = $whatsappService;
        $this->conversationService = $conversationService;
    }

    public function verify(Request $request): Response|string
    {
        $mode = $request->query('hub_mode');
        $token = $request->query('hub_verify_token');
        $challenge = $request->query('hub_challenge');

        Log::info('WhatsApp webhook verification attempt', [
            'mode' => $mode,
            'token' => $token,
            'challenge' => $challenge
        ]);

        $result = $this->whatsappService->verifyWebhook($token, $challenge, $mode);

        if ($result !== false) {
            return response($result, 200);
        }

        return response('Forbidden', 403);
    }

    public function handle(Request $request): Response
    {
        try {
            $data = $request->all();

            Log::info('WhatsApp webhook received', [
                'data' => $data,
                'headers' => $request->headers->all()
            ]);

            $result = $this->whatsappService->processWebhookMessage($data);

            if ($result['success']) {
                // Here you can add your business logic
                // For example, process the incoming messages
                foreach ($result['messages'] as $message) {
                    $this->handleIncomingMessage($message);
                }
            }

            return response('OK', 200);

        } catch (\Exception $e) {
            Log::error('WhatsApp webhook error: ' . $e->getMessage(), [
                'request_data' => $request->all()
            ]);

            return response('Internal Server Error', 500);
        }
    }

    protected function handleIncomingMessage(array $message): void
    {
        Log::info('Processing incoming WhatsApp message', $message);

        $from = $message['from'];

        // Process through ConversationService for comprehensive handling
        try {
            switch ($message['type']) {
                case 'text':
                    $this->handleTextMessage($message);
                    break;
                case 'image':
                    $this->handleImageMessage($message);
                    break;
                case 'document':
                case 'audio':
                case 'video':
                    $this->handleMediaMessage($message);
                    break;
                default:
                    Log::info('Unhandled message type: ' . $message['type']);
                    $this->whatsappService->sendMessage(
                        $from,
                        "I received your message. How can I help you find products or get pricing today?"
                    );
            }
        } catch (\Exception $e) {
            Log::error('Error handling WhatsApp message', [
                'error' => $e->getMessage(),
                'message' => $message
            ]);

            // Send friendly error message
            $this->whatsappService->sendMessage(
                $from,
                "I'm having a moment. Please try again or type 'help' for assistance."
            );
        }
    }

    protected function handleTextMessage(array $message): void
    {
        $from = $message['from'];
        $text = $message['text'];
        $phoneNumber = 'whatsapp:' . $from;

        Log::info('Handling text message via ConversationService', [
            'from' => $from,
            'text' => $text
        ]);

        // Use the comprehensive ConversationService
        try {
            $this->conversationService->processIncomingMessage(
                $phoneNumber,
                $text,
                null, // no media URL
                null  // no media type
            );
        } catch (\Exception $e) {
            Log::error('ConversationService error: ' . $e->getMessage());

            // Fallback to basic response
            $this->basicResponse($from, $text);
        }
    }

    protected function handleImageMessage(array $message): void
    {
        $from = $message['from'];
        $image = $message['image'];
        $phoneNumber = 'whatsapp:' . $from;

        Log::info('Handling image message', [
            'from' => $from,
            'image_id' => $image['id'] ?? null
        ]);

        // Process image through ConversationService
        $mediaUrl = $image['url'] ?? null;
        $this->conversationService->processIncomingMessage(
            $phoneNumber,
            'User sent an image',
            $mediaUrl,
            'image'
        );
    }

    protected function handleMediaMessage(array $message): void
    {
        $from = $message['from'];
        $type = $message['type'];

        Log::info('Handling media message', [
            'from' => $from,
            'type' => $type
        ]);

        $this->whatsappService->sendMessage(
            $from,
            "I received your {$type}. You can ask me about products, pricing, or place an order. How can I help?"
        );
    }

    protected function basicResponse(string $from, string $text): void
    {
        $lowerText = strtolower($text);

        if (str_contains($lowerText, 'hello') || str_contains($lowerText, 'hi')) {
            $this->whatsappService->sendMessage(
                $from,
                'Hello! Welcome to Youzeafrika. 🇿🇼\n\nI can help you:\n• Find products\n• Get pricing with delivery\n• Place orders\n\nWhat are you looking for today?'
            );
        } elseif (str_contains($lowerText, 'help')) {
            $this->whatsappService->sendMessage(
                $from,
                "Here's how I can help:\n\n📱 *Products*: Ask about any electronics\n💵 *Pricing*: Get quotes with delivery\n📦 *Orders*: Place and track orders\n👤 *Support*: Type 'agent' for human help\n\nExample: 'Samsung TV prices' or 'iPhone 15'"
            );
        } elseif (str_contains($lowerText, 'agent') || str_contains($lowerText, 'human')) {
            $this->whatsappService->sendMessage(
                $from,
                "I'll connect you with a human agent. Meanwhile, feel free to continue browsing products!"
            );
            // TODO: Trigger escalation
        } else {
            // For any other message, try to be helpful
            $this->whatsappService->sendMessage(
                $from,
                "I can help you find '{$text}'. Let me check our products...\n\nMeanwhile, you can:\n• Ask for specific products\n• Request pricing\n• Say 'help' for more options"
            );
        }
    }
}