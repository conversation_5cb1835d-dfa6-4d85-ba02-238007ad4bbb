<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Services\WhatsAppService;
use App\Services\ConversationService;
use App\Services\AI\ConversationEngine;
use App\Services\Escalation\EscalationHandler;
use App\Jobs\ProcessWhatsAppMessage;

class WhatsAppController extends Controller
{
    protected $whatsAppService;
    protected $conversationService;
    protected $conversationEngine;
    protected $escalationHandler;

    public function __construct(
        WhatsAppService $whatsAppService,
        ConversationService $conversationService,
        ConversationEngine $conversationEngine,
        EscalationHandler $escalationHandler
    ) {
        $this->whatsAppService = $whatsAppService;
        $this->conversationService = $conversationService;
        $this->conversationEngine = $conversationEngine;
        $this->escalationHandler = $escalationHandler;
    }

    public function webhook(Request $request)
    {
        Log::info('🔵 REGULAR WhatsApp webhook received', $request->all());

        // Verify webhook (Twilio signature verification)
        if (!$this->verifyWebhook($request)) {
            return response('Unauthorized', 401);
        }

        // Extract message data from Twilio
        $from = $request->input('From'); // e.g., whatsapp:+263777123456
        $to = $request->input('To'); // e.g., whatsapp:+1234567890
        $body = $request->input('Body');
        $mediaUrl = $request->input('MediaUrl0'); // For images
        $mediaContentType = $request->input('MediaContentType0');

        // Clean phone number (remove whatsapp: prefix)
        $phoneNumber = str_replace('whatsapp:', '', $from);

        try {
            // Process the message
            $this->conversationService->processIncomingMessage(
                $phoneNumber,
                $body,
                $mediaUrl,
                $mediaContentType
            );

            return response()->xml('<Response></Response>');
        } catch (\Exception $e) {
            Log::error('Error processing WhatsApp message', [
                'phone' => $phoneNumber,
                'body' => $body,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Don't send error message to user in case that's causing the From/To issue
            // Just log and return 200 to prevent Twilio retries
            
            return response()->xml('<Response></Response>');
        }
    }

    protected function verifyWebhook(Request $request): bool
    {
        // In sandbox/development, skip verification
        // Twilio Sandbox doesn't always send reliable signatures
        if (app()->environment(['local', 'development']) || config('services.twilio.skip_verification', false)) {
            return true;
        }

        $twilioSignature = $request->header('X-Twilio-Signature');
        $url = $request->fullUrl();
        $data = $request->all();

        // Log for debugging
        Log::info('Twilio webhook verification', [
            'signature' => $twilioSignature,
            'url' => $url,
            'has_data' => !empty($data)
        ]);

        return $this->whatsAppService->validateSignature($twilioSignature, $url, $data);
    }

    /**
     * Enhanced webhook method using new AI engine
     */
    public function webhookAI(Request $request)
    {
        Log::info('🤖 AI-enhanced WhatsApp webhook received', [
            'from' => $request->input('From'),
            'message_sid' => $request->input('MessageSid')
        ]);

        if (!$this->verifyWebhook($request)) {
            return response('Unauthorized', 401);
        }

        // Dispatch to queue for async AI processing
        ProcessWhatsAppMessage::dispatch($request->all())
            ->onQueue('whatsapp-ai')
            ->delay(now()->addSeconds(1));

        Log::info('WhatsApp message queued for AI processing');
        return response()->xml('<Response></Response>');
    }

    /**
     * Get escalation queue for agents
     */
    public function getEscalationQueue(Request $request)
    {
        $urgency = $request->get('urgency', 'all');
        $queue = $this->escalationHandler->getEscalationQueue($urgency);

        return response()->json([
            'success' => true,
            'queue' => $queue,
            'count' => count($queue)
        ]);
    }

    /**
     * Resolve an escalation
     */
    public function resolveEscalation(Request $request, int $conversationId)
    {
        $resolution = $request->get('resolution', 'resolved');
        $success = $this->escalationHandler->resolveEscalation($conversationId, $resolution);

        return response()->json([
            'success' => $success,
            'message' => $success ? 'Escalation resolved' : 'Failed to resolve escalation'
        ]);
    }
}
