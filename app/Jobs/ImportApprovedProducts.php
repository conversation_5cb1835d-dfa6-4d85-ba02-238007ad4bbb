<?php

namespace App\Jobs;

use App\Models\CatalogueIngestion;
use App\Services\CatalogueIngestionService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ImportApprovedProducts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The maximum number of attempts for this job
     */
    public $tries = 3;

    /**
     * The maximum time (in seconds) the job can run
     */
    public $timeout = 1800; // 30 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(
        public CatalogueIngestion $ingestion
    ) {
        $this->onQueue('import');
    }

    /**
     * Execute the job.
     */
    public function handle(CatalogueIngestionService $ingestionService): void
    {
        Log::info('Starting approved products import', [
            'ingestion_id' => $this->ingestion->id,
        ]);

        try {
            $imported = $ingestionService->importApprovedProducts($this->ingestion);

            Log::info('Products imported successfully', [
                'ingestion_id' => $this->ingestion->id,
                'products_imported' => $imported,
            ]);

        } catch (\Exception $e) {
            Log::error('Product import failed', [
                'ingestion_id' => $this->ingestion->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Product import job failed permanently', [
            'ingestion_id' => $this->ingestion->id,
            'error' => $exception->getMessage(),
        ]);
    }
}