<?php

namespace App\Jobs;

use App\Services\AI\ConversationEngine;
use App\Services\WhatsAppService;
use App\Services\Escalation\EscalationHandler;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessWhatsAppMessage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 60;
    public $maxExceptions = 3;

    public function __construct(
        private array $webhookData
    ) {}

    public function handle(
        ConversationEngine $conversationEngine,
        WhatsAppService $whatsAppService,
        EscalationHandler $escalationHandler
    ): void {
        try {
            // Extract message data from Twilio webhook
            $message = $this->extractMessage($this->webhookData);

            if (!$message) {
                Log::warning('No valid message found in webhook data', $this->webhookData);
                return;
            }

            Log::info('Processing WhatsApp message', [
                'from' => $message['from'],
                'type' => $message['type'],
                'message_id' => $message['id'] ?? 'unknown'
            ]);

            // Process message through AI conversation engine
            $result = $conversationEngine->processMessage(
                $message['from'],
                $message['text'],
                $message['media'] ?? null
            );

            if (!$result['success']) {
                throw new \Exception('Conversation engine failed: ' . ($result['error'] ?? 'Unknown error'));
            }

            // Check if escalation is needed
            if ($result['requires_human'] || !empty($result['response']['escalate'])) {
                $this->handleEscalation(
                    $escalationHandler,
                    $message['from'],
                    $message['text'],
                    $result['response']['escalation_reason'] ?? 'System determined human assistance needed'
                );
            } else {
                // Send AI response back via WhatsApp
                $this->sendResponse($whatsAppService, $message['from'], $result['response']);
            }

            Log::info('WhatsApp message processed successfully', [
                'from' => $message['from'],
                'intent' => $result['intent'] ?? 'unknown',
                'confidence' => $result['confidence'] ?? 0,
                'escalated' => $result['requires_human'] ?? false
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process WhatsApp message', [
                'webhook_data' => $this->webhookData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Send fallback message to user
            $this->sendFallbackMessage($whatsAppService, $this->webhookData);

            throw $e; // Re-throw to trigger retry mechanism
        }
    }

    private function extractMessage(array $data): ?array
    {
        // Extract message data from Twilio webhook format
        $from = $data['From'] ?? null;
        $to = $data['To'] ?? null;
        $body = $data['Body'] ?? null;
        $messageId = $data['MessageSid'] ?? null;
        $mediaUrl = $data['MediaUrl0'] ?? null;
        $mediaContentType = $data['MediaContentType0'] ?? null;

        if (!$from) {
            return null;
        }

        // Clean phone number (remove whatsapp: prefix)
        $phoneNumber = str_replace('whatsapp:', '', $from);

        $extracted = [
            'id' => $messageId,
            'from' => $phoneNumber,
            'to' => str_replace('whatsapp:', '', $to ?? ''),
            'text' => $body ?? '',
            'type' => $mediaUrl ? 'media' : 'text',
            'timestamp' => now()->toISOString()
        ];

        // Extract media information
        if ($mediaUrl) {
            $extracted['media'] = [
                'type' => $this->getMediaType($mediaContentType),
                'url' => $mediaUrl,
                'content_type' => $mediaContentType
            ];
        }

        return $extracted;
    }

    private function getMediaType(?string $contentType): string
    {
        if (!$contentType) {
            return 'unknown';
        }

        if (str_starts_with($contentType, 'image/')) {
            return 'image';
        }

        if (str_starts_with($contentType, 'audio/')) {
            return 'audio';
        }

        if (str_starts_with($contentType, 'video/')) {
            return 'video';
        }

        if (str_starts_with($contentType, 'application/pdf')) {
            return 'document';
        }

        return 'file';
    }

    private function sendResponse(WhatsAppService $whatsAppService, string $to, array $response): void
    {
        if (empty($response['text'])) {
            return;
        }

        // Send the main message
        $success = $whatsAppService->sendMessage($to, $response['text']);

        if (!$success) {
            Log::error('Failed to send WhatsApp response', [
                'to' => $to,
                'message' => $response['text']
            ]);
        }

        // Handle interactive buttons if present
        if (!empty($response['buttons'])) {
            // Note: Twilio WhatsApp doesn't support interactive buttons like Facebook
            // We could send follow-up messages with numbered options instead
            $this->sendButtonAlternatives($whatsAppService, $to, $response['buttons']);
        }

        // Handle media if present
        if (!empty($response['media_url'])) {
            $whatsAppService->sendMessage(
                $to,
                $response['media_caption'] ?? 'Here\'s more information:',
                [$response['media_url']]
            );
        }
    }

    private function sendButtonAlternatives(WhatsAppService $whatsAppService, string $to, array $buttons): void
    {
        if (empty($buttons)) {
            return;
        }

        $buttonText = "\n💡 *Quick Options:*\n";
        foreach ($buttons as $index => $button) {
            $buttonText .= "• Reply '" . ($index + 1) . "' for " . $button['title'] . "\n";
        }

        $whatsAppService->sendMessage($to, $buttonText);
    }

    private function handleEscalation(
        EscalationHandler $escalationHandler,
        string $phoneNumber,
        string $userMessage,
        string $reason
    ): void {
        try {
            // This would find the conversation and escalate it
            $conversation = \App\Models\Conversation::where('wa_number', $phoneNumber)
                ->latest()
                ->first();

            if ($conversation) {
                $escalationData = [
                    'should_escalate' => true,
                    'reasons' => [$reason],
                    'urgency' => 'medium',
                    'confidence' => 0.8
                ];

                $escalationHandler->escalateConversation(
                    $conversation,
                    $escalationData,
                    $userMessage
                );
            }
        } catch (\Exception $e) {
            Log::error('Escalation handling failed', [
                'phone_number' => $phoneNumber,
                'reason' => $reason,
                'error' => $e->getMessage()
            ]);
        }
    }

    private function sendFallbackMessage(WhatsAppService $whatsAppService, array $webhookData): void
    {
        try {
            $from = $webhookData['From'] ?? null;
            if ($from) {
                $phoneNumber = str_replace('whatsapp:', '', $from);
                $fallbackMessage = "I apologize, but I'm experiencing technical difficulties. " .
                                 "Please try again in a moment, or type 'AGENT' to speak with our team directly.";

                $whatsAppService->sendMessage($phoneNumber, $fallbackMessage);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send fallback message', [
                'webhook_data' => $webhookData,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('ProcessWhatsAppMessage job failed permanently', [
            'webhook_data' => $this->webhookData,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        // Send a final fallback message if possible
        try {
            $whatsAppService = app(WhatsAppService::class);
            $this->sendFallbackMessage($whatsAppService, $this->webhookData);
        } catch (\Exception $e) {
            Log::error('Failed to send final fallback message', [
                'error' => $e->getMessage()
            ]);
        }
    }

    public function retryUntil(): \DateTime
    {
        return now()->addMinutes(10);
    }
}