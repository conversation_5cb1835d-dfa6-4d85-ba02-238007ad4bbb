<?php

namespace App\Jobs;

use App\Models\CatalogueIngestion;
use App\Services\CatalogueIngestionService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessCatalogueIngestion implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The maximum number of attempts for this job
     */
    public $tries = 3;

    /**
     * The maximum time (in seconds) the job can run
     */
    public $timeout = 3600; // 1 hour

    /**
     * Create a new job instance.
     */
    public function __construct(
        public CatalogueIngestion $ingestion
    ) {
        // Set queue based on priority or type
        $this->onQueue('ingestion');
    }

    /**
     * Execute the job.
     */
    public function handle(CatalogueIngestionService $ingestionService): void
    {
        Log::info('Starting catalogue ingestion processing', [
            'ingestion_id' => $this->ingestion->id,
            'catalogue_id' => $this->ingestion->catalogue_id,
        ]);

        try {
            // Process the catalogue
            $ingestionService->processCatalogue($this->ingestion);

            Log::info('Catalogue ingestion completed successfully', [
                'ingestion_id' => $this->ingestion->id,
                'products_found' => $this->ingestion->products_found,
            ]);

        } catch (\Exception $e) {
            Log::error('Catalogue ingestion processing failed', [
                'ingestion_id' => $this->ingestion->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Re-throw to trigger job failure handling
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Catalogue ingestion job failed permanently', [
            'ingestion_id' => $this->ingestion->id,
            'error' => $exception->getMessage(),
        ]);

        // Mark ingestion as failed
        $this->ingestion->markAsFailed($exception->getMessage());
    }
}