<?php

namespace App\Jobs;

use App\Models\FollowUp;
use App\Services\NotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessFollowUps implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle(NotificationService $notificationService): void
    {
        $dueFollowUps = FollowUp::where('status', 'pending')
            ->where('scheduled_at', '<=', now())
            ->with(['followable', 'assignedTo'])
            ->get();

        foreach ($dueFollowUps as $followUp) {
            try {
                $this->processFollowUp($followUp, $notificationService);
            } catch (\Exception $e) {
                Log::error('Failed to process follow-up', [
                    'follow_up_id' => $followUp->id,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    private function processFollowUp(FollowUp $followUp, NotificationService $notificationService): void
    {
        $followable = $followUp->followable;
        
        if (!$followable) {
            $followUp->update(['status' => 'cancelled']);
            return;
        }

        switch ($followUp->type) {
            case 'whatsapp':
                $this->sendWhatsAppFollowUp($followable, $followUp, $notificationService);
                break;
                
            case 'call':
                $this->scheduleCallReminder($followUp);
                break;
                
            case 'email':
                $this->sendEmailFollowUp($followable, $followUp, $notificationService);
                break;
                
            case 'quote_followup':
                $this->sendQuoteFollowUp($followable, $followUp, $notificationService);
                break;
        }

        // Mark as completed
        $followUp->complete('Automated follow-up processed');
    }

    private function sendWhatsAppFollowUp($followable, FollowUp $followUp, NotificationService $notificationService): void
    {
        $message = match(get_class($followable)) {
            \App\Models\Lead::class => $this->getLeadFollowUpMessage($followable, $followUp),
            \App\Models\Order::class => $this->getOrderFollowUpMessage($followable, $followUp),
            default => $followUp->title . "\n\n" . ($followUp->notes ?: 'We wanted to follow up with you. How can we help?')
        };

        $notificationService->sendLeadFollowUp($followable, $message);
    }

    private function getLeadFollowUpMessage(\App\Models\Lead $lead, FollowUp $followUp): string
    {
        $name = $lead->name ? explode(' ', $lead->name)[0] : 'there';
        
        return "👋 Hi {$name}!\n\n" .
               "I wanted to follow up on your interest in our South African products.\n\n" .
               "We have some great deals right now and I'd love to help you find exactly what you're looking for.\n\n" .
               "What products are you most interested in?\n\n" .
               "Or reply 'agent' to speak with our team directly.";
    }

    private function getOrderFollowUpMessage(\App\Models\Order $order, FollowUp $followUp): string
    {
        return "📋 Order #{$order->id} Update\n\n" .
               "We wanted to check in about your recent order.\n\n" .
               "Current Status: " . ucfirst($order->status) . "\n\n" .
               "Any questions? Reply 'agent' to speak with our team.";
    }

    private function scheduleCallReminder(FollowUp $followUp): void
    {
        // In a real implementation, this could integrate with a calling system
        // or send notifications to sales team
        Log::info('Call reminder scheduled', [
            'follow_up_id' => $followUp->id,
            'assigned_to' => $followUp->assigned_to
        ]);
    }

    private function sendEmailFollowUp($followable, FollowUp $followUp, NotificationService $notificationService): void
    {
        // Email follow-up implementation
        Log::info('Email follow-up scheduled', [
            'follow_up_id' => $followUp->id,
            'followable_type' => get_class($followable)
        ]);
    }

    private function sendQuoteFollowUp($followable, FollowUp $followUp, NotificationService $notificationService): void
    {
        if ($followable instanceof \App\Models\Lead) {
            $message = "💰 *Quote Follow-up*\n\n" .
                      "Hi! I wanted to follow up on the pricing quote we provided.\n\n" .
                      "Do you have any questions about the pricing?\n" .
                      "Ready to move forward with your order?\n\n" .
                      "Reply:\n" .
                      "• 'yes' if you're ready to order\n" .
                      "• 'questions' if you need clarification\n" .
                      "• 'not now' if you need more time";

            $notificationService->sendLeadFollowUp($followable, $message);
        }
    }
}