Got it. Let’s capture the flow and rubrics from these WhatsApp conversations and then translate them into a technical development guideline for building a conversational AI to handle product discovery and quotations for YouzeAfrika.

⸻

📘 Conversational AI Development Guideline for YouzeAfrika

1. Business Context

YouzeAfrika helps Zimbabwean customers buy goods from South African retailers (e.g., Game, Makro, Evetech, etc.).
Customers typically:
	•	Share a product reference (text, link, screenshot, or catalog image).
	•	Ask for a quotation/landing price.
	•	Clarify what “landing cost” means.
	•	Provide delivery and contact details once they confirm interest.
	•	Make payments via Ecocash, USD cash, or bank transfer.
	•	Expect human confirmation before finalizing orders.

The AI’s job: automate quotation and engagement, while escalating to human agents when:
	•	Customer confirms an order.
	•	Special requests (bulk, fragile, unusual products).
	•	Payment confirmation is needed.

⸻

2. Core Conversation Flow (AI Rubric)

Step 1: Greeting & Context
	•	Detect when a customer initiates chat (from ad, catalog, or message).
	•	Greet warmly, introduce service, and explain process briefly.

AI Example:
“👋 Hello! Welcome to YouzeAfrika. Please share the product you’d like a quote for — a screenshot, link, or name is fine.”

⸻

Step 2: Product Capture
	•	Input formats the AI should recognize:
	•	Screenshot/Image of product/catalog.
	•	Text description (“Defy 30L Solo Microwave”).
	•	Link to retailer page.

AI Tasks:
	•	OCR/parse images for text (brand, model, price).
	•	Extract product name + price from text or link.
	•	If catalog image: allow customer to circle/highlight or describe item.
	•	Confirm product with user.

⸻

Step 3: Price Processing
	•	Apply business rules for quotation:
	1.	Convert South African Rand → USD (using official/bank rate).
	2.	Add markup (50%).
	3.	Add shipping & handling (fixed or variable).
	4.	Output Landing Cost (Harare).

AI Example:
“✅ Got it! The Defy 30L Solo Microwave is R1399. After conversion and shipping, your landing cost in Harare will be $123 USD.”

⸻

Step 4: Customer Clarification
	•	Customers often ask:
	•	“What does landing cost mean?”
	•	“Is that including shipping?”
	•	“Can I pay later/half deposit?”

AI must:
	•	Detect FAQs and auto-reply with short, clear answers.
	•	Provide payment options and deposit terms.

⸻

Step 5: Order Pre-Confirmation
	•	AI collects structured order details:
	•	Full Name
	•	Contact & Alternative Number
	•	Delivery Address
	•	Email (optional)

AI Example:
“To confirm your order 😊 please provide:
	•	Name
	•	Contact number
	•	Alternative contact
	•	Address
	•	Email (optional)”

⸻

Step 6: Handover to Human
	•	When customer submits details OR confirms purchase intent, AI escalates:
	•	Flag conversation as “HOT LEAD”.
	•	Notify human agent via CRM/WhatsApp API.
	•	Provide structured summary (product, landing cost, customer details).

⸻

Step 7: Post-Order & Payment Handling
	•	AI can share payment options: Ecocash, bank transfer, cash at office.
	•	Do not process payments directly — escalate to human once customer asks for account/number.
	•	After payment, human verifies proof of payment (POP).

⸻

3. Technical Architecture

3.1 Frontend (Customer Interaction)
	•	WhatsApp Business API (primary channel).
	•	Facebook/Instagram Messenger integration.
	•	Optional web chat widget.

3.2 AI/NLP Engine
	•	Use Dialogflow CX / Rasa / LangChain with LLM backend.
	•	Capabilities:
	•	OCR for images (Google Vision / Tesseract).
	•	Text parsing for product names + prices.
	•	Entity extraction: {ProductName, PriceZAR, Retailer, LandingCostUSD}.
	•	FAQ intent detection.

3.3 Pricing Engine
	•	Inputs:
	•	Retailer Price (ZAR).
	•	Exchange Rate (API or manual update).
	•	Markup % (configurable).
	•	Shipping rules.
	•	Outputs:
	•	Landing Cost (USD).
	•	Breakdown (optional).

⸻

4. Escalation Logic
	•	AI handles: greetings, product capture, quotations, FAQs, order detail collection.
	•	Human handles: payment instructions, proof verification, delivery logistics.

⸻

5. Data Models

Customer
	•	Name
	•	Primary Contact
	•	Alternative Contact
	•	Address
	•	Email

Product
	•	Name
	•	Retailer
	•	Price (ZAR)
	•	Catalog Reference (optional)

Quotation
	•	Conversion Rate
	•	Markup %
	•	Shipping Cost
	•	Final Landing Price (USD)

⸻

6. Implementation Roadmap
	1.	Phase 1 – Automation Foundation
	•	WhatsApp Business API integration.
	•	AI conversation for product capture → quotation.
	•	FAQ automation.
	2.	Phase 2 – Order Structuring
	•	Collect structured customer details.
	•	Generate automated quotation summary.
	•	Escalate to human for payment.
	3.	Phase 3 – Advanced Automation
	•	OCR parsing for catalog images.
	•	Multi-product/cart handling.
	•	CRM dashboard for agents (leads + orders).

⸻

7. KPIs for Success
	•	% of inquiries answered fully by AI.
	•	Average response time.
	•	Lead conversion rate (inquiries → orders).
	•	Reduced agent workload (only final confirmations).

⸻

✅ With this system, AI filters out all discovery and quoting workload, leaving humans to focus only on high-value finalization (payment, delivery, special requests).

⸻

Would you like me to map this into a sample conversation flow chart + decision tree (so your developers can immediately build intents and entities), or should I keep it strictly in text guideline format?
