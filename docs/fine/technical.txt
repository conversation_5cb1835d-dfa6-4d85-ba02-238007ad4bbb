Awesome — here’s a ready-to-paste starter set of intents, entities, training phrases, responses, and webhook contracts for a WhatsApp-first bot. I’m giving you Dialogflow CX samples (primary) and Rasa equivalents (optional) so your team can pick a stack and ship fast.

⸻

1) Shared ontology (entities & slots)

Entities
	•	@retailer = { game, makro, evetech, checkers, takealot, … }
	•	@city_zw = { harare, bulawayo, gweru, mutare, kwekwe, … }
	•	@payment_method = { ecocash, bank transfer, cash, swipe, zipit }
	•	@confirm_bool = { yes, yeah, yebo, ehe, okay, proceed, no, not now, later }
	•	@doc_type = { catalog, brochure, flyer, screenshot, link, url }
	•	@currency = { zar, rand, r, usd, $ }
	•	@number (sys number)
	•	@email (sys email)
	•	@phone (sys phone)

Slots you’ll store
	•	product_name (text)
	•	product_price_zar (number)
	•	retailer (enum)
	•	catalog_reference (url or image id)
	•	city (enum)
	•	landing_cost_usd (number; computed)
	•	customer_name, contact_primary, contact_alt, address, email

⸻

2) Dialogflow CX: intents (training phrases + responses)

Tip: Create a pricing webhook (/pricing/quote) and a lead webhook (/leads/create). Set end-session = false until handover.

I. greeting.welcome

Training phrases
	•	hi / hello / morning / afternoon
	•	I want to buy something from SA
	•	Can I make a purchase?
	•	I saw your ad on Instagram
	•	Need a quote
	•	How do you work?

Agent response
	•	“👋 Hi! Please share the product you want a quote for — you can send a link, name, or screenshot from Game/Makro/Evetech etc.”

⸻

II. product_inquiry.text

Training phrases
	•	Defy 30L Solo Microwave
	•	Samsung 55 inch Crystal UHD
	•	Gamdias AURA GC9M case white
	•	I want that Hisense bar in the flyer
	•	A Whirlpool 13kg top loader price?

Parameters extracted
	•	product_name (entire text)
	•	optional retailer if mentioned

Agent response
	•	“Got it — $session.params.product_name. If you have a price or link, please share it. Otherwise I’ll try to find it from the image or site.”

⸻

III. product_inquiry.link

Training phrases
	•	(paste URL)
	•	Here’s the link
	•	https://www.makro.co.za/…
	•	https://www.evetech.co.za/…

Parameters
	•	catalog_reference (url)
	•	optional retailer

Agent actions
	•	Call webhook /content/parse to scrape: {product_name, price_zar, retailer}.

Agent response
	•	Success: “✅ I found $product_name for R$price_zar at $retailer. Want me to calculate landing cost in Harare?”
	•	Fail: “I couldn’t read a price from that page. Can you send the price shown or a screenshot with the price visible?”

⸻

IV. product_inquiry.image

Training phrases
	•	(image uploaded)
	•	here’s the screenshot
	•	from the catalog

Agent actions
	•	Run OCR (/content/ocr) → attempt to extract product_name, price_zar, retailer.

Agent response
	•	If confident: “✅ Looks like $product_name priced R$price_zar. Shall I calculate landing cost?”
	•	If not confident: “I see the flyer — please circle or describe which item and, if possible, share the price shown.”

⸻

V. price_quote.request

Training phrases
	•	Quote please
	•	How much to land in Harare?
	•	What’s the landing cost?
	•	Total including shipping?
	•	Convert R1399 to landing price
	•	Price in USD delivered

Webhook
POST /pricing/quote

{
  "product_name":"Defy 30L Solo Microwave",
  "price_zar":1399,
  "retailer":"Game",
  "city":"Harare"
}

Webhook response (to show user)

Landing cost (Harare): **$123**
Breakdown: Product R1399 → $75 (rate $1:R18.60)
+ Markup 50%: $37.50
+ Shipping/Handling: $10.50
= **$123** (all-in to Harare)

(Show “rates & fees auto-updated today” if you’re pulling FX daily.)

Follow-up prompt
	•	“Would you like to proceed with this order? 😊”

⸻

VI. faq.landing_cost

Training phrases
	•	What does landing cost mean?
	•	Including shipping right?
	•	Is delivery included?

Agent response
	•	“Landing cost is the final price you pay in Harare, including the product and our shipping/handling. Local last-mile delivery (if outside pickup) can be arranged separately.”

⸻

VII. faq.payment_options

Training phrases
	•	How do I pay?
	•	Can I pay half?
	•	Ecocash available?
	•	Do you accept bank transfer?

Agent response
	•	“We accept Ecocash, bank transfer, or cash. A 50% deposit secures the order, with the balance on pickup/delivery. I can prepare your order details now.”

⸻

VIII. order.confirm_intent

Training phrases
	•	Yes proceed
	•	I want it
	•	Let’s do it
	•	Okay quote is fine
	•	Go ahead

Agent response
	•	“Great! To confirm your order, kindly provide:
• Name
• Primary Contact
• Alternative Contact (optional)
• Address
• Email (optional)”

⸻

IX. order.capture_details

Training phrases
	•	John Moyo, 0772…, 41 Msasa Park, john@…
	•	Name Charity Riyete; alt 0772…; 4141 Msasa Park; email …
	•	(any message containing name + phone/address)

Parameters
	•	customer_name (@sys.person or free text)
	•	contact_primary (@phone)
	•	contact_alt (@phone, optional)
	•	address (free text)
	•	email (@email, optional)

Agent actions
	•	Call /leads/create with all session params.
	•	Tag conversation HOT_LEAD.

Agent response
	•	“✅ Thanks, I’ve saved your order details. A human agent will share payment instructions and final confirmation shortly. Anything else meanwhile?”

⸻

X. payment.intent_to_pay

Training phrases
	•	Let’s do Ecocash
	•	Bank transfer details please
	•	I will pay at your office
	•	Can I get the number?

Action
	•	Trigger human handover (agent assist panel ping + CRM task).

Agent response
	•	“I’m connecting you to an agent now for payment instructions and POP verification 👍.”

⸻

XI. shipping.fee_question_only

Training phrases
	•	How much is shipping only?
	•	Cost to deliver only?
	•	Just shipping Harare?

Agent response
	•	If the user referenced a cart total:
“From your cart total R$number, shipping to Harare is $X. The landing cost (product + shipping) would be $Y. Would you like the combined figure?”

⸻

XII. fallback.clarify

Training phrases
	•	(ambiguous or noise)

Agent response
	•	“I didn’t quite get that. Please share a link, product name, or screenshot of the item, and I’ll quote the landing cost.”

⸻

3) Dialogflow CX: route settings (suggested)
	•	Entry page → detect greeting.welcome.
	•	Pages: ProductCapture → Quote → Decision → OrderDetails → Handover.
	•	Form parameters on OrderDetails page:
	•	customer_name (required)
	•	contact_primary (required)
	•	address (required)
	•	contact_alt/email (optional)
	•	Handover page → custom event to your Agent Assist or CRM.

⸻

4) Webhook contracts (pricing & lead)

/pricing/quote (POST)

Request

{
  "product_name": "Defy 30L Solo Microwave",
  "price_zar": 1399,
  "retailer": "Game",
  "city": "Harare",
  "session_id": "wa-263779..."
}

Pricing formula (server-side)

usd = price_zar / fx_rate;             // fx_rate e.g., 18.60
markup = usd * markup_pct;              // e.g., 0.50
shipping = base_ship + (usd * ship_pct); // e.g., $6 + 0.06*usd
landing = round2(usd + markup + shipping);

Return breakdown and human-readable string.

Response

{
  "landing_cost_usd": 123.00,
  "fx_rate": 18.60,
  "markup_pct": 0.50,
  "shipping_usd": 10.5,
  "breakdown_text": "Product R1399 → $75 @18.60 + Markup 50% $37.5 + Shipping $10.5 = $123"
}


⸻

/leads/create (POST)

Request

{
  "session_id": "wa-263779...",
  "customer": {
    "name": "Charity Riyete",
    "contact_primary": "+26377...",
    "contact_alt": "+26377...",
    "address": "4141 Msasa Park",
    "email": "<EMAIL>"
  },
  "product": {
    "name": "Defy 30L Solo Microwave",
    "retailer": "Game",
    "price_zar": 1399,
    "landing_cost_usd": 123
  },
  "source": "Instagram Ad",
  "city": "Harare",
  "status": "HOT_LEAD"
}

Response

{ "lead_id": "YZ-2025-000183", "next_action": "HUMAN_HANDOVER" }


⸻

5) Rasa equivalent (optional quick start)

nlu.yml (snippet)

version: "3.1"
nlu:
- intent: greeting
  examples: |
    - hi
    - hello
    - can I make a purchase?
    - I saw your ad
    - need a quote

- intent: product_inquiry_text
  examples: |
    - Defy 30L Solo Microwave
    - Samsung 55 inch Crystal UHD
    - Gamdias AURA GC9M case white

- intent: product_inquiry_link
  examples: |
    - here is the link
    - https://www.makro.co.za/...
    - https://www.evetech.co.za/...

- intent: price_quote
  examples: |
    - quote please
    - what's the landing cost?
    - total including shipping
    - convert R1399 to landing price

- intent: faq_landing_cost
  examples: |
    - what does landing cost mean?
    - is shipping included?

- intent: payment_options
  examples: |
    - how do I pay?
    - can I pay half?
    - ecocash available?

- intent: order_confirm
  examples: |
    - yes proceed
    - I want it
    - let's do it

- intent: order_details
  examples: |
    - John Moyo 0772... 41 Msasa Park john@...
    - Name Charity Riyete; alt 0772...; 4141 Msasa Park; email ...

- intent: shipping_only
  examples: |
    - how much is shipping only?
    - cost to deliver only?

domain.yml (snippet)

slots:
  product_name: { type: text, influence_conversation: true }
  product_price_zar: { type: float }
  retailer: { type: text }
  city: { type: text, initial_value: "Harare" }
  landing_cost_usd: { type: float }
  customer_name: { type: text }
  contact_primary: { type: text }
  contact_alt: { type: text }
  address: { type: text }
  email: { type: text }

responses:
  utter_greet:
    - text: "Hi! Share a link, name, or screenshot of the product for a quote."
  utter_explain_landing_cost:
    - text: "Landing cost = final price you pay in Harare (product + shipping/handling)."
  utter_payment_options:
    - text: "We accept Ecocash, bank transfer, or cash. 50% deposit secures the order."

actions:
  - action_parse_link_or_image
  - action_quote_price
  - action_create_lead
  - action_handover_human

stories.yml (snippet)

- story: quote and confirm
  steps:
    - intent: greeting
    - action: utter_greet
    - intent: product_inquiry_link
    - action: action_parse_link_or_image
    - intent: price_quote
    - action: action_quote_price
    - intent: order_confirm
    - action: utter_ask_order_details
    - intent: order_details
    - action: action_create_lead
    - action: action_handover_human

custom actions (pseudocode)

class ActionQuotePrice(Action):
    def run(...):
        fx = get_fx_rate()          # cache hourly
        usd = price_zar / fx
        landing = round(usd + usd*0.5 + (6 + usd*0.06), 2)
        dispatcher.utter_message(text=f"Landing cost (Harare): ${landing}")
        return [SlotSet("landing_cost_usd", landing)]


⸻

6) OCR & scraping heuristics
	•	Prefer retailer PDP pages (Makro/Game/Evetech).
	•	From images: find tokens like R[0-9,]+ near brand keywords (defy, samsung, hisense, gamdias).
	•	If multiple prices, pick the one adjacent to the product name box.
	•	Confidence < 0.7 → ask user to confirm price or resend a clearer shot.

⸻

7) Human handover rubric
	•	Trigger handover when:
	•	User says let’s pay, send number, bank details, Ecocash.
	•	Order details captured.
	•	Bulk order / fragile / >$700 item.
	•	Push a lead card to CRM/Telegram/Slack:
	•	Customer, product, retailer, ZAR price, landing USD, channel, transcript link.

⸻

8) Guardrails & edge cases
	•	If FX missing: “I can estimate now or wait for today’s rate update; estimate shows ±3% tolerance.”
	•	If item is out-of-stock or price changed by scraper: “The site shows a different price now (Rxxxx). Re-quote?”
	•	If user asks shipping only: support cart screenshots where Total is visible; compute % shipping of basket or fixed table by weight/category.

⸻

9) QA checklist (before launch)
	•	✅ ZAR→USD rate refreshed and cached.
	•	✅ Markup % and shipping logic are configurable per category/retailer.
	•	✅ WhatsApp media OCR working for common flyers.
	•	✅ Conversation transcripts saved against lead records.
	•	✅ Handover notifies an agent within 30s (push + email fallback).
	•	✅ PII masked in logs.

⸻

This gives your team everything to scaffold the bot, wire the pricing engine, and start training. Want me to package these as Dialogflow CX JSON exports or a Rasa project skeleton next?
