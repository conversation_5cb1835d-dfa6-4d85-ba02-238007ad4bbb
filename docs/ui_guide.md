# Youzeafrika — Product Specification & Architecture

**Version:** 1.1\
**Date:** 29 Aug 2025 (Africa/Harare)\
**Owner:** Youzeafrika (Zimbabwe)

---

## 1) Executive Summary

Youzeafrika enables Zimbabwean shoppers to browse South African store catalogues (Makro, Game, etc.), request pricing via a WhatsApp AI assistant (text or image), build a cart, and submit an **Order Request**. The **Landing Page** is a crucial entry point that combines catalog browsing, AI chat engagement, and WhatsApp integration.

---

## 2) Landing Page Objectives

- Provide a **first impression** that conveys trust, simplicity, and cross‑border shopping ease.
- Enable **catalog browsing** via carousels (images, PDFs, or shop categories).
- Offer **dual engagement channels**: WhatsApp handoff OR in‑page Chat Widget.
- Allow shoppers to **generate sharable links/URIs** referencing catalog documents or specific products.

---

## 3) Landing Page Layout Specification

### 3.1 Hero Section

- Large hero banner with tagline: *“Shop South Africa from Zimbabwe, hassle‑free.”*
- Call‑to‑Actions (CTAs):
  - **Browse Catalogs**
  - **Chat for Pricing**
  - **Track My Order**

### 3.2 Catalog Carousel Section

- Horizontal scrollable carousel of **catalog thumbnails** (PDF or image uploads).
- Each catalog card:
  - Cover image or preview page.
  - Shop logo (Makro, Game, etc.).
  - Quick actions:
    - **Open PDF Viewer** (modal or new tab)
    - **Copy Share Link** (URI generator)
    - **Send to Chat** (auto‑attach in widget)

### 3.3 Featured Products Section

- Dynamic grid of **top requested products**.
- Product cards: image, title, ZAR price, quick “Get Quote” button.

### 3.4 Chat Widget (Embedded Assistant)

- Floating **chat bubble** bottom‑right.
- Opens panel with:
  - Welcome message: “Ask me for any product. I can price it for you in USD.”
  - Input box (text + image upload).
  - Pre‑built quick replies (e.g., “Show me TVs”, “Upload Catalog Page”).
- Same backend as WhatsApp bot → quote, cart, order creation.
- Sync: user can seamlessly switch between **web chat** and **WhatsApp** (conversation linked via phone number or session token).

### 3.5 Call‑to‑Action Section

- Banner: “Ready to place an order?” → **Open Chat** or **Request a Quote**.

### 3.6 Footer

- Company info, T&Cs, Privacy, Contact, WhatsApp CTA.

---

## 4) UI Design Specs

### 4.1 Design Principles

- **Clean, premium, mobile‑first**.
- Use **Youzeafrika brand palette** (e.g., navy, gold, white).
- Consistent card styles with rounded corners, subtle shadows.
- Motion: smooth carousel scroll, fade‑in modals.

### 4.2 Components

**CatalogCarousel.vue**

- Props: `catalogs: {id, title, type[pdf|image], coverUrl, shop}`
- Actions: `onOpen`, `onCopyLink`, `onSendToChat`

**PdfViewerModal.vue**

- Embedded PDF.js viewer with “Add Page to Chat” button.

**ChatWidget.vue**

- States: `collapsed`, `expanded`.
- Messages: threaded cards (pricing breakdown, images, product cards).
- Input: text, emoji, image upload.

**ProductCard.vue**

- Props: `title, image, shop, priceZar`
- CTA: “Get Quote” → triggers Pricing API.

### 4.3 Example Wireframe Flow

```
[Hero Banner]
   CTA: Browse Catalogs | Chat for Pricing | Track Order

[Catalog Carousel]
   [Makro PDF]  [Game Image Catalog]  [Shoprite PDF]

[Featured Products]
   [Hisense TV] [Defy Fridge] [HP Laptop]

[Floating Chat Bubble]
   -> Chat Window (text + image + quick replies)

[Footer]
   Company info | WhatsApp CTA | Social
```

---

## 5) Catalog Browsing Workflow

1. User lands on page → sees **carousel of catalogs**.
2. Clicks catalog → opens **viewer** or generates **URI**.
3. Optionally: **Attach to Chat** (embedding catalog reference into AI conversation).
4. AI assistant → finds product(s) in referenced doc → prices it.

---

## 6) Chat Widget Integration

- Same backend endpoints as WhatsApp assistant.
- Session linked by cookie/sessionId; if phone number provided, conversation syncs with WhatsApp.
- User can:
  - Paste catalog link (auto‑parsed).
  - Upload image → AI matches product.
  - Ask free‑form question → NLU resolves intent.
  - Receive **pricing breakdown** cards with “Add to Cart” option.

---

## 7) Technical Implementation (Frontend)

- **Vue 3 + Vite + TailwindCSS**
- State: Pinia stores for catalogs, chat, cart.
- Routing: `/` (Landing), `/catalog/:id`, `/viewer/:id?page=3`, `/cart`.
- External libs:
  - PDF.js (PDF rendering)
  - Swiper.js (carousel)
  - Socket.io or Laravel Echo for chat sync

---

## 8) Acceptance Criteria (Landing Page)

- Landing page loads in <2s on mobile.
- Carousel displays at least 5 catalogs, swipeable.
- Clicking catalog → opens viewer or generates URI.
- Chat widget supports:
  - Text inquiries
  - Image uploads
  - Catalog references
  - Pricing cards with CTA
- Chat session links to WhatsApp conversation.



---

## 21) Landing Page & UI Design Specs (Vue 3)

### 21.1 Objectives

- Showcase shops and catalogues (PDFs/Images) with **carousel browsing**.
- Let users **open a document**, **extract products/pages**, and **send to chat** or **generate a shareable URI** for WhatsApp.
- Provide an embedded **Chat Widget** that mirrors WhatsApp flows (inquiry → pricing → add to cart → order request).
- Keep it fast (LCP < 2.5s on 4G), accessible (WCAG 2.1 AA), SEO‑friendly.

### 21.2 Information Architecture

- **Route /** → Landing (hero, shops, featured catalogues, chat widget, FAQs, trust signals)
- **Route /catalogues** → All catalogues w/ filters
- **Route /catalogues/****:id** → Catalogue Viewer (PDF/Image carousel, page picker, “Send to Chat”)
- **Route /products** → Search results (PLP)
- **Global**: **Cart Drawer** (sticky), **Chat Drawer** (right side), **Share Link modal**

### 21.3 Layout – Landing Page Wireframe

1. **Top Nav**: Logo (left) | Shops, Catalogues, How it works, Track Order | CTA: **Open Chat** (right)
2. **Hero Section** (two‑column):
   - Left: headline (“Buy from SA. Get a Zim price, instantly.”), subcopy, **Search bar** (with voice & image), secondary CTAs: *Browse Catalogues*, *Open Chat*.
   - Right: **Featured Catalogue Carousel** (PDF/Image cards) + **Quick Quote Card** (mini pricing demo). Lazy‑loads.
3. **Shops Row**: Grid of Makro, Game, etc. Clicking filters catalogues & products.
4. **Catalogue Explorer** (carousel): horizontally scrollable categories; each item opens **Catalogue Viewer**.
5. **How It Works**: 3 steps cards (Find → Get Price → Request Order).
6. **Embedded Chat Widget**: docked panel (right) with floating button. Mirrors WhatsApp flows.
7. **Trust Section**: SLA bullets, FX disclosure, logistics partners, customer badges.
8. **Footer**: links, contact, WhatsApp deep link, legal.

### 21.4 Components (Vue SFCs)

- `AppNavbar.vue`: props `{ user }`; emits `open-chat`, `open-cart`.
- `HeroSearch.vue`: props `{ placeholder }`; slots: `actions`; events: `submit(query)`, `upload(image)`.
- `CatalogueCarousel.vue`: props `{ catalogues: CatalogueDoc[], title }`; emits `open(doc)`, `share(doc)`, `sendToChat(doc)`.
- `CatalogueCard.vue`: shows preview (PDF cover or first image), meta (shop, pages, updated\_at), actions (Open, Send to Chat, Share URI).
- `CatalogueViewer.vue` (route view):
  - **PDF mode**: page thumbnails left, main canvas center (PDF.js), actions: *Send page to chat*, *Extract items*, *Share page URI*.
  - **Image mode**: horizontal image carousel with pinch‑zoom; same actions.
- `ChatWidget.vue`: docked drawer. Tabs: **Chat**, **Cart**, **Quotes**. Input supports text, image upload, and **Attach Catalogue Page**.
- `QuoteCard.vue`: shows breakdown + **Add to Cart**.
- `CartDrawer.vue`: line items (variant, qty, quote\_id), totals, **Request Order**.
- `ShareLinkModal.vue`: shows generated deep link, copy button, QR.

### 21.5 Data Contracts (Frontend)

```ts
// Catalogue document (PDF or image set)
export interface CatalogueDoc {
  id: number;
  shop_slug: string;       // 'makro'
  title: string;
  type: 'pdf' | 'images';
  cover_url: string;
  file_url?: string;       // pdf url
  images?: string[];       // for image set
  page_count?: number;
  updated_at: string;      // ISO
}

// Shareable reference to a specific page/region
export interface DocRef {
  doc_id: number;
  page?: number;           // for PDF
  image_idx?: number;      // for images
  region?: {x:number;y:number;w:number;h:number}; // optional crop
  token: string;           // short, signed token
  url: string;             // generated public URI
}

// Chat message payloads
export type ChatMessage =
  | { type:'text'; text:string }
  | { type:'image'; url:string }
  | { type:'doc_ref'; ref: DocRef }
  | { type:'product_card'; product_id:number }
  | { type:'quote_card'; quote_id:number };
```

### 21.6 Backend Endpoints (new)

- **GET /catalogues** → list with filters `?shop=makro&type=pdf|images&query=…`
- **GET /catalogues/{id}** → detail including page thumbnails/first images.
- **POST /catalogues/{id}/share** `{ page?, image_idx?, region? }` → `{ doc_ref }`
- **POST /chat/send** `{ conversation_id?, message: ChatMessage }` → 200
- **POST /chat/quote** `{ variant_id, qty, doc_ref? }` → returns QuoteCard data
- **GET /share/{token}** → 302 redirect to canonical landing / viewer + loads `DocRef`

### 21.7 Interactions & Flows

**A) Send Catalogue Page to Chat**

1. User opens `CatalogueViewer` → selects page 8 → clicks **Send to Chat**.
2. FE calls `/catalogues/:id/share` → gets `doc_ref` with URL.
3. FE sends chat message `{ type:'doc_ref', ref }` via `/chat/send`.
4. Bot/agent receives reference; AI uses page image to detect likely products → replies with candidates + **Get Price** CTAs.

**B) Generate WhatsApp URI**

- FE constructs `https://wa.me/<number>?text=${encodeURIComponent("Check this: "+doc_ref.url)}` and shows in `ShareLinkModal` with QR.

**C) Embedded Chat → Pricing → Cart**

1. User: “Price this Hisense 40" from page 8.” (has attached `DocRef`).
2. Bot resolves product candidates; on selection calls `/pricing/quote`.
3. Returns `QuoteCard` → user taps **Add to Cart** → cart drawer updates.

### 21.8 Catalogue Viewer Details

- **PDF**: Use PDF.js worker, render page at DPR‑aware scale, generate page thumbnails (lazy). Support text selection & rectangular crop (for specific product regions). **Keyboard**: ← → page nav.
- **Images**: Use virtualized carousel, lazy‑load, pinch‑zoom on mobile, double‑tap to zoom.
- **Toolbar**: Open in new tab, Download (if allowed), Send to Chat, Share, Extract items (experimental vision).

### 21.9 Chat Widget Details

- **Placement**: floating circular button bottom‑right → expands to 360–420px drawer; full‑height on mobile.
- **Input**: text (enter to send), file/image upl
