# cPanel Setup Guide for Youzeafrika

## 🚀 Quick Setup for cPanel Hosting

### 1. **Upload Files**
- Upload all project files to your cPanel public_html directory
- Ensure the `cron/` directory is readable

### 2. **Set Up Cron Jobs in cPanel**

Go to **c<PERSON>anel → Cron Jobs** and add these entries:

#### Option A: Using PHP Scripts (Recommended)
```bash
# Laravel Scheduler - Every Minute
* * * * * /usr/local/bin/php /home/<USER>/public_html/cron/scheduler.php >/dev/null 2>&1

# Queue Worker - Every 5 Minutes
*/5 * * * * /usr/local/bin/php /home/<USER>/public_html/cron/queue-worker.php >/dev/null 2>&1

# System Health Check - Every 30 Minutes
*/30 * * * * /usr/local/bin/php /home/<USER>/public_html/cron/system-health.php >/dev/null 2>&1
```

#### Option B: Using wget/curl Routes (Alternative)
```bash
# Laravel Scheduler - Every Minute
* * * * * /usr/bin/wget -q -O /dev/null "https://yourdomain.com/cron/schedule"

# Process Notifications - Every 5 Minutes
*/5 * * * * /usr/bin/wget -q -O /dev/null "https://yourdomain.com/cron/notifications"

# Process Queue - Every 5 Minutes
*/5 * * * * /usr/bin/wget -q -O /dev/null "https://yourdomain.com/cron/queue"

# Qualify Leads - Every Hour
0 * * * * /usr/bin/wget -q -O /dev/null "https://yourdomain.com/cron/qualify-leads"

# System Health - Every 30 Minutes
*/30 * * * * /usr/bin/wget -q -O /dev/null "https://yourdomain.com/cron/health"
```

### 3. **Important Notes**
- Replace `/home/<USER>/public_html` with your actual path
- Replace `yourdomain.com` with your actual domain
- PHP path might be `/usr/bin/php` or `/usr/local/bin/php84` - check with your host
- The first setup uses less server resources

### 4. **Testing Routes**
Visit these URLs to test functionality:

- **System Health**: `https://yourdomain.com/cron/health`
- **System Stats**: `https://yourdomain.com/cron/stats`
- **Process Notifications**: `https://yourdomain.com/cron/notifications`
- **Qualify Leads**: `https://yourdomain.com/cron/qualify-leads`

### 5. **Monitoring**
- Check cPanel Error Logs for any issues
- Visit `/cron/stats` to see system statistics
- Laravel logs are in `storage/logs/laravel.log`

### 6. **Queue Management**
The system uses database queues (no Redis needed):
- Jobs are stored in the `jobs` table
- Failed jobs go to `failed_jobs` table
- Queue workers process jobs automatically via cron

### 7. **File Permissions**
Make sure these have correct permissions:
```bash
chmod 755 cron/
chmod 644 cron/*.php
chmod -R 775 storage/
chmod -R 775 bootstrap/cache/
```

### 8. **Security**
- The `/cron/*` routes are throttled (60 requests per minute)
- Consider adding IP restrictions for production
- Monitor the logs for any unauthorized access

## 🎯 What Each Cron Job Does

| Job | Frequency | Purpose |
|-----|-----------|---------|
| `scheduler.php` | Every minute | Runs Laravel's task scheduler |
| `queue-worker.php` | Every 5 minutes | Processes background jobs |
| `system-health.php` | Every 30 minutes | Monitors system health |

## 📊 Available Routes

| Route | Purpose | Response |
|-------|---------|----------|
| `/cron/health` | System health check | JSON health status |
| `/cron/stats` | Business statistics | JSON stats dashboard |
| `/cron/schedule` | Run scheduler | JSON execution result |
| `/cron/notifications` | Process notifications | JSON processing result |
| `/cron/qualify-leads` | Auto-qualify leads | JSON qualification result |
| `/cron/queue` | Process queue jobs | JSON processing result |

## 🚨 Troubleshooting

1. **Cron jobs not running?**
   - Check cPanel cron job syntax
   - Verify PHP path with your hosting provider
   - Check cPanel error logs

2. **Database connection errors?**
   - Verify `.env` database settings
   - Check database user permissions

3. **Queue jobs not processing?**
   - Verify `jobs` table exists
   - Check queue worker cron job is running
   - Visit `/cron/queue` manually to test

4. **High memory usage?**
   - Reduce max-jobs in queue-worker.php
   - Increase processing frequency instead

This setup ensures your Youzeafrika application runs smoothly on cPanel hosting! 🎉