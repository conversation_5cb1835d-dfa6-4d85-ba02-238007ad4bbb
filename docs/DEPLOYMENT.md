# Production Deployment Guide for youzeafrika.com

## Pre-deployment Checklist

✅ **.env file updated** for production with:
- `APP_URL=https://youzeafrika.com`
- `APP_ENV=production`
- `APP_DEBUG=false`
- `LOG_LEVEL=error`
- `SESSION_DOMAIN=youzeafrika.com`
- `CACHE_STORE=redis`

⚠️ **Update these production values before deployment:**
- `DB_HOST`, `DB_DATABASE`, `DB_USERNAME`, `DB_PASSWORD`
- `MAIL_HOST`, `MAIL_USERNAME`, `MAIL_PASSWORD`

✅ **Production dependencies installed** (`composer install --optimize-autoloader --no-dev`)

## Deployment Steps

### 1. Upload Files
Upload all files to your production server except:
- `node_modules/`
- `.git/`
- `tests/`
- `storage/logs/*`

### 2. Set Directory Permissions
```bash
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache
```

### 3. Update .env for Production
Replace placeholder values in `.env`:
```env
# Database
DB_HOST=your_production_db_host
DB_DATABASE=your_production_db_name
DB_USERNAME=your_production_db_user
DB_PASSWORD=your_secure_db_password

# Email
MAIL_HOST=your_smtp_host
MAIL_USERNAME=your_email_username
MAIL_PASSWORD=your_email_password
```

### 4. Install Dependencies & Build Assets
```bash
# Install PHP dependencies
composer install --optimize-autoloader --no-dev

# Install Node dependencies
npm install

# Generate wayfinder types (requires DB connection)
php artisan wayfinder:generate --with-form

# Build production assets
npm run build

# Clear and cache for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 5. Database Setup
```bash
# Run migrations
php artisan migrate --force

# Seed data if needed
php artisan db:seed --force
```

### 6. Storage Linking
```bash
php artisan storage:link
```

### 7. Set Up Queue Worker (Production)
Add to crontab or supervisor:
```bash
php artisan queue:work --daemon --tries=3 --timeout=60
```

### 8. Configure Web Server

#### Apache (.htaccess should work automatically)
Ensure `public/` is your document root.

#### Nginx
```nginx
server {
    listen 80;
    server_name youzeafrika.com;
    root /path/to/your/app/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

## Production Security Notes

1. **SSL Certificate**: Ensure HTTPS is properly configured
2. **Environment**: Double-check all `.env` values are production-ready
3. **File Permissions**: Verify storage and cache directories are writable
4. **Database**: Use strong passwords and restricted user permissions
5. **Firewall**: Only allow necessary ports (80, 443, SSH)

## Post-Deployment Testing

1. Test admin login at `https://youzeafrika.com/admin`
2. Verify catalogue images load properly with CORS headers
3. Test file uploads functionality
4. Check queue processing for AI catalogue ingestion
5. Verify email functionality

## Rollback Plan

Keep a backup of:
- Previous codebase
- Database backup
- `.env` file backup

## Troubleshooting

- **Build errors**: Ensure database connection is available during `npm run build`
- **File upload issues**: Check storage permissions and symlink
- **Queue not processing**: Verify queue worker is running
- **CORS errors**: Confirm routes are properly cached with `php artisan route:cache`