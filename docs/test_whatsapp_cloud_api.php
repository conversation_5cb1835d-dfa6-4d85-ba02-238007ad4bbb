<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

// Test WhatsApp Cloud API integration
class WhatsAppCloudAPITest
{
    private string $accessToken;
    private string $phoneNumberId;
    private string $baseUrl;

    public function __construct()
    {
        $this->accessToken = 'EAAVo0P2uXRwBPRMhroTadEx7efRu2M88FKQYVMwuLNXZB8UVEi7PvjeSiWfdEyB9OywUdxsZCltTVYmNyZB0oyup5wRYUzMCSOSjCP92JQD8od95G4tvI1ZBnWTDXJNZAK8yroRDj7TjFclEbtXEouKZCVBIrp7wdRhYUOlI0v5DCoQZDZD';
        $this->phoneNumberId = '785599544640214';
        $this->baseUrl = "https://graph.facebook.com/v22.0/{$this->phoneNumberId}";
    }

    public function testTextMessage(): array
    {
        echo "Testing text message...\n";

        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => '263775240298', // Your test number
            'type' => 'text',
            'text' => [
                'body' => 'Hello from WhatsApp Cloud API! This is a test message from Youzeafrika. 🇿🇼'
            ]
        ];

        return $this->sendRequest($payload);
    }

    public function testTemplateMessage(): array
    {
        echo "Testing template message...\n";

        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => '263775240298', // Your test number
            'type' => 'template',
            'template' => [
                'name' => 'hello_world',
                'language' => [
                    'code' => 'en_US'
                ]
            ]
        ];

        return $this->sendRequest($payload);
    }

    private function sendRequest(array $payload): array
    {
        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $this->baseUrl . '/messages',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($payload),
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $this->accessToken,
                'Content-Type: application/json',
            ],
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return [
                'success' => false,
                'error' => $error,
                'response' => null
            ];
        }

        $responseData = json_decode($response, true);

        echo "HTTP Code: {$httpCode}\n";
        echo "Response: " . json_encode($responseData, JSON_PRETTY_PRINT) . "\n\n";

        return [
            'success' => $httpCode === 200,
            'http_code' => $httpCode,
            'response' => $responseData
        ];
    }

    public function runAllTests(): void
    {
        echo "Starting WhatsApp Cloud API tests...\n\n";

        // Test 1: Template message
        $templateResult = $this->testTemplateMessage();
        if ($templateResult['success']) {
            echo "✅ Template message test PASSED\n";
        } else {
            echo "❌ Template message test FAILED\n";
            echo "Error: " . json_encode($templateResult, JSON_PRETTY_PRINT) . "\n";
        }

        echo "\n" . str_repeat('-', 50) . "\n\n";

        // Test 2: Text message
        $textResult = $this->testTextMessage();
        if ($textResult['success']) {
            echo "✅ Text message test PASSED\n";
        } else {
            echo "❌ Text message test FAILED\n";
            echo "Error: " . json_encode($textResult, JSON_PRETTY_PRINT) . "\n";
        }

        echo "\n" . str_repeat('=', 50) . "\n";
        echo "Tests completed!\n";
        echo "Webhook URL for Meta setup: https://yourdomain.com/api/whatsapp/cloud-webhook\n";
        echo "Verify token: your_webhook_verify_token_here\n";
    }
}

// Run the tests
$tester = new WhatsAppCloudAPITest();
$tester->runAllTests();