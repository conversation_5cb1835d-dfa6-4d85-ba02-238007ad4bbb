# Public Folder Storage Setup for cPanel

## Overview
This application is configured to store all uploaded files directly in the `/public/uploads/` directory instead of using Laravel's default `/storage/` directory with symbolic links. This setup is perfect for cPanel hosting where symbolic links are often restricted.

## 🗂️ Directory Structure

```
public/
└── uploads/
    ├── catalogues/
    │   ├── pdf/           # PDF catalogue files
    │   ├── images/        # Image-based catalogue pages
    │   └── covers/        # Catalogue cover images
    ├── shops/
    │   └── logos/         # Shop logo images
    ├── products/          # Product images
    ├── temp/              # Temporary files (auto-cleaned)
    └── chat/              # Chat-related uploads
```

## 🔧 Key Components

### 1. FileUploadService
Located at: `app/Services/FileUploadService.php`

Main methods:
- `uploadToPublic()` - Upload files to public folder
- `deleteFromPublic()` - Delete files from public folder
- `getPublicUrl()` - Get full URL for public files
- `existsInPublic()` - Check if file exists
- `getFileSize()` - Get file size
- `ensureUploadDirectories()` - Create directory structure

### 2. Model Accessors
The `Catalogue` model automatically converts relative paths to full URLs:
- `cover_url` - Returns full URL for cover image
- `pdf_url` - Returns full URL for PDF file
- `images` - Returns array of full URLs for images

### 3. Controller Updates
All controllers now use `FileUploadService` instead of Laravel's `Storage` facade:
- `CatalogueController` - Handles catalogue uploads
- `ChatController` - Handles chat image uploads
- `ConversationService` - Handles WhatsApp media

## 📝 Usage Examples

### Uploading a File
```php
use App\Services\FileUploadService;

// Upload a file
$path = FileUploadService::uploadToPublic(
    $request->file('image'),
    'uploads/catalogues/covers',
    'my-cover.jpg'
);

// Get the full URL
$url = FileUploadService::getPublicUrl($path);
```

### Deleting a File
```php
// Delete a file
FileUploadService::deleteFromPublic('uploads/catalogues/covers/my-cover.jpg');
```

### In Models
```php
// The model accessors automatically handle URL generation
$catalogue = Catalogue::find(1);
echo $catalogue->cover_url; // Returns: https://yourdomain.com/uploads/catalogues/covers/image.jpg
```

## 🚀 Deployment to cPanel

1. **Upload all files** to your cPanel public_html directory
2. **No symbolic links needed** - files are stored directly in public folder
3. **Set permissions** on upload directories:
   ```bash
   chmod 755 public/uploads
   chmod -R 755 public/uploads/*
   ```

## 🔒 Security Considerations

1. **File Type Validation** - Always validate file types before upload
2. **Size Limits** - Enforce reasonable file size limits
3. **Filename Sanitization** - FileUploadService generates safe filenames
4. **Directory Traversal** - Paths are sanitized to prevent directory traversal attacks

## 🧹 Maintenance

### Clean Temporary Files
Add this to your Laravel scheduler (`app/Console/Kernel.php`):
```php
$schedule->call(function () {
    $tempDir = public_path('uploads/temp');
    $files = glob($tempDir . '/*');
    $now = time();
    
    foreach ($files as $file) {
        // Delete files older than 24 hours
        if (is_file($file) && ($now - filemtime($file) >= 86400)) {
            unlink($file);
        }
    }
})->daily();
```

## 🔄 Migration from Storage to Public

If you have existing files in `/storage/`, the migration `2025_09_02_update_catalogue_paths_to_public.php` will:
1. Update database paths from `/storage/` to `uploads/`
2. Maintain backward compatibility with existing URLs
3. Handle both old and new path formats

## ✅ Benefits of This Setup

1. **No Symbolic Links** - Works perfectly on restricted cPanel hosting
2. **Direct Access** - Files are directly accessible via web URLs
3. **Simplified Deployment** - No extra server configuration needed
4. **Better Performance** - Direct file serving without PHP overhead
5. **Easy Backup** - All uploads in one public folder

## 🎯 Best Practices

1. Always use `FileUploadService` for file operations
2. Store sensitive files outside public folder if needed
3. Implement proper access control for private files
4. Regular cleanup of temporary files
5. Monitor disk usage in upload directories

This setup ensures your application works seamlessly on cPanel hosting without any symbolic link issues!