# Simplified WhatsApp Bot Implementation

This implementation follows the guide.txt specifications for a simple, state machine-based WhatsApp bot that helps customers get USD delivered pricing from SA shops.

## State Machine Flow

The bot follows this exact flow as specified in guide.txt:

```
GREET → IDENTIFY → INTENT → CAPTURE_ITEM → ENRICH_ITEM → PRICE → CART_OR_ORDER → ORDER_CONFIRM → DONE
```

### State Descriptions

1. **GREET**: <PERSON><PERSON> sends friendly greeting
2. **IDENTIFY**: Ask for customer name if not known, greet by name if known
3. **INTENT**: Ask what they want to do (send product name, link, or photo)
4. **CAPTURE_ITEM**: Ingest text, link, or image from user
5. **ENRICH_ITEM**: Extract product name, outlet, price using AI/DB/scraping
6. **PRICE**: Calculate USD landed price with breakdown
7. **CART_OR_ORDER**: User chooses to add to cart or order now
8. **ORDER_CONFIRM**: Confirm order details and create order
9. **DONE**: Order placed with reference number

## Key Components

### 1. BotFlow Service (`app/Services/BotFlow.php`)
- Implements the state machine logic
- Static methods for each state transition
- Uses cache for state management
- Handles all conversation flow

### 2. PricingEngine Service (`app/Services/PricingEngine.php`)
- Implements guide.txt pricing formula:
  - `base = source_price * fx_rate`
  - `markup = base * markup_percent_by_outlet_or_category`
  - `delivery = delivery_table[city|weight_band]`
  - `payment_fee = (base + markup) * payment_rate`
  - `total_usd = round_up(base + markup + delivery + payment_fee + misc_duty, 0.50)`

### 3. SimpleBotController (`app/Http/Controllers/SimpleBotController.php`)
- Handles WhatsApp webhook messages
- Routes messages through the state machine
- Supports both Twilio and Meta WhatsApp Cloud API

### 4. Data Models
- **Customer**: Stores customer info (phone_e164, first_name, is_blocked)
- **IntakeItem**: One raw item per user submission (text/link/image)
- **Cart/CartItem**: Shopping cart functionality
- **Order**: Final orders with reference numbers
- **PricingRule**: Configurable pricing rules
- **DeliveryBand**: City-based delivery pricing
- **AuditLog**: Tracks all bot actions

## Webhook Endpoints

### Primary Endpoint
- `GET/POST /api/whatsapp/webhook` - Simplified bot (primary)

### Backup Endpoints
- `GET/POST /api/whatsapp/cloud-webhook` - Cloud API backup
- `POST /api/whatsapp/legacy-webhook` - Legacy Twilio
- `POST /api/whatsapp/ai-webhook` - Legacy AI-powered
- `POST /api/whatsapp/smart-webhook` - Legacy smart

## Configuration

### Environment Variables
```env
# WhatsApp Cloud API
WHATSAPP_ACCESS_TOKEN=your_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_verify_token

# Twilio (backup)
TWILIO_SID=your_sid
TWILIO_TOKEN=your_token
TWILIO_WHATSAPP_FROM=whatsapp:+1234567890

# Gemini AI
GEMINI_API_KEY=your_gemini_key
```

### Database Setup
```bash
php artisan migrate
php artisan db:seed --class=SimpleBotSeeder
```

## Testing

### Test Endpoints
- `GET /api/test/simple-bot/flow` - Test individual components
- `GET /api/test/simple-bot/conversation` - Simulate full conversation
- `POST /api/test/simple-bot/reset` - Reset test data

### Example Conversation Flow

1. **User**: "hi"
   - **Bot**: "Hi! I'm Youze Africa 🤖. I help you get USD delivered pricing from SA shops. What's your name?"

2. **User**: "Tapi"
   - **Bot**: "Thanks, Tapi. Send a product name, link, or photo (e.g., 'Defy 13-place dishwasher from Makro')."

3. **User**: "Samsung Galaxy A15 128GB from Game, R3,499"
   - **Bot**: "Delivered to Harare: $118.50 (FX 18.20, markup 25%, delivery $12). Add to cart or order now?"

4. **User**: "order now"
   - **Bot**: "Order: 1× Samsung Galaxy A15 (Game, R3499) → $118.50. Deliver to Harare. Confirm?"

5. **User**: "confirm"
   - **Bot**: "🎉 Order placed! Ref #YA-9K2Q. We'll follow up shortly."

## Key Features

### Simplified Architecture
- Removed complex AI conversation services
- Simple state machine with cache-based state storage
- Direct integration with existing Gemini and WhatsApp services
- Minimal dependencies

### Pricing Engine
- Data-driven pricing rules (no hardcoded logic)
- Configurable markups by outlet/category
- City-based delivery bands
- Automatic duty calculations
- Round-up to nearest $0.50

### Error Handling
- Graceful fallbacks for missing data
- User-friendly error messages
- Comprehensive logging
- Audit trail for all actions

### Extensibility
- Easy to add new outlets/categories
- Configurable pricing rules via database
- Support for multiple currencies
- Pluggable enrichment strategies

## Cleanup Completed

### Removed Complex Components
- ❌ AIConversationService, AIFlowManager, LeadScoringService
- ❌ Complex controllers (ConversationTestController, SmartChatController, etc.)
- ❌ Complex models (ConversationAnalytics, ConversationFlow, Lead, etc.)
- ❌ Complex migrations for analytics, intents, flows
- ❌ Complex routes and API endpoints
- ❌ LeadCaptureService, EscalationHandler, OCRService
- ❌ Complex ConversationService (replaced with simple legacy stub)

### Simplified Architecture
- ✅ Single state machine flow (BotFlow service)
- ✅ Cache-based state management
- ✅ Direct Gemini integration
- ✅ Simple text parsing for product info
- ✅ Basic cart functionality
- ✅ Straightforward order creation

### Kept Essential Services
- ✅ WhatsApp service (supports both Twilio & Meta APIs)
- ✅ Gemini service for AI processing
- ✅ Pricing service foundation
- ✅ Core database models (Customer, Product, Cart, Order)
- ✅ Webhook verification and processing

### Clean Codebase
The implementation now provides a clean, maintainable foundation that:
- Follows guide.txt specifications exactly
- Removes all unnecessary complexity
- Maintains backward compatibility where needed
- Uses existing infrastructure efficiently
- Is easy to understand and extend
