# Twilio WhatsApp Integration Setup

## Current Configuration

Your app is **TWILIO-ONLY** configured and ready to work exclusively with Twilio WhatsApp API.

### Environment Variables (Already Set)
```env
# Twilio WhatsApp Configuration (Primary)
TWILIO_SID=**********************************
TWILIO_TOKEN=a5ac8fc29bb36f3663b53bf391c4266f
TWILIO_WHATSAPP_FROM=whatsapp:+***********
TWILIO_SKIP_VERIFICATION=true
TWILIO_FORCE_MODE=true
```

## Webhook URL for Twilio Console

**Use your existing webhook URL - no changes needed:**

### Primary Webhook (Your Current URL)
```
https://yourdomain.com/api/whatsapp/webhook
```

**✅ This is the same URL you're already using - just reupload the app!**

## Setup Steps

**✅ NO CHANGES NEEDED** - Your webhook URL stays the same!

1. **Just reupload your app** with the updated code
2. **Your existing Twilio webhook configuration will work**
3. **Test by sending a message** to your Twilio WhatsApp number

### If you need to verify your Twilio settings:
1. **Login to Twilio Console**: https://console.twilio.com/
2. **Navigate to**: Messaging > Try it out > Send a WhatsApp message
3. **Verify webhook URL**: `https://yourdomain.com/api/whatsapp/webhook`
4. **Verify HTTP method**: `POST`

## How It Works

### Twilio-Only Mode
The app now works **exclusively with Twilio**:
- **Cloud API disabled**: No more dual API support
- **TwiML responses**: Returns proper XML for Twilio
- **Simplified processing**: Direct Twilio webhook handling

### Message Processing Flow
1. **Webhook Received** → `SimpleBotController@handle`
2. **Twilio Processing** → `WhatsAppService@processTwilioWebhook`
3. **Bot Processing** → `BotFlow` state machine
4. **Response Sent** → Via Twilio API
5. **TwiML Response** → Returns `<?xml version="1.0"?><Response></Response>`

### Supported Message Types
- ✅ **Text messages**
- ✅ **Images** (via MediaUrl0)
- ✅ **Audio** (via MediaUrl0)
- ✅ **Video** (via MediaUrl0)
- ✅ **Documents** (via MediaUrl0)

## Testing Endpoints

### Test Configuration
```bash
GET /api/test/twilio/config
```
Returns current Twilio configuration status.

### Test Sending Messages
```bash
POST /api/test/twilio/send
Content-Type: application/json

{
    "to": "+************",
    "message": "Test message from bot"
}
```

### Simulate Webhook
```bash
POST /api/test/twilio/webhook
Content-Type: application/json

{
    "message": "Hello bot"
}
```

### Test Complete Flow
```bash
POST /api/test/twilio/flow
Content-Type: application/json

{
    "from": "+************",
    "message": "hi"
}
```

## Force Twilio Mode

If you want to use only Twilio (disable Cloud API), add to `.env`:
```env
TWILIO_FORCE_MODE=true
```

This will force the app to use Twilio even if Cloud API credentials are present.

## Webhook Verification

- **Twilio**: No verification needed (TWILIO_SKIP_VERIFICATION=true)
- **Cloud API**: Requires hub verification token

The app handles both automatically.

## Message Format Examples

### Twilio Webhook Format (Incoming)
```json
{
    "MessageSid": "SM1234567890abcdef",
    "From": "whatsapp:+************",
    "To": "whatsapp:+***********",
    "Body": "Hello bot",
    "NumMedia": "0",
    "AccountSid": "**********************************"
}
```

### With Media
```json
{
    "MessageSid": "SM1234567890abcdef",
    "From": "whatsapp:+************",
    "To": "whatsapp:+***********",
    "Body": "",
    "NumMedia": "1",
    "MediaUrl0": "https://api.twilio.com/media/123",
    "MediaContentType0": "image/jpeg"
}
```

## Bot Conversation Flow

The bot follows the guide.txt flow:
1. **GREET** → "Hi! I'm Youze Afrika 🤖..."
2. **IDENTIFY** → Ask for customer name
3. **INTENT** → Ask what they want to do
4. **CAPTURE_ITEM** → Get product info
5. **ENRICH_ITEM** → Process with AI
6. **PRICE** → Calculate USD pricing
7. **CART_OR_ORDER** → Add to cart or order
8. **ORDER_CONFIRM** → Confirm order
9. **DONE** → Order placed

## Troubleshooting

### Check Logs
```bash
tail -f storage/logs/laravel.log | grep -i twilio
```

### Common Issues

1. **Webhook not received**
   - Check Twilio webhook URL is correct
   - Verify domain is accessible from internet
   - Check firewall settings

2. **Messages not sending**
   - Verify TWILIO_SID and TWILIO_TOKEN are correct
   - Check Twilio account balance
   - Verify WhatsApp number is approved

3. **Bot not responding**
   - Check if BotFlow service is working
   - Verify database connection
   - Check for PHP errors in logs

### Debug Mode
Add to `.env` for detailed logging:
```env
APP_DEBUG=true
LOG_LEVEL=debug
```

## Production Checklist

- [ ] Set correct webhook URL in Twilio Console
- [ ] Verify SSL certificate on domain
- [ ] Test message sending and receiving
- [ ] Check bot conversation flow
- [ ] Monitor logs for errors
- [ ] Set up proper error handling
- [ ] Configure rate limiting if needed

## Support

The app supports both Twilio and WhatsApp Cloud API simultaneously. It will automatically detect the webhook format and process accordingly. The simplified bot implementation ensures reliable message processing and follows the exact conversation flow specified in guide.txt.
