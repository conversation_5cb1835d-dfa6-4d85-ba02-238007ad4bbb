<?php

// Simple test script for WhatsApp service
require_once __DIR__ . '/vendor/autoload.php';

// Set up Laravel environment
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// Now we can use Laravel services
use App\Services\WhatsAppService;

try {
    $whatsappService = app(WhatsAppService::class);

    echo "WhatsApp Service Test\n";
    echo "====================\n";
    echo "API Type: " . $whatsappService->getApiType() . "\n\n";

    $testPhone = '263775240298';
    $testMessage = 'Hello! This is a test message from Youzeafrika WhatsApp integration. 🚀';

    echo "Sending test message to: {$testPhone}\n";
    echo "Message: {$testMessage}\n\n";

    $result = $whatsappService->sendMessage($testPhone, $testMessage);

    if ($result) {
        echo "✅ SUCCESS: Message sent successfully!\n";
    } else {
        echo "❌ FAILED: Could not send message\n";
    }

    // If using Cloud API, test template message
    if ($whatsappService->getApiType() === 'cloud_api') {
        echo "\nTesting template message...\n";
        $templateResult = $whatsappService->sendTemplateMessage($testPhone, 'hello_world');

        if ($templateResult) {
            echo "✅ SUCCESS: Template message sent!\n";
        } else {
            echo "❌ FAILED: Template message failed (template may not be approved)\n";
        }
    }

    echo "\nWebhook configuration:\n";
    echo "URL: " . config('app.url') . "/api/whatsapp/cloud-webhook\n";
    echo "Verify Token: " . config('whatsapp.webhook_verify_token') . "\n";

} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}